# 呼吸灯动画功能说明

## 功能概述

在原有的流光动画基础上，新增了"呼吸灯"动画效果，为主界面的输入框和输出框提供更多样化的视觉效果选择。

## 新增功能

### 🎨 动画类型选择
- **流光动画**: 原有的边框流光效果，光点沿边框移动
- **呼吸灯动画**: 新增的呼吸灯效果，整个边框透明度、宽度和大小周期性变化，营造真实的呼吸感

### ⚙️ 设置界面更新
1. **动画类型选择器**: 使用 FilterChip 组件，可在"流光"和"呼吸灯"之间切换
2. **智能设置显示**: 
   - 流光动画：显示方向、长度、速度设置
   - 呼吸灯动画：只显示速度设置（隐藏方向和长度设置）
3. **动态标签**: 速度设置标签根据动画类型变化
   - 流光动画：显示"流光速度"
   - 呼吸灯动画：显示"呼吸速度"

## 技术实现

### 📁 修改的文件

#### 1. AnimationSettings.kt
- 添加 `AnimationType` 枚举类型
- 新增动画类型相关的设置变量和存储键
- 更新 `load()` 和 `save()` 方法支持动画类型持久化

#### 2. DataInputScreen.kt
- 扩展 `animatedBorder()` 函数，添加 `animationType` 参数
- 实现呼吸灯动画逻辑：
  - 使用 `breathingAlpha` 控制透明度变化
  - 边框宽度随透明度动态调整
  - 采用 `RepeatMode.Reverse` 实现呼吸效果

#### 3. SettingsScreen.kt
- 添加动画类型选择器 UI
- 实现条件显示逻辑（方向和长度设置仅在流光动画时显示）
- 动态更新设置标签文本

### 🔧 核心代码逻辑

#### 呼吸灯动画实现
```kotlin
AnimationSettings.AnimationType.BREATHING_LIGHT -> {
    // 同时启动透明度和缩放动画
    launch {
        breathingAlpha.animateTo(
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis / 2, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }
    launch {
        breathingScale.animateTo(
            targetValue = 1.05f, // 轻微放大5%
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis / 2, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }
}
```

#### 边框缩放效果实现
```kotlin
// 创建缩放后的路径
val scaledPath = Path()
val scaleMatrix = android.graphics.Matrix()
val centerX = size.width / 2f
val centerY = size.height / 2f
val scale = breathingScale.value

// 设置缩放变换，以中心点为缩放中心
scaleMatrix.setScale(scale, scale, centerX, centerY)

// 应用变换到路径
path.asAndroidPath().transform(scaleMatrix, scaledPath.asAndroidPath())

drawPath(
    path = scaledPath, // 使用缩放后的路径
    brush = breathingBrush,
    style = Stroke(...)
)
```

## 使用方法

### 📱 用户操作步骤
1. 打开应用设置界面
2. 展开"流光动画设置"卡片
3. 选择要配置的框（输入框/输出框）
4. 在"动画类型"中选择"呼吸灯"
5. 调整呼吸速度（数值越小呼吸越快）
6. 调整颜色深浅以获得最佳视觉效果

### 🎯 效果特点
- **呼吸节奏**: 边框透明度从30%到100%周期性变化
- **宽度变化**: 边框宽度在0.8倍到1.2倍之间变化，增强呼吸感
- **边框缩放**: 呼吸灯边框本身轻微放大缩小（1.0倍到1.05倍），营造真实的呼吸感
- **同步动画**: 透明度、宽度和缩放三种效果完美同步
- **平滑过渡**: 使用线性缓动确保呼吸效果自然流畅
- **独立控制**: 输入框和输出框可以设置不同的动画类型

## 兼容性

### ✅ 向后兼容
- 现有的流光动画功能完全保留
- 原有设置自动迁移，默认使用流光动画
- 所有原有配置参数继续有效

### 🔄 设置迁移
- 首次启动时，动画类型默认为 `FLOWING_LIGHT`
- 现有用户的所有设置保持不变
- 新用户可以直接体验两种动画效果

## 测试建议

### 🧪 功能测试
1. **动画切换测试**: 在流光和呼吸灯之间切换，确认效果正确
2. **设置持久化测试**: 重启应用后确认动画类型设置被正确保存
3. **性能测试**: 确认呼吸灯动画不会影响应用性能
4. **视觉效果测试**: 在不同主题下测试呼吸灯效果

### 🎨 视觉验证
- 呼吸灯效果应该平滑自然，没有突兀的跳跃
- 边框宽度变化应该与透明度变化同步
- 在不同颜色主题下都应该有良好的视觉效果

这个新功能为用户提供了更多个性化选择，让界面动画效果更加丰富多样！
