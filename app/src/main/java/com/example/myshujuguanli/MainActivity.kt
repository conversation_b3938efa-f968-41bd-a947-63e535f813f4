package com.example.myshujuguanli

import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatDelegate
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import com.example.myshujuguanli.ui.components.ActivationDialog
import com.example.myshujuguanli.ui.screens.DataInputScreen
import com.example.myshujuguanli.ui.screens.DataManageScreen
import com.example.myshujuguanli.ui.screens.ManagementScreen
import com.example.myshujuguanli.ui.screens.SettingsScreen
// import com.example.myshujuguanli.ui.screens.OddsSettingsScreen
import com.example.myshujuguanli.ui.screens.UserGuideScreen
import com.example.myshujuguanli.ui.screens.DebugScreen
import com.example.myshujuguanli.ui.screens.SpecialBetStatsScreen
import com.example.myshujuguanli.utils.SecurityUtils
import com.example.myshujuguanli.utils.SoundUtils
import com.example.myshujuguanli.utils.ZodiacUtils
import com.example.myshujuguanli.utils.BettingOdds
import com.example.myshujuguanli.utils.DeviceUtils
import com.example.myshujuguanli.theme.ThemeManager
import com.example.myshujuguanli.viewmodels.ThemeViewModel

import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.random.Random

class MainActivity : ComponentActivity() {
    companion object {
        private const val TAG = "MainActivity"
        private const val MAX_RETRY_COUNT = 3
        // 添加一个静态变量标记应用是否是首次启动
        private var isFirstLaunch = true
    }

    private var validationJob: Job? = null
    private var serverRequestFailCount = 0  // 添加服务器请求失败计数
    private var appStatus by mutableStateOf<SecurityUtils.StatusInfo?>(null)
    private var showActivationDialog by mutableStateOf(false)
    private var isManagementEnabled by mutableStateOf(false)

    // 添加平板模式状态
    private var isTabletMode by mutableStateOf(false)
    private var identifiers by mutableStateOf<List<String>>(emptyList())
    private var selectedIdentifier by mutableStateOf("")
    private var isFromShortcut = false

    private val themeViewModel: ThemeViewModel by viewModels()

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        // 关键：在super.onCreate之前调用，以确保Activity在创建时就应用正确的模式
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        super.onCreate(savedInstanceState)
        // 记录启动时间
        val startTime = System.currentTimeMillis()
        
        // 设置冷启动标志
        getSharedPreferences("app_prefs", MODE_PRIVATE)
            .edit()
            .putBoolean("is_cold_start", true)
            .apply()
        
        // 判断是否是真正的冷启动（应用进程重新启动）
        val isColdStart = MyApplication.isColdStart
        if (isColdStart) {
            // 只在真正的冷启动时强制清除随机主题缓存和存储，确保生成全新的随机主题
            ThemeManager.clearRandomThemeCacheAndForceNew(this)
        }
        
        // 预先加载主题设置
        themeViewModel.loadTheme(this, false)  // 始终使用亮色模式
        
        // 注册配置变化监听器
        setupDarkModeListener()

        // 1. 延迟初始化非必需组件
        lifecycleScope.launch {
            // 将这些初始化放到协程中异步执行
            SoundUtils.init()
            ZodiacUtils.loadSavedMappings(this@MainActivity)
            BettingOdds.loadSavedSettings(this@MainActivity)
        }

        // 检查是否是从快捷方式启动
        isFromShortcut = intent?.action == "android.intent.action.VIEW"

        // 如果是从快捷方式启动，设置启动标志
        if (isFromShortcut) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }

        // 在初始化时检测设备类型
        isTabletMode = DeviceUtils.isTablet(this)
        
        // 获取初始屏幕
        val screenExtra = intent?.getStringExtra("screen")
        val initialScreen = when {
            isFromShortcut && screenExtra?.lowercase() == "datamanage" -> "dataManage"
            screenExtra == "management" -> "management"
            else -> "input"
        }

        // 在 setContent 之前加载标识
        loadIdentifiers()

        // 修改为只在首次启动时检查应用状态
        if (MyApplication.isColdStart) {

            lifecycleScope.launch {
                checkAppStatus()
            }
            // 重置标记，防止在同一进程中再次检查
            MyApplication.isColdStart = false
        } else {
            // 使用本地状态
            lifecycleScope.launch {
                val localStatus = SecurityUtils.getCurrentLocalStatus(this@MainActivity)
                appStatus = localStatus
                
                // 检查本地状态是否过期
                val currentTime = System.currentTimeMillis()
                if (localStatus.expiryTime <= currentTime) {
                    // 如果过期，显示弹窗
                    showActivationDialog = true
                    isManagementEnabled = false
                } else {
                    showActivationDialog = false
                    isManagementEnabled = true
                }
            }
        }

        // 添加定期验证检查
        startValidationCheck()
        
        // 强制设置应用的主题模式（在setContent之前）
        // 覆盖系统UI设置，确保即使系统是暗模式，应用也能保持用户设置的亮模式
        val forceDarkMode = themeViewModel.isDarkMode.value
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val mode = if (forceDarkMode) {
                AppCompatDelegate.MODE_NIGHT_YES
            } else {
                AppCompatDelegate.MODE_NIGHT_NO
            }
            // 如果不跟随系统，则强制设置模式
            if (!themeViewModel.followSystemDarkMode.value) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            }
        }

        setContent {
            // 始终使用亮色模式
            val isDarkTheme = false
            
            // 使用ThemeViewModel中的当前主题
            val colorScheme = themeViewModel.currentColorScheme.value ?: MaterialTheme.colorScheme

            var identifiers by remember { mutableStateOf(this.identifiers) }
            var selectedIdentifier by remember { mutableStateOf(this.selectedIdentifier) }
            var currentScreen by remember { mutableStateOf(initialScreen) }

            // 状态弹窗逻辑
            if (showActivationDialog && appStatus != null) {
                ActivationDialog(
                    status = appStatus!!,
                    onActivate = { code ->
                        lifecycleScope.launch {
                            val result = SecurityUtils.activateApp(this@MainActivity)
                            if (result.success) {
                                showActivationDialog = false
                                isManagementEnabled = true
                                checkAppStatus() // 重新检查状态
                            } else {
                                Toast.makeText(this@MainActivity, result.message, Toast.LENGTH_LONG).show()
                            }
                        }
                    },
                    onContinue = {
                        if (appStatus!!.expiryTime > appStatus!!.serverTime) {
                            showActivationDialog = false
                            isManagementEnabled = true
                        } else {
                            // 过期状态直接退出软件
                            finish()
                        }
                    }
                )
            }

            // 标识更新函数
            fun updateIdentifiers(newIdentifiers: List<String>) {
                identifiers = newIdentifiers
                this.identifiers = newIdentifiers
                // 保存到 SharedPreferences
                getSharedPreferences("identifier_prefs", MODE_PRIVATE)
                    .edit()
                    .putStringSet("identifiers", newIdentifiers.toSet())
                    .apply()
            }

            fun updateSelectedIdentifier(newIdentifier: String) {
                selectedIdentifier = newIdentifier
                this.selectedIdentifier = newIdentifier
                // 保存到 SharedPreferences
                getSharedPreferences("identifier_prefs", MODE_PRIVATE)
                    .edit()
                    .putString("selected_identifier", newIdentifier)
                    .apply()
            }

            // 删除标识函数
            fun deleteIdentifier(identifier: String) {
                val newIdentifiers = identifiers.filter { it != identifier }
                updateIdentifiers(newIdentifiers)
                if (identifier == selectedIdentifier) {
                    updateSelectedIdentifier("")
                }
            }

            colorScheme.let {
                MaterialTheme(colorScheme = it) {
                    // 获取当前背景色并设置状态栏
                    val backgroundColor = MaterialTheme.colorScheme.primaryContainer
                    DisposableEffect(backgroundColor, isDarkTheme) {
                        // 使用颜色亮度而不是系统深色模式来决定状态栏样式
                        // 注意：这里根据颜色亮度判断是否需要暗色状态栏图标
                        val isLightBackground = backgroundColor.luminance() > 0.5f
                        window.decorView.systemUiVisibility = if (isLightBackground) {
                            // 浅色背景，使用深色状态栏图标
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                                View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                        } else {
                            // 深色背景，使用浅色状态栏图标
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        }
                        
                        // 强制设置状态栏颜色为透明
                        window.statusBarColor = Color.Transparent.toArgb()
                        
                        onDispose { }
                    }

                    // 主界面内容
                    Box(modifier = Modifier.fillMaxSize()) {
                        when (currentScreen) {
                            "input" -> {
                                DataInputScreen(
                                    appStatus = appStatus,
                                    identifiers = identifiers,
                                    selectedIdentifier = selectedIdentifier,
                                    onUpdateIdentifiers = ::updateIdentifiers,
                                    onUpdateSelectedIdentifier = ::updateSelectedIdentifier,
                                    onDeleteIdentifier = ::deleteIdentifier,
                                    isTabletMode = isTabletMode,
                                    onOpenManagement = {
                                        if (isManagementEnabled) {
                                            currentScreen = "management"
                                        } else {
                                            Toast.makeText(this@MainActivity, "请等待状态检查完成", Toast.LENGTH_SHORT).show()
                                        }
                                    },
                                    onOpenSettings = { currentScreen = "settings" },
                                    onOpenUserGuide = { currentScreen = "userGuide" },
                                    onCheckStatus = {
                                        lifecycleScope.launch {
                                            checkAppStatus()
                                        }
                                    },
                                    onOpenDebug = { currentScreen = "debug" },
                                    isManagementEnabled = isManagementEnabled
                                )
                            }

                            "management" -> {
                                BackHandler {
                                    if (isFromShortcut) {
                                        finish()
                                    } else {
                                        currentScreen = "input"
                                    }
                                }

                                Box(modifier = Modifier.fillMaxSize()) {
                                    // 背景层
                                    Box(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color.White)
                                    )

                                    // 顶部背景条
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .background(MaterialTheme.colorScheme.primaryContainer)
                                            .height(with(LocalDensity.current) {
                                                WindowInsets.statusBars
                                                    .asPaddingValues()
                                                    .calculateTopPadding()
                                                    .toPx()
                                                    .dp + 36.dp
                                            })
                                    )

                                    // 主要内容
                                    Column(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .statusBarsPadding()
                                    ) {
                                        CenterAlignedTopAppBar(
                                            title = {
                                                Text(
                                                    text = "风险控制",
                                                    style = MaterialTheme.typography.titleLarge,
                                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                                )
                                            },
                                            navigationIcon = {
                                                IconButton(onClick = { currentScreen = "input" }) {
                                                    Icon(
                                                        imageVector = Icons.Default.ArrowBack,
                                                        contentDescription = "返回",
                                                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                                                    )
                                                }
                                            },
                                            actions = {
                                                // 添加增删按钮
                                                TextButton(
                                                    onClick = { 
                                                        currentScreen = "specialBetStats"
                                                    },
                                                    colors = ButtonDefaults.textButtonColors(
                                                        contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                                                    )
                                                ) {
                                                    Text("平特类")
                                                }
                                                
                                                TextButton(
                                                    onClick = { currentScreen = "dataManage" },
                                                    colors = ButtonDefaults.textButtonColors(
                                                        contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                                                    )
                                                ) {
                                                    Text("对账")
                                                }
                                            },
                                            colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                                                containerColor = Color.Transparent
                                            ),
                                            modifier = Modifier.height(36.dp)
                                        )

                                        Box(
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .weight(1f)
                                        ) {
                                            ManagementScreen(
                                                isTabletMode = isTabletMode,  // 传递平板模式状态
                                                mainActivity = this@MainActivity,
                                                onNavigateToDataManage = { currentScreen = "dataManage" }
                                            )
                                        }
                                    }
                                }
                            }

                            "settings" -> {
                                BackHandler { currentScreen = "input" }
                                SettingsScreen(
                                    isTabletMode = isTabletMode,
                                    onBack = { currentScreen = "input" },
                                    onNavigateToOddsSettings = { currentScreen = "oddsSettings" },
                                    onNavigateToUserGuide = { currentScreen = "userGuide" },
                                    themeViewModel = themeViewModel,
                                    isDarkTheme = false  // 始终使用亮色模式
                                )
                            }

                            "oddsSettings" -> {
                                BackHandler { currentScreen = "settings" }
                                // OddsSettingsScreen(
                                //     onBack = { currentScreen = "settings" }
                                // )
                            }

                            "userGuide" -> {
                                BackHandler {
                                    currentScreen = "input"
                                }
                                UserGuideScreen(
                                    onNavigateBack = { currentScreen = "input" }
                                )
                            }

                            "dataManage" -> {
                                BackHandler {
                                    if (isFromShortcut) {
                                        finish()
                                    } else {
                                        currentScreen = "management"
                                    }
                                }
                                DataManageScreen(
                                    onBack = {
                                        if (isFromShortcut) {
                                            finish()
                                        } else {
                                            currentScreen = "management"
                                        }
                                    }
                                )
                            }

                            "debug" -> {
                                BackHandler { currentScreen = "input" }
                                DebugScreen(
                                    onNavigateBack = { currentScreen = "input" }
                                )
                            }
                            
                            "specialBetStats" -> {
                                BackHandler { currentScreen = "management" }
                                SpecialBetStatsScreen(
                                    onNavigateBack = { currentScreen = "management" }
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        // 更新 isFromShortcut 状态
        isFromShortcut = intent?.action == "android.intent.action.VIEW"
        
        // 重新检查和加载主题，确保安全
        val isDarkMode = resources.configuration.uiMode and 
                android.content.res.Configuration.UI_MODE_NIGHT_MASK == 
                android.content.res.Configuration.UI_MODE_NIGHT_YES
        try {
            // 只有在跟随系统模式时才重新加载主题
            if (themeViewModel.followSystemDarkMode.value) {
                themeViewModel.onDarkModeChanged(this, isDarkMode)
            }
        } catch (e: Exception) {
            Log.e(TAG, "onNewIntent中主题重载失败", e)
        }
    }

    private fun loadIdentifiers() {
        val prefs = getSharedPreferences("identifier_prefs", MODE_PRIVATE)
        identifiers = prefs.getStringSet("identifiers", setOf())?.toList() ?: emptyList()
        selectedIdentifier = prefs.getString("selected_identifier", "") ?: ""
    }

    override fun onDestroy() {
        super.onDestroy()
        // 释放提示音资源
        SoundUtils.release()
    }

    override fun onResume() {
        super.onResume()
        
        // 强制设置为亮色模式
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        }
        
        // 获取上次验证的时间
        val prefs = getSharedPreferences(SecurityUtils.PREFS_NAME, MODE_PRIVATE)
        val lastValidationTime = prefs.getLong("last_validation_time", 0)
        val currentTime = System.currentTimeMillis()
        
        // 只有在距离上次验证超过一定时间（比如2小时）才重新验证
        if (currentTime - lastValidationTime > 2 * 60 * 60 * 1000) {
            // 记录本次验证时间
            prefs.edit().putLong("last_validation_time", currentTime).apply()
            
            // 验证状态
            val expiryTime = prefs.getLong(SecurityUtils.KEY_EXPIRY_TIME, 0)
            if (currentTime <= expiryTime) {
                // 只有在本地状态显示未过期时才验证，避免网络请求
                lifecycleScope.launch {
                    // 使用本地状态，不发起网络请求
                    appStatus = SecurityUtils.getCurrentLocalStatus(this@MainActivity)
                    showActivationDialog = false
                    isManagementEnabled = true
                }
            }
        }
        
        // 确保验证任务正在运行，但不立即触发验证
        if (validationJob == null || validationJob?.isActive == false) {
            startValidationCheck()
        }
    }

    override fun onPause() {
        super.onPause()
        // 停止验证任务
        validationJob?.cancel()
        // 在应用暂停时保存标识状态
        saveIdentifiers()
    }

    private fun saveIdentifiers() {
        getSharedPreferences("identifier_prefs", MODE_PRIVATE)
            .edit()
            .putStringSet("identifiers", identifiers.toSet())
            .putString("selected_identifier", selectedIdentifier)
            .apply()
    }

    private suspend fun checkAppStatus() {
        try {
            val status = SecurityUtils.checkAppStatus(this)
            appStatus = status
            
            when (status.status) {
                SecurityUtils.AppStatus.ACTIVATED -> {
                    // 已激活状态不显示弹窗
                    showActivationDialog = false
                    isManagementEnabled = true
                }
                SecurityUtils.AppStatus.NORMAL -> {
                    // 订阅状态，检查是否需要显示到期提醒
                    val remainingTime = status.expiryTime - status.serverTime
                    val remainingDays = remainingTime / (24 * 60 * 60 * 1000)
                    
                    if (remainingDays <= 3) {
                        // 检查今天是否已经显示过提醒
                        val prefs = getSharedPreferences("activation_prefs", MODE_PRIVATE)
                        val lastReminderDate = prefs.getLong("last_reminder_date", 0)
                        val today = System.currentTimeMillis() / (24 * 60 * 60 * 1000)
                        
                        if (lastReminderDate < today) {
                            // 今天还没显示过提醒
                            showActivationDialog = true
                            // 记录今天已经显示过提醒
                            prefs.edit().putLong("last_reminder_date", today).apply()
                        } else {
                            showActivationDialog = false
                        }
                    } else {
                        showActivationDialog = false
                    }
                    isManagementEnabled = true
                }
                SecurityUtils.AppStatus.TRIAL -> {
                    // 试用期状态每次都显示弹窗
                    showActivationDialog = true
                    isManagementEnabled = true
                }
                SecurityUtils.AppStatus.EXPIRED -> {
                    // 过期状态每次都显示弹窗
                    showActivationDialog = true
                    isManagementEnabled = false
                }
            }
        } catch (e: Exception) {
            Toast.makeText(this, "检查应用状态失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun startValidationCheck() {
        stopValidationCheck()  // 确保之前的任务被取消
        serverRequestFailCount = 0  // 重置失败计数
        
        validationJob = lifecycleScope.launch {
            while (isActive) {
                // 延长检查间隔
                delay(30 * 60 * 1000) // 30分钟检查一次
                
                // 检查是否需要验证，优先使用本地数据
                val prefs = getSharedPreferences(SecurityUtils.PREFS_NAME, MODE_PRIVATE)
                val currentTime = System.currentTimeMillis()
                val expiryTime = prefs.getLong(SecurityUtils.KEY_EXPIRY_TIME, 0)
                
                // 只有在过期或接近过期时才验证
                if (currentTime > expiryTime || (expiryTime - currentTime < 3 * 24 * 60 * 60 * 1000)) {
                    validateStatus()
                }
            }
        }
    }

    private fun validateStatus() {
        // 先进行本地验证
        if (!SecurityUtils.validateLocalStatus(this)) {
            lifecycleScope.launch {
                try {
                    val prefs = getSharedPreferences(SecurityUtils.PREFS_NAME, MODE_PRIVATE)
                    val currentTime = System.currentTimeMillis()
                    val expiryTime = prefs.getLong(SecurityUtils.KEY_EXPIRY_TIME, 0)
                    val deviceStatus = prefs.getString(SecurityUtils.KEY_DEVICE_STATUS, null)
                    
                    // 对于NORMAL状态，仅在快到期时才显示弹窗
                    if (deviceStatus == SecurityUtils.AppStatus.NORMAL.name) {
                        val remainingTime = expiryTime - currentTime
                        val remainingDays = remainingTime / (24 * 60 * 60 * 1000)
                        
                        if (remainingDays > 3) {
                            // 剩余天数大于3天，不显示弹窗
                            showActivationDialog = false
                            return@launch
                        }
                    }
                    
                    // 如果确实过期了，才请求服务器
                    if (currentTime > expiryTime) {
                        try {
                            // 重新获取最新状态
                            val status = SecurityUtils.checkAppStatus(this@MainActivity)
                            appStatus = status
                            showActivationDialog = true
                            isManagementEnabled = false
                            // 成功获取状态后停止验证
                            stopValidationCheck()
                        } catch (e: Exception) {
                            serverRequestFailCount++
                            
                            if (serverRequestFailCount >= MAX_RETRY_COUNT) {
                                // 达到最大重试次数，停止验证
                                showActivationDialog = true
                                isManagementEnabled = false
                                stopValidationCheck()
                            }
                        }
                    } else {
                        // 关键修改: 使用本地状态而非直接显示过期
                        try {
                            // 使用已保存的本地状态
                            val localStatus = SecurityUtils.getCurrentLocalStatus(this@MainActivity)
                            appStatus = localStatus
                            
                            // 根据本地状态决定是否显示弹窗和管理功能
                            when (localStatus.status) {
                                SecurityUtils.AppStatus.ACTIVATED -> {
                                    showActivationDialog = false
                                    isManagementEnabled = true
                                }
                                SecurityUtils.AppStatus.NORMAL -> {
                                    val remainingTime = localStatus.expiryTime - currentTime
                                    val remainingDays = remainingTime / (24 * 60 * 60 * 1000)
                                    showActivationDialog = remainingDays <= 3
                                    isManagementEnabled = true
                                }
                                SecurityUtils.AppStatus.TRIAL -> {
                                    showActivationDialog = false
                                    isManagementEnabled = true
                                }
                                else -> {
                                    // 过期状态
                                    showActivationDialog = true
                                    isManagementEnabled = false
                                }
                            }
                        } catch (e: Exception) {
                            // 如果获取本地状态失败，才显示过期
                            showActivationDialog = true
                            isManagementEnabled = false
                        }
                    }
                } catch (e: Exception) {
                    showActivationDialog = true
                    isManagementEnabled = false
                    stopValidationCheck()
                }
            }
        } else {
            // 本地验证成功，默认不显示弹窗
            showActivationDialog = false
            isManagementEnabled = true
            stopValidationCheck()
        }
    }

    // 添加停止验证方法
    private fun stopValidationCheck() {
        validationJob?.cancel()
        validationJob = null
        serverRequestFailCount = 0  // 重置失败计数
    }

    // 供其他组件调用的随机主题生成方法（简化为调用ViewModel的方法）
    fun applyRandomTheme(indexHint: Int = -1) {
        // 始终使用亮色模式，忽略系统设置
        val isDarkMode = false
        
        if(indexHint >= 0) {
            themeViewModel.updateTheme(this, indexHint, isDarkMode)
        } else {
            themeViewModel.enableRandomTheme(this, isDarkMode)
        }
    }

    // 注册暗/亮模式变化监听器 - 简化版，不再实际监听
    private fun setupDarkModeListener() {
        // 什么都不做，我们不再需要监听暗/亮模式变化
        // 保留方法以保持兼容性
    }
    
    // 添加配置变化处理方法
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        
        // 忽略系统暗/亮模式变化，始终保持亮色模式
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        }
    }
}