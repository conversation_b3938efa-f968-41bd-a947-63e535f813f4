package com.example.myshujuguanli.viewmodels

import android.content.Context
import android.content.res.Configuration
import android.util.Log
import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myshujuguanli.theme.ThemeInfo
import com.example.myshujuguanli.theme.ThemeManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class ThemeViewModel : ViewModel() {
    // 当前颜色方案
    private val _currentColorScheme = mutableStateOf<ColorScheme?>(null)
    val currentColorScheme: State<ColorScheme?> = _currentColorScheme
    
    // 主题信息包含主题索引和是否为随机主题
    private val _themeInfo = mutableStateOf(ThemeInfo(false, 0, false))
    val themeInfo: State<ThemeInfo> = _themeInfo
    
    // 是否跟随系统深色模式 - 始终为false
    private val _followSystemDarkMode = mutableStateOf(false)
    val followSystemDarkMode: State<Boolean> = _followSystemDarkMode
    
    // 当前深色模式状态 - 始终为false
    private val _isDarkMode = mutableStateOf(false)
    val isDarkMode: State<Boolean> = _isDarkMode
    
    // 当前主题索引的便捷获取方法
    val currentThemeIndex: Int
        get() = _themeInfo.value.themeIndex
    
    // 是否是随机主题的便捷获取方法
    val isRandomTheme: Boolean
        get() = _themeInfo.value.isRandomTheme
        
    // 加载主题 - 忽略isDarkMode参数，始终使用亮模式
    fun loadTheme(context: Context, isDarkMode: Boolean) {
        // 始终设置为亮模式
        _isDarkMode.value = false
        _currentColorScheme.value = ThemeManager.loadThemeSettings(context, false)
        val themeInfo = ThemeManager.getCurrentThemeInfo(context)
        // 覆盖followSystem为false
        _themeInfo.value = themeInfo.copy(followSystem = false)
        _followSystemDarkMode.value = false
    }
    
    // 更新主题 - 忽略isDarkMode参数，始终使用亮模式
    fun updateTheme(context: Context, themeIndex: Int, isDarkMode: Boolean) {
        ThemeManager.saveThemeSettings(context, themeIndex)
        val themeInfo = ThemeManager.getCurrentThemeInfo(context)
        // 覆盖followSystem为false
        _themeInfo.value = themeInfo.copy(followSystem = false)
        _currentColorScheme.value = ThemeManager.getThemeScheme(themeIndex, false)
    }
    
    // 启用随机主题 - 忽略isDarkMode参数，始终使用亮模式
    fun enableRandomTheme(context: Context, isDarkMode: Boolean) {
        ThemeManager.enableRandomTheme(context)
        val themeInfo = ThemeManager.getCurrentThemeInfo(context)
        // 覆盖followSystem为false
        _themeInfo.value = themeInfo.copy(followSystem = false)
        _currentColorScheme.value = ThemeManager.getRandomTheme(false)
    }
    
    // 更新当前颜色方案，保持兼容性
    fun updateThemeColor(colorScheme: ColorScheme) {
        _currentColorScheme.value = colorScheme
    }
    
    // 新增：仅更新主题信息不重新生成随机主题
    fun updateThemeInfoOnly(context: Context) {
        val themeInfo = ThemeManager.getCurrentThemeInfo(context)
        // 覆盖followSystem为false
        _themeInfo.value = themeInfo.copy(followSystem = false)
        _followSystemDarkMode.value = false
    }
    
    // 处理暗/亮模式变化 - 忽略参数，始终使用亮模式
    fun onDarkModeChanged(context: Context, isDarkMode: Boolean) {
        // 忽略参数，始终使用亮模式
        _isDarkMode.value = false
        
        // 保存亮模式设置
        saveDarkModeState(context, false)
        
        // 获取更新后的主题方案
        val updatedScheme = ThemeManager.getUpdatedThemeForDarkMode(context, false)
        _currentColorScheme.value = updatedScheme
    }
    
    // 设置是否跟随系统暗/亮模式 - 始终设置为false
    fun setFollowSystemDarkMode(context: Context, follow: Boolean) {
        // 忽略参数，始终设置为false
        ThemeManager.setFollowSystem(context, false)
        _followSystemDarkMode.value = false
        updateThemeInfoOnly(context)
    }
    
    // 手动设置暗/亮模式 - 忽略参数，始终使用亮模式
    fun setDarkMode(context: Context, isDark: Boolean) {
        // 忽略参数，始终使用亮模式
        _isDarkMode.value = false
        saveDarkModeState(context, false)
        
        // 获取更新后的主题方案
        val updatedScheme = if (isRandomTheme) {
            ThemeManager.getRandomTheme(false)
        } else {
            ThemeManager.getThemeScheme(currentThemeIndex, false)
        }
        _currentColorScheme.value = updatedScheme
    }
    
    // 保存暗/亮模式状态到SharedPreferences - 始终保存为亮模式
    private fun saveDarkModeState(context: Context, isDarkMode: Boolean) {
        context.getSharedPreferences("theme_prefs", Context.MODE_PRIVATE)
            .edit()
            .putBoolean("user_dark_mode", false)  // 始终保存为false
            .apply()
    }
    
    // 获取用户保存的暗/亮模式状态 - 始终返回亮模式
    fun getUserDarkModeState(context: Context): Boolean? {
        // 始终返回false
        return false
    }
} 