package com.example.myshujuguanli.theme

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import kotlin.random.Random

object ThemeManager {
    private const val PREFS_NAME = "theme_prefs"
    private const val KEY_THEME_INDEX = "theme_index"
    private const val KEY_IS_RANDOM_THEME = "is_random_theme"
    private const val KEY_RANDOM_THEME_INDEX = "random_theme_index"
    private const val KEY_FOLLOW_SYSTEM = "follow_system"
    
    // 添加缓存随机主题的变量，避免每次都随机生成新主题
    private var cachedRandomThemeIndex: Int? = null
    
    // 用于保存当前暗/亮模式状态
    private var currentDarkMode: Boolean = false
    
    // 预定义主题集合
    val themes = listOf(
        ThemeDefinition(
            name = "蓝色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF1976D2),
                primaryContainer = Color(0xFFBBDEFB)
            )
        ),
        ThemeDefinition(
            name = "绿色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF388E3C),
                primaryContainer = Color(0xFFC8E6C9)
            )
        ),
        ThemeDefinition(
            name = "青色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF5E35B1),
                primaryContainer = Color(0xFFD1C4E9)
            )
        ),
        ThemeDefinition(
            name = "紫色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF7B1FA2),
                primaryContainer = Color(0xFFCE93D8)
            )
        ),
        ThemeDefinition(
            name = "灰色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF546E7A),
                primaryContainer = Color(0xFFCFD8DC)
            )
        ),
        ThemeDefinition(
            name = "橙色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFFF57C00),
                primaryContainer = Color(0xFFFFE0B2)
            )
        )
    )
    
    // 从存储加载主题设置
    fun loadThemeSettings(context: Context, isDarkMode: Boolean): ColorScheme {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val isRandomTheme = prefs.getBoolean(KEY_IS_RANDOM_THEME, false)
        val savedThemeIndex = prefs.getInt(KEY_THEME_INDEX, 0)
        
        // 无论传入什么，始终使用亮模式
        currentDarkMode = false
        
        return if (isRandomTheme) {
            // 检查是否已经有缓存的随机主题索引
            val themeIndex = cachedRandomThemeIndex ?: run {
                // 如果没有缓存，从SharedPreferences中恢复或生成新的随机主题索引
                val savedRandomIndex = prefs.getInt(KEY_RANDOM_THEME_INDEX, -1)
                val newIndex = if (savedRandomIndex >= 0 && savedRandomIndex < themes.size) {
                    // 恢复之前保存的随机主题索引
                    savedRandomIndex
                } else {
                    // 生成新的随机主题索引
                    val randomIndex = Random.nextInt(themes.size)
                    Log.d("ThemeManager", "生成新的随机主题索引: $randomIndex")
                    // 保存新生成的随机主题索引
                    prefs.edit().putInt(KEY_RANDOM_THEME_INDEX, randomIndex).apply()
                    randomIndex
                }
                cachedRandomThemeIndex = newIndex
                newIndex
            }

            // 始终使用亮色主题
            themes[themeIndex].lightScheme
        } else {
            // 非随机主题模式，清除缓存的随机主题
            cachedRandomThemeIndex = null
            val index = if (savedThemeIndex in themes.indices) savedThemeIndex else 0
            // 始终使用亮色主题
            themes[index].lightScheme
        }
    }
    
    // 保存主题设置
    fun saveThemeSettings(context: Context, themeIndex: Int) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putInt(KEY_THEME_INDEX, themeIndex)
            .putBoolean(KEY_IS_RANDOM_THEME, false)
            .apply()
        // 清除缓存的随机主题
        cachedRandomThemeIndex = null
    }
    
    // 启用随机主题
    fun enableRandomTheme(context: Context) {
        // 启用随机主题时，总是生成一个新的随机主题并缓存
        val randomIndex = Random.nextInt(themes.size)
        cachedRandomThemeIndex = randomIndex

        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putBoolean(KEY_IS_RANDOM_THEME, true)
            .putInt(KEY_RANDOM_THEME_INDEX, randomIndex) // 保存随机主题索引
            .apply()
    }
    
    // 设置是否跟随系统暗/亮模式
    fun setFollowSystem(context: Context, follow: Boolean) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putBoolean(KEY_FOLLOW_SYSTEM, follow)
            .apply()
    }
    
    // 判断是否跟随系统暗/亮模式
    fun isFollowingSystem(context: Context): Boolean {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .getBoolean(KEY_FOLLOW_SYSTEM, true)
    }
    
    // 获取指定主题的ColorScheme
    fun getThemeScheme(themeIndex: Int, isDarkMode: Boolean): ColorScheme? {
        // 无论传入什么，始终使用亮模式
        currentDarkMode = false
        
        return if (themeIndex in themes.indices) {
            // 始终使用亮色主题
            themes[themeIndex].lightScheme
        } else {
            null
        }
    }
    
    // 获取随机主题 - 修改为使用缓存的随机索引或创建新的
    fun getRandomTheme(isDarkMode: Boolean): ColorScheme {
        // 无论传入什么，始终使用亮模式
        currentDarkMode = false
        
        val randomIndex = cachedRandomThemeIndex ?: Random.nextInt(themes.size).also { 
            cachedRandomThemeIndex = it 
        }
        // 始终使用亮色主题
        return themes[randomIndex].lightScheme
    }
    
    // 根据当前主题索引获取更新后的主题方案（用于暗/亮模式切换）
    fun getUpdatedThemeForDarkMode(context: Context, isDarkMode: Boolean): ColorScheme {
        // 无论传入什么，始终使用亮模式
        currentDarkMode = false
        
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val isRandomTheme = prefs.getBoolean(KEY_IS_RANDOM_THEME, false)
        
        return if (isRandomTheme) {
            val randomIndex = cachedRandomThemeIndex ?: prefs.getInt(KEY_RANDOM_THEME_INDEX, 0)
            if (randomIndex in themes.indices) {
                // 始终使用亮色主题
                themes[randomIndex].lightScheme
            } else {
                // 始终使用亮色主题
                themes[0].lightScheme
            }
        } else {
            val themeIndex = prefs.getInt(KEY_THEME_INDEX, 0)
            val index = if (themeIndex in themes.indices) themeIndex else 0
            // 始终使用亮色主题
            themes[index].lightScheme
        }
    }
    
    // 清除随机主题缓存，用于应用冷启动时
    fun clearRandomThemeCache() {
        val oldIndex = cachedRandomThemeIndex
        cachedRandomThemeIndex = null
    }

    // 清除随机主题缓存并强制生成新的随机主题（用于冷启动）
    fun clearRandomThemeCacheAndForceNew(context: Context) {
        val oldIndex = cachedRandomThemeIndex
        cachedRandomThemeIndex = null

        // 同时清除 SharedPreferences 中保存的随机主题索引，强制生成新的
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .remove(KEY_RANDOM_THEME_INDEX)
            .apply()
    }
    
    // 获取当前主题信息
    fun getCurrentThemeInfo(context: Context): ThemeInfo {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val isRandomTheme = prefs.getBoolean(KEY_IS_RANDOM_THEME, false)
        val themeIndex = if (isRandomTheme) {
            // 如果是随机主题，使用缓存的随机索引或从存储中恢复
            cachedRandomThemeIndex ?: prefs.getInt(KEY_RANDOM_THEME_INDEX, 0)
        } else {
            prefs.getInt(KEY_THEME_INDEX, 0)
        }
        val followSystem = prefs.getBoolean(KEY_FOLLOW_SYSTEM, true)
        return ThemeInfo(isRandomTheme, themeIndex, followSystem)
    }
    
    // 监听深色模式变化的非Composable函数
    fun observeDarkModeChanges(
        lifecycleOwner: LifecycleOwner,
        onDarkModeChanged: (Boolean) -> Unit
    ) {
        // 不再需要监听暗/亮模式变化，直接返回
        return
    }
    
    // Composable版本的深色模式变化监听器（保留兼容性）
    @Composable
    fun ObserveDarkModeChanges(
        lifecycleOwner: LifecycleOwner,
        onDarkModeChanged: (Boolean) -> Unit
    ) {
        // 不再需要监听暗/亮模式变化，什么都不做
        DisposableEffect(lifecycleOwner) {
            onDispose { }
        }
    }
}

// 主题定义数据类
data class ThemeDefinition(
    val name: String,
    val lightScheme: ColorScheme
)

// 主题信息数据类，增加了followSystem字段
data class ThemeInfo(
    val isRandomTheme: Boolean,
    val themeIndex: Int,
    val followSystem: Boolean = true
)

// 提供CompositionLocal以在Compose中访问主题
val LocalAppTheme = staticCompositionLocalOf { ThemeInfo(false, 0, true) } 