package com.example.myshujuguanli

import android.app.Application
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import androidx.appcompat.app.AppCompatDelegate
import com.example.myshujuguanli.theme.ThemeManager
import com.example.myshujuguanli.utils.ParseLearningEngine
import com.example.myshujuguanli.utils.DataParser

class MyApplication : Application() {
    companion object {
        var isColdStart = true
    }
    
    override fun onCreate() {
        // 在super.onCreate()之前设置，确保立即生效
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        super.onCreate()
        // 应用启动时重置为冷启动状态
        isColdStart = true
        
        // 强制使用亮模式
        forceLightMode()
        
        // 预先初始化主题
        initTheme()
        
        ParseLearningEngine.init(this)
        DataParser.init(this)
    }
    
    private fun forceLightMode() {
        // 强制设置为亮模式
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        
        // 保存设置到ThemeManager
        ThemeManager.setFollowSystem(this, false)
        
        // 保存当前模式为亮模式
        getSharedPreferences("theme_prefs", Context.MODE_PRIVATE)
            .edit()
            .putBoolean("user_dark_mode", false)
            .apply()
    }
    
    private fun initTheme() {
        // 清除随机主题缓存
        ThemeManager.clearRandomThemeCache()
        
        // 始终使用亮模式
        val isDarkMode = false
        
        // 预加载主题设置
        ThemeManager.loadThemeSettings(this, isDarkMode)
    }
    
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        
        // 忽略系统模式变化，始终使用亮模式
        forceLightMode()
    }
} 