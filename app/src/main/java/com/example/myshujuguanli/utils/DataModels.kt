package com.example.myshujuguanli.utils

import com.example.myshujuguanli.utils.SecurityUtils.StatusInfo
import java.io.Serializable
import android.content.Context
import android.util.Log
import org.json.JSONObject
import org.json.JSONArray

data class NumberData(
    val totalAmount: Int = 0,
    val profit: Int = 0,
    val macauAmount: Int = 0,
    val hongKongAmount: Int = 0,
    val count: Int = 0,
    val specialAmount: Int = 0,
    val specialBets: Map<String, Int> = mapOf()
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}

data class Stats(
    val totalAmount: Int = 0,
    val maxProfit: Int = 0,
    val winRate: Float = 0f,
    val macauAmount: Int = 0,
    val hongKongAmount: Int = 0
) : Serializable {
    companion object {
        private const val serialVersionUID = 2L
    }
}

data class ProfitStats(
    val totalAmount: Int = 0,
    val maxProfit: Int = 0,
    val winRate: Float = 0f,
    val macauAmount: Int = 0,
    val hongKongAmount: Int = 0,
    val adjustments: Map<Int, Int> = mapOf(),
    val totalReduction: Int = 0,
    val adjustedTotal: Int = 0,
    val adjustedMaxProfit: Int = 0,
    val adjustedWinRate: Float = 0f,
    val specialBetsTotal: Int = 0,
    val macauSpecialTotal: Int = 0,
    val hkSpecialTotal: Int = 0
) : Serializable {
    companion object {
        private const val serialVersionUID = 3L
    }
}

data class WinningResult(
    val description: String,
    val winAmount: Int
) : Serializable {
    companion object {
        private const val serialVersionUID = 4L
    }
}

data class SpecialBetResult(
    val numbers: List<Int>,
    val amount: Int,
    val type: String
) : Serializable {
    companion object {
        private const val serialVersionUID = 5L
    }
}

// 投注类型枚举
enum class BetType {
    SPECIAL_NUMBER,  // 特码
    ZODIAC          // 生肖
}

// 投注记录数据类
data class BetRecord(
    val type: BetType,
    val tag: String,          // 标签（澳门/香港）
    val timestamp: String,    // 时间戳
    val source: String,       // 来源
    val originalData: String, // 原始数据
    val amount: Double,       // 投注金额
    val payout: Double,       // 赔付金额
    // 特码相关字段
    val winningNumber: Int? = null,
    val zodiac: String? = null,
    // 生肖相关字段
    val zodiacBetType: String? = null,  // 如：2连肖、3连肖等
    val winningZodiac: String? = null,
    val odds: Double? = null
) : Serializable {
    companion object {
        private const val serialVersionUID = 6L
    }
}

// 统计数据类
data class Statistics(
    val tag: String,         // 标签（澳门/香港）
    val totalBets: Double,   // 总注额
    val totalPayouts: Double,// 总派彩
    val profit: Double       // 盈利
) : Serializable {
    companion object {
        private const val serialVersionUID = 7L
    }
}

// 赔率设置数据类
data class ZodiacOddsSettings(
    val normalOdds: Map<String, Double>,
    val currentYearOdds: Map<String, Double>
) : Serializable {
    companion object {
        private const val serialVersionUID = 9L
    }
}

// 赔率相关常量和设置
object BettingOdds {
    // 将默认赔率改为公开常量
    val DEFAULT_NORMAL_ODDS = mapOf(
        "特码" to 47.0,
        "特肖" to 11.0,
        "平特" to 2.0,
        "2连肖" to 4.0,
        "3连肖" to 10.0,
        "4连肖" to 30.0,
        "5连肖" to 100.0,
        "二中二" to 60.0,    // 新增
        "三中二" to 19.0,   // 新增
        "三中三" to 600.0,   // 新增
        "平特尾" to 1.8,     // 新增
        "五不中" to 2.0,     // 新增
        "六不中" to 2.4,     // 新增
        "七不中" to 2.8,     // 新增
        "八不中" to 3.4,     // 新增
        "九不中" to 4.1,     // 新增
        "十不中" to 5.0,     // 新增
        "十一不中" to 6.0,   // 新增
        "包波色" to 2.6,   // 新增
        "包波色双单" to 5.0,   // 新增
        "包双单合双单" to 1.8,   // 新增
    )

    val DEFAULT_CURRENT_YEAR_ODDS = mapOf(
        "特肖" to 9.0,
        "平特" to 1.8,
        "2连肖" to 3.5,
        "3连肖" to 8.0,
        "4连肖" to 25.0,
        "5连肖" to 80.0
        // 特码、二中二、三中三、平特尾不需要本命年赔率
    )

    private var currentSettings = ZodiacOddsSettings(DEFAULT_NORMAL_ODDS, DEFAULT_CURRENT_YEAR_ODDS)

    // 添加默认赔率常量
    const val DEFAULT_SPECIAL_NUMBER_ODDS = 47.0  // 特码默认赔率
    
    // 添加赔率范围限制
    private val ODDS_RANGE = mapOf(
        "特码" to (20.0..100.0),
        "特肖" to (5.0..20.0),
        "平特" to (1.5..5.0),
        "2连肖" to (2.0..10.0),
        "3连肖" to (5.0..20.0),
        "4连肖" to (15.0..50.0),
        "5连肖" to (50.0..200.0)
    )

    // 验证赔率是否在合理范围内
    fun validateOdds(type: String, odds: Double): Boolean {
        val range = ODDS_RANGE[type] ?: return false
        return odds in range
    }

    // 获取赔率范围
    fun getOddsRange(type: String): ClosedFloatingPointRange<Double>? {
        return ODDS_RANGE[type]
    }

    fun getOddsForZodiac(betType: String, zodiac: String, currentYearZodiac: String): Double {
        val odds = if (zodiac == currentYearZodiac) {
            currentSettings.currentYearOdds[betType]
        } else {
            currentSettings.normalOdds[betType]
        }
        return odds ?: 0.0
    }

    fun updateOddsSettings(context: Context, settings: ZodiacOddsSettings) {
        currentSettings = settings
        saveOddsSettings(context, settings)
    }

    fun getCurrentSettings(): ZodiacOddsSettings = currentSettings

    // 更新常规赔率方法
    fun updateNormalOdds(betType: String, odds: Double, context: Context? = null) {
        val newNormalOdds = currentSettings.normalOdds + (betType to odds)
        currentSettings = currentSettings.copy(normalOdds = newNormalOdds)
        
        // 如果提供了Context，则保存更新后的设置
        context?.let {
            saveOddsSettings(it, currentSettings)
        }
    }

    // 更新本命年赔率方法
    fun updateCurrentYearOdds(betType: String, odds: Double, context: Context? = null) {
        // 只有存在于DEFAULT_CURRENT_YEAR_ODDS的类型才能更新
        if (betType in DEFAULT_CURRENT_YEAR_ODDS.keys) {
            val newCurrentYearOdds = currentSettings.currentYearOdds + (betType to odds)
            currentSettings = currentSettings.copy(currentYearOdds = newCurrentYearOdds)
            
            // 如果提供了Context，则保存更新后的设置
            context?.let {
                saveOddsSettings(it, currentSettings)
            }
        }
    }

    // 重置为默认赔率
    fun resetToDefaultSettings(context: Context? = null) {
        currentSettings = ZodiacOddsSettings(DEFAULT_NORMAL_ODDS, DEFAULT_CURRENT_YEAR_ODDS)
        
        // 如果提供了Context，则保存更新后的设置
        context?.let {
            saveOddsSettings(it, currentSettings)
        }
    }

    private fun saveOddsSettings(context: Context, settings: ZodiacOddsSettings) {
        val prefs = context.getSharedPreferences("odds_settings", Context.MODE_PRIVATE)
        val normalOddsJson = JSONObject()
        val currentYearOddsJson = JSONObject()

        // 手动构建 JSON 对象
        settings.normalOdds.forEach { (key, value) ->
            normalOddsJson.put(key, value)
        }
        settings.currentYearOdds.forEach { (key, value) ->
            currentYearOddsJson.put(key, value)
        }

        prefs.edit().apply {
            putString("normal_odds", normalOddsJson.toString())
            putString("current_year_odds", currentYearOddsJson.toString())
        }.apply()
    }

    fun loadSavedSettings(context: Context) {
        val prefs = context.getSharedPreferences("odds_settings", Context.MODE_PRIVATE)
        try {
            val normalOddsJson = prefs.getString("normal_odds", null)
            val currentYearOddsJson = prefs.getString("current_year_odds", null)

            if (normalOddsJson != null && currentYearOddsJson != null) {
                val normalOdds = jsonToMap(JSONObject(normalOddsJson))
                val currentYearOdds = jsonToMap(JSONObject(currentYearOddsJson))
                currentSettings = ZodiacOddsSettings(normalOdds, currentYearOdds)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            // 如果加载失败，使用默认设置
            currentSettings = ZodiacOddsSettings(DEFAULT_NORMAL_ODDS, DEFAULT_CURRENT_YEAR_ODDS)
        }
    }

    private fun jsonToMap(jsonObject: JSONObject): Map<String, Double> {
        val map = mutableMapOf<String, Double>()
        val keys = jsonObject.keys()
        while (keys.hasNext()) {
            val key = keys.next()
            map[key] = jsonObject.getDouble(key)
        }
        return map
    }
}

// 中奖结果数据类
data class ZodiacWinResult(
    val isWin: Boolean,
    val rate: Double,
    val matchingZodiacs: Set<String> = setOf()
) : Serializable {
    companion object {
        private const val serialVersionUID = 8L
    }
}

// 激活结果数据类
data class ActivationResult(
    val success: Boolean,
    val message: String? = null,
    val status: StatusInfo? = null
)

data class ParseLearningData(
    val originalInput: String,
    val parsedOutput: String,
    val userModifiedOutput: String,
    val timestamp: Long = System.currentTimeMillis()
)

data class FormatRule(
    val pattern: String,
    var confidence: Float = 0f
) {
    fun apply(input: String): String = input

    companion object {
        val DEFAULT = FormatRule("")
    }
}

data class AmountRule(
    val number: String,
    var preferredAmount: Int = 0,
    var confidence: Float = 0f
) {
    fun apply(input: String): String = input

    companion object {
        val DEFAULT = AmountRule("")
    }
}

// 添加赔率设置数据类
data class OddsSettingItem(
    val type: String,
    var normalOdds: Double,
    var currentYearOdds: Double
) 