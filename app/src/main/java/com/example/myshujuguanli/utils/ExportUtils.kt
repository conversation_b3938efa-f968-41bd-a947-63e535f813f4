package com.example.myshujuguanli.utils

import android.content.Context
import android.content.Intent
import android.database.sqlite.SQLiteDatabase
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.net.Uri
import android.util.Log
import android.widget.Toast
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileOutputStream
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object ExportUtils {
    private const val TAG = "ExportUtils"
    private var isFromDialog = false

    //使用 StringBuilder 池来减少内存分配
   private val stringBuilderPool = object : ThreadLocal<StringBuilder>() {
       override fun initialValue(): StringBuilder {
           return StringBuilder(1024)  // 预分配合适的初始容量
       }
   }

    fun shareAdjustmentSuggestions(
        context: Context, 
        adjustments: Map<Int, Int>, 
        showMacau: <PERSON>ole<PERSON>, 
        showHongKong: Boolean 
        ) 
        {
        val content = buildString {
            // 构建地区字符串
            val regions = mutableListOf<String>()
            if (showMacau) regions.add("澳门")
            if (showHongKong) regions.add("香港")
            
            // 添加时间和地区
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            // appendLine("时间: $timestamp")
            appendLine("${regions.joinToString("/")}")
             appendLine("---------------")
            
            // 只输出需要调整的号码，按注额从大到小排序
            var totalReduction = 0
            adjustments.entries
                .filter { it.value > 0 }
                .sortedByDescending { it.value }
                .forEach { (number, reduction) ->
                    appendLine("${number}各${reduction} 元")
                    totalReduction += reduction
                }
            
            // 添加总计部分
            appendLine("\n== 总计 ==")
            appendLine("    $totalReduction 元")
        }
        // 创建分享 Intent
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, content)
        }
        
        // 启动分享选择器
        context.startActivity(Intent.createChooser(shareIntent, "分享到"))
    }

//    fun shareOriginalData(context: Context, number: Int, showMacau: Boolean, showHongKong: Boolean) {
//        val dbHelper = DatabaseHelper(context)
//        val content = buildString {
//            // 构建地区字符串
//            val regions = mutableListOf<String>()
//            if (showMacau) regions.add("澳门")
//            if (showHongKong) regions.add("香港")
//            val regionsStr = if (regions.size == 2) "" else "${regions.joinToString("")}的"
//
//            appendLine("=== 号码 $number 的${regionsStr}相关数据 ===\n")
//
//            // 获取总计数据
//            val macauTotal = if (showMacau) DatabaseUtils.getNumberStats(context, number, true, false).macauAmount else 0
//            val hongKongTotal = if (showHongKong) DatabaseUtils.getNumberStats(context, number, false, true).hongKongAmount else 0
//
//            dbHelper.readableDatabase.use { db ->
//                // 构建地区筛选条件
//                var selection = "特码 = ?"
//                val selectionArgs = mutableListOf(number.toString())
//
//                if (!showMacau && !showHongKong) {
//                    return@buildString  // 如果都不显示，直接返回空字符串
//                } else if (!showMacau) {
//                    selection += " AND 地区 = '香港'"
//                } else if (!showHongKong) {
//                    selection += " AND 地区 = '澳门'"
//                }
//
//                val cursor = db.query(
//                    DatabaseHelper.TABLE_NAME,
//                    arrayOf("时间戳", "原始数据", "地区", "注额"),
//                    selection,
//                    selectionArgs.toTypedArray(),
//                    null,
//                    null,
//                    "时间戳 ASC"
//                )
//
//                cursor.use {
//                    while (it.moveToNext()) {
//                        val timestamp = it.getString(0)
//                        val originalData = it.getString(1)
//                        val tag = it.getString(2)
//                        val amount = it.getInt(3)
//
//                        appendLine("时间: $timestamp")
//                        appendLine("地区: $tag")
//                        appendLine("金额: $amount")
//                        appendLine("原始数据:")
//                        appendLine(originalData)
//                        appendLine("-".repeat(50))
//                        appendLine()
//                    }
//                }
//            }
//
//            // 添加特殊投注统计
//            val numberData = DatabaseUtils.getNumberStats(context, number, showMacau, showHongKong)
//            if (numberData.specialBets.isNotEmpty()) {
//                appendLine("\n=== 特殊投注统计 ===")
//                numberData.specialBets.forEach { (type, amount) ->
//                    appendLine("$type: $amount 元")
//                }
//            }
//
//            // 添加总计部分
//            appendLine("\n=== 总计 ===")
//            if (showMacau && macauTotal > 0) appendLine("澳门总计: $macauTotal 元")
//            if (showHongKong && hongKongTotal > 0) appendLine("香港总计: $hongKongTotal 元")
//        }
//
//        // 创建分享 Intent
//        val shareIntent = Intent().apply {
//            action = Intent.ACTION_SEND
//            type = "text/plain"
//            putExtra(Intent.EXTRA_TEXT, content)
//        }
//
//        // 启动分享选择器
//        context.startActivity(Intent.createChooser(shareIntent, "分享到"))
//    }

    // 修改验证开奖号码的函数
//    private fun validateWinningNumbers(numbers: String): Pair<Boolean, List<Int>> {
//        try {
//            // 清理输入字符串，移除所有空白字符和分隔符
//            val cleanNumbers = numbers.replace(Regex("[,，\\s]+"), " ").trim()
//            val numberList = cleanNumbers.split(" ").map { it.toInt() }
//
//            // 验证号码数量（允许1个特码或7个完整号码）
//            if (numberList.isEmpty() || (numberList.size != 1 && numberList.size != 7)) {
//                return false to emptyList()
//            }
//
//            // 验证号码范围
//            if (numberList.any { it !in 1..49 }) {
//                return false to emptyList()
//            }
//
//            // 如果是7个号码，检查是否有重复
//            if (numberList.size == 7 && numberList.toSet().size != 7) {
//                return false to emptyList()
//            }
//
//            return true to numberList
//        } catch (e: Exception) {
//            e.printStackTrace()
//            return false to emptyList()
//        }
//    }

    // 修改 shareWinningData 函数,添加标识选择功能
    fun shareWinningData(
        context: Context,
        inputNumbers: String,
        showMacau: Boolean,
        showHongKong: Boolean,
        selectedTags: Set<String>? = null  // 添加标识参数
    ) {
        try {
            // 改进号码解析逻辑
            val numbers = inputNumbers
                .replace("，", ",") // 统一中英文逗号
                .split(Regex("[,\\s]+")) // 按逗号或空格分割
                .map { it.trim() } // 去除空白
                .filter { it.isNotEmpty() } // 过滤空字符串
                .mapNotNull { 
                    try {
                        it.toInt().takeIf { num -> num in 1..49 }
                    } catch (e: NumberFormatException) {
                        Log.w(TAG, "无效号码: $it")
                        null
                    }
                }

            if (numbers.isEmpty()) {
                Log.e(TAG, "无效的输入号码: $inputNumbers")
                Toast.makeText(context, "请输入有效号码", Toast.LENGTH_SHORT).show()
                return
            }

            // 修改这里的逻辑：只有在 selectedTags 为 null 且不是从对话框调用时才显示对话框
            if (selectedTags == null && !isFromDialog) {
                isFromDialog = true  // 设置标志，防止循环
                showTagSelectionDialog(context, numbers, showMacau, showHongKong)
                isFromDialog = false  // 重置标志
                return
            }

            // 获取开奖号码和生肖
            val winningNumbers = numbers.joinToString(",") // 获取开奖号码
            val winningZodiacs = numbers.mapNotNull { ZodiacUtils.getZodiacForNumber(it) } // 获取生肖
            
            val content = buildString {
                // 添加开奖信息头部
                appendLine("=== 开奖码: $winningNumbers ===")
                appendLine("对应生肖：${winningZodiacs.joinToString("")}")
                appendLine()

                DatabaseHelper(context).readableDatabase.use { db ->
                    if (showMacau) {
                        processRegionBets(context, db, "澳门", numbers.last(), numbers, winningZodiacs.toSet(), this, selectedTags)
                    }
                    if (showHongKong) {
                        processRegionBets(context, db, "香港", numbers.last(), numbers, winningZodiacs.toSet(), this, selectedTags)
                    }
                }
            }

            // 如果没有生成任何内容，显示提示
            if (content.isBlank()) {
                Toast.makeText(context, "没有找到相关中奖记录", Toast.LENGTH_SHORT).show()
                return
            }

            // 创建分享 Intent
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, content)
            }
            context.startActivity(Intent.createChooser(shareIntent, "分享到"))

        } catch (e: Exception) {
            Log.e(TAG, "处理过程中发生错误", e)
            Toast.makeText(context, "导出失败：${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    // 修改标识选择对话框
    private fun showTagSelectionDialog(
        context: Context,
        numbers: List<Int>,
        showMacau: Boolean,
        showHongKong: Boolean
    ) {
        // 获取所有标识
        val identifiers = mutableSetOf<String>()
        DatabaseHelper(context).readableDatabase.use { db ->
            val regions = mutableListOf<String>()
            if (showMacau) regions.add("澳门")
            if (showHongKong) regions.add("香港")
            
            val query = """
                SELECT DISTINCT 标识 
                FROM ${DatabaseHelper.TABLE_NAME} 
                WHERE 标识 IS NOT NULL 
                AND 标识 != ''
                AND 地区 IN (${regions.joinToString(",") { "'$it'" }})
            """.trimIndent()
            
            db.rawQuery(query, null)?.use { cursor ->
                while (cursor.moveToNext()) {
                    cursor.getString(0)?.let { identifiers.add(it) }
                }
            }
        }

        // 显示多选对话框
        val displayIdentifiers = mutableListOf("无标识数据") + identifiers.toList()
        val checkedItems = BooleanArray(displayIdentifiers.size) { false }
        
        android.app.AlertDialog.Builder(context)
            .setTitle("选择标识")
            .setMultiChoiceItems(displayIdentifiers.toTypedArray(), checkedItems) { _, which, isChecked ->
                checkedItems[which] = isChecked
            }
            .setPositiveButton("详细导出") { _, _ ->
                val selectedIdentifiers = mutableSetOf<String>()
                displayIdentifiers.forEachIndexed { index, identifier ->
                    if (checkedItems[index]) {
                        if (identifier == "无标识数据") {
                            selectedIdentifiers.add("")
                        } else {
                            selectedIdentifiers.add(identifier)
                        }
                    }
                }
                
                if (selectedIdentifiers.isEmpty()) {
                    Toast.makeText(context, "请至少选择一个选项", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }
                
                // 使用选中的标识调用详细导出
                shareWinningData(
                    context,
                    numbers.joinToString(" "),
                    showMacau,
                    showHongKong,
                    selectedIdentifiers
                )
            }
            .setNeutralButton("简化导出") { _, _ ->
                val selectedIdentifiers = mutableSetOf<String>()
                displayIdentifiers.forEachIndexed { index, identifier ->
                    if (checkedItems[index]) {
                        if (identifier == "无标识数据") {
                            selectedIdentifiers.add("")
                        } else {
                            selectedIdentifiers.add(identifier)
                        }
                    }
                }
                
                if (selectedIdentifiers.isEmpty()) {
                    Toast.makeText(context, "请至少选择一个选项", Toast.LENGTH_SHORT).show()
                    return@setNeutralButton
                }
                
                // 显示导出格式选择对话框
                showExportFormatDialog(
                    context,
                    numbers.joinToString(" "),
                    showMacau,
                    showHongKong,
                    selectedIdentifiers
                )
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示导出格式选择对话框
     */
    private fun showExportFormatDialog(
        context: Context,
        numbers: String,
        showMacau: Boolean,
        showHongKong: Boolean,
        selectedIdentifiers: Set<String>
    ) {
        val options = arrayOf("图片导出", "TXT导出")
        
        android.app.AlertDialog.Builder(context)
            .setTitle("选择导出格式")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> shareSimplifiedWinningData(
                        context, numbers, showMacau, showHongKong, selectedIdentifiers
                    )
                    1 -> shareSimplifiedWinningDataAsTXT(
                        context, numbers, showMacau, showHongKong, selectedIdentifiers
                    )
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 简化导出TXT版本
     */
    fun shareSimplifiedWinningDataAsTXT(
        context: Context,
        numbers: String,
        showMacau: Boolean,
        showHongKong: Boolean,
        selectedIdentifiers: Set<String>
    ) {
        try {
            // 使用FileProvider而不是MediaStore来避免API级别问题
            val exportDir = File(context.getExternalFilesDir(null), "exports") // 获取外部存储目录
            exportDir.mkdirs() // 创建目录

            // 使用标识作为文件名
            val identifier = selectedIdentifiers.joinToString("_") // 将标识连接成字符串
            val file = File(exportDir, "${identifier}_中奖数据.txt") // 创建文件
            
            // 创建StringBuider来构建内容
            val sb = StringBuilder() // 创建StringBuilder
            
            // 解析开奖号
            val winningNumbers = numbers.split(" ").mapNotNull { it.toIntOrNull() } // 将字符串分割成整数列表
            if (winningNumbers.isEmpty()) {
                Toast.makeText(context, "无效的开奖号码", Toast.LENGTH_SHORT).show()
                return
            }
            
            val specialNumber = winningNumbers.lastOrNull() ?: 0 // 获取特码
            val firstSixNumbers = winningNumbers.dropLast(1) // 获取前6个号码
            
            // 写入开奖号码
            sb.appendLine("开奖号码: $numbers") // 写入开奖号码
            
            // 使用ZodiacUtils获取当前年份
            val currentYear = ZodiacUtils.getCurrentBaseYear(context) // 获取当前年份
            
            // 获取当前生肖
            val currentZodiac = ZodiacUtils.getZodiacForYear(currentYear) // 获取当前生肖
            
            // 获取开奖生肖
            val zodiacMappings = ZodiacUtils.getZodiacMappings() // 获取生肖映射
            // 修改为包含所有七位开奖号码
            val allWinningNumbers = firstSixNumbers + specialNumber // 获取所有七位开奖号码
            val winningZodiacs = allWinningNumbers.mapNotNull { number ->
                zodiacMappings.entries.find { (_, numbers) -> 
                    numbers.contains(number) 
                }?.key
            }.toSet()
            
            // 写入生肖信息
            // sb.appendLine("当前年份: ${currentYear}年 (${currentZodiac}年)")
            sb.appendLine("对应生肖: ${allWinningNumbers.map { num -> zodiacMappings.entries.find { it.value.contains(num) }?.key }.joinToString(", ")}") // 写入生肖信息
            // sb.appendLine("特码: $specialNumber")
            sb.appendLine()
            
            // 获取数据库
            val dbHelper = DatabaseHelper(context)
            val db = dbHelper.readableDatabase
            
            // 处理不同地区的数据
            if (showMacau) {
                sb.appendLine("==== 澳门数据 ====")
                
                // 修改processRegionBets函数的调用，使其添加分隔线
                val originalSbLength = sb.length
                processRegionBets(
                    context, db, "澳门", specialNumber, 
                    firstSixNumbers, winningZodiacs.toSet(), sb, 
                    selectedIdentifiers, true // 使用简化模式
                )
                sb.appendLine()
            }
            
            if (showHongKong) {
                sb.appendLine("==== 香港数据 ====")
                processRegionBets(
                    context, db, "香港", specialNumber, 
                    firstSixNumbers, winningZodiacs.toSet(), sb, 
                    selectedIdentifiers, true // 使用简化模式
                )
            }
            
            // 写入文件
            FileWriter(file).use { writer ->
                writer.write(sb.toString())
            }
            
            // 获取文件 URI
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file
            )
            
            // 分享文件
            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_STREAM, uri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            context.startActivity(Intent.createChooser(intent, "分享中奖数据"))
            
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(context, "导出失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    // 定义一个更完整的数据类来存储中奖信息
    data class WinningRecord(
        val index: Int,       // 序号
        val type: String,     // 中奖类型
        val amount: Int,      // 原始投注金额
        val odds: Double,     // 赔率
        val payout: Double    // 赔付金额
    )

    // 新增辅助函数：根据标识选择返回返水率
    private fun getRebateValue(context: Context, selectedTags: Set<String>?): Float {
        // 多个标识或未选，使用全局返水
        if (selectedTags == null || selectedTags.size != 1) {
            val prefs = context.getSharedPreferences("settings", Context.MODE_PRIVATE)
            return prefs.getFloat("rebate_value", 0.0f)
        }
        // 单个标识，优先用标识返水
        val tag = selectedTags.first()
        if (tag.isEmpty()) return 0f
        val prefs = context.getSharedPreferences("identifier_prefs", Context.MODE_PRIVATE)
        return prefs.getFloat("identifier_rebates_$tag", 0.0f)
    }

    private fun processRegionBets(
        context: Context,
        db: SQLiteDatabase,
        region: String,
        specialNumber: Int,
        firstSixNumbers: List<Int>,
        winningZodiacs: Set<String>,
        sb: StringBuilder,
        selectedTags: Set<String>? = null,
        simplifiedMode: Boolean = false  // 添加简化模式参数
    ) {
        try {
            // 构建标识条件
            val identifierCondition = if (selectedTags != null && selectedTags.isNotEmpty()) {
                val conditions = mutableListOf<String>()
                if (selectedTags.any { it.isNotEmpty() }) {
                    conditions.add("标识 IN (${selectedTags.filter { it.isNotEmpty() }.joinToString(",") { "'$it'" }})")
                }
                if (selectedTags.contains("")) {
                    conditions.add("(标识 IS NULL OR 标识 = '')")
                }
                if (conditions.isEmpty()) "" else "AND (${conditions.joinToString(" OR ")})"
            } else ""
            
            // 获取所有原始数据并按时间排序，用于生成全局序号
            val allDataQuery = """
                SELECT DISTINCT 原始数据, 时间戳, 地区
                FROM ${DatabaseHelper.TABLE_NAME}
                WHERE 原始数据 IS NOT NULL AND 原始数据 != ''
                $identifierCondition
                ORDER BY 时间戳 ASC
            """
            
            // 创建原始数据到序号的映射，包含地区和时间戳信息
            val dataToIndex = mutableMapOf<Triple<String, String, String>, Int>() // Triple<原始数据, 地区, 时间戳> -> 序号
            db.rawQuery(allDataQuery, null)?.use { cursor ->
                var index = 1  // 从1开始计数
                while (cursor.moveToNext()) {
                    val originalData = cursor.getString(0)
                    val timestamp = cursor.getString(1)
                    val dataRegion = cursor.getString(2)
                    if (!originalData.isNullOrEmpty()) {
                        // 使用原始数据、地区和时间戳作为键
                        dataToIndex[Triple(originalData, dataRegion, timestamp)] = index++
                    }
                }
            }
            
            var totalBets = 0.0
            var totalPayouts = 0.0
            var specialNumberPayouts = 0.0  // 特码派彩
            var zodiacPayouts = 0.0        // 平特派彩
            
            // 用于存储简化模式数据
            val simplifiedData = mutableListOf<WinningRecord>()
            
            // 获取每个序号对应的总投注额
            val indexToBetInfo = mutableMapOf<Int, Pair<Double, Boolean>>() // 序号 -> (投注总额, 是否已显示)

            // 查询所有原始数据的总投注额，考虑时间戳
            db.rawQuery("""
                SELECT 原始数据, 地区, 时间戳, SUM(CASE 
                    WHEN 特码 IS NOT NULL THEN 注额
                    WHEN 特殊组合注额 IS NOT NULL THEN 特殊组合注额
                    ELSE 0 
                END) as total
                FROM ${DatabaseHelper.TABLE_NAME}
                WHERE 地区 = ?
                $identifierCondition
                GROUP BY 原始数据, 地区, 时间戳
            """.trimIndent(), arrayOf(region))?.use { cursor ->
                while (cursor.moveToNext()) {
                    val originalData = cursor.getString(0)
                    val dataRegion = cursor.getString(1)
                    val timestamp = cursor.getString(2)
                    val total = cursor.getDouble(3)
                    val index = dataToIndex[Triple(originalData, dataRegion, timestamp)] ?: 0
                    if (index > 0) {
                        indexToBetInfo[index] = Pair(total, false)
                    }
                }
            }
            
            // 获取区域总投注额
            val totalQuery = """
                SELECT SUM(CASE 
                    WHEN 特码 IS NOT NULL THEN 注额
                    WHEN 特殊组合注额 IS NOT NULL THEN 特殊组合注额
                    ELSE 0 
                END) as total
                FROM ${DatabaseHelper.TABLE_NAME}
                WHERE 地区 = ?
                $identifierCondition
            """.trimIndent()
            
            var regionTotalBets = 0.0  // 确保定义这个变量
            db.rawQuery(totalQuery, arrayOf(region))?.use { cursor ->
                if (cursor.moveToFirst()) {
                    regionTotalBets = cursor.getDouble(0)
                }
            }
            
            // 修改中奖记录查询，添加标识条件
            val query = """
                SELECT 特码, 注额, 时间戳, 地区, 原始数据, 特殊组合注额, 特殊组合类型, 标识, 投注类型, 号码列表
                FROM ${DatabaseHelper.TABLE_NAME}
                WHERE (特码 = ? OR 特殊组合类型 IS NOT NULL)
                AND 地区 = ?
                $identifierCondition
                ORDER BY 时间戳 ASC
            """.trimIndent()
            
            // 确保数字转换安全
            db.rawQuery(query, arrayOf(specialNumber.toString(), region))?.use { cursor ->
                while (cursor.moveToNext()) {
                    try {
                        val betAmount = cursor.getInt(cursor.getColumnIndexOrThrow("注额"))
                        val specialType = cursor.getString(cursor.getColumnIndexOrThrow("特殊组合类型"))
                        val specialAmount = cursor.getInt(cursor.getColumnIndexOrThrow("特殊组合注额"))
                        val originalData = cursor.getString(cursor.getColumnIndexOrThrow("原始数据"))
                        val timestamp = cursor.getString(cursor.getColumnIndexOrThrow("时间戳"))
                        val source = cursor.getString(cursor.getColumnIndexOrThrow("标识")) ?: ""
                        val betType = cursor.getString(cursor.getColumnIndexOrThrow("投注类型"))
                        val numberListStr = cursor.getString(cursor.getColumnIndexOrThrow("号码列表"))
                        
                        // 获取原始数据序号，使用原始数据、地区和时间戳作为键
                        val dataIndex = dataToIndex[Triple(originalData, region, timestamp)] ?: 0
                        
                        // 在处理每条记录时，先判断是否是特码中奖
                        if (cursor.getInt(cursor.getColumnIndexOrThrow("特码")) == specialNumber) {
                            // 使用设置中的特码赔率
                            val specialNumberOdds = BettingOdds.getCurrentSettings().normalOdds["特码"] ?: 47.0
                            val payout = betAmount * specialNumberOdds
                            totalBets += betAmount
                            totalPayouts += payout
                            specialNumberPayouts += payout
                            
                            // 如果是简化模式，仅收集数据
                            if (simplifiedMode) {
                                simplifiedData.add(WinningRecord(
                                    index = dataIndex,
                                    type = "特码",
                                    amount = betAmount,
                                    odds = specialNumberOdds,
                                    payout = payout
                                ))
                            } else {
                                sb.appendLine("----- ${region} 特码 $specialNumber(${ZodiacUtils.getZodiacForNumber(specialNumber)}) -----")
                                sb.appendLine("第 $dataIndex 条")  // 添加序号信息
                                sb.appendLine("时间: $timestamp")
                                sb.appendLine("标识: $region")
                                sb.appendLine("标签: $source")
                                sb.appendLine("原始数据: $originalData")
                                sb.appendLine("中奖金额: $betAmount")
                                sb.appendLine("赔率: $specialNumberOdds")
                                sb.appendLine("赔付: $payout")
                                sb.appendLine("----------------------------------------------")
                            }
                        } else if (!specialType.isNullOrEmpty() && specialAmount > 0) {
                            // 然后处理特殊投注类型
                            val betType = cursor.getString(cursor.getColumnIndexOrThrow("投注类型"))
                            val specialType = cursor.getString(cursor.getColumnIndexOrThrow("特殊组合类型"))
                            val specialAmount = cursor.getInt(cursor.getColumnIndexOrThrow("特殊组合注额"))
                            val originalData = cursor.getString(cursor.getColumnIndexOrThrow("原始数据"))
                            val timestamp = cursor.getString(cursor.getColumnIndexOrThrow("时间戳"))
                            val source = cursor.getString(cursor.getColumnIndexOrThrow("标识")) ?: ""
                            val numberListStr = cursor.getString(cursor.getColumnIndexOrThrow("号码列表"))
                            
                            // 只处理有类型和金额的特殊投注
                            if ((!specialType.isNullOrEmpty() || !betType.isNullOrEmpty()) && specialAmount > 0) {
                                // 获取实际的投注类型
                                val actualType = betType.takeIf { !it.isNullOrEmpty() } ?: specialType ?: ""
                                
                                // 输出日志，帮助调试
                                Log.d("ExportUtils", "处理投注类型: $actualType, betType=$betType, specialType=$specialType, 投注号码=$numberListStr")
                                
                                // 解析投注号码列表（如果有）
                                val betNumbers = if (!numberListStr.isNullOrEmpty()) {
                                    try {
                                        numberListStr.split(",").mapNotNull { 
                                            it.trim().toIntOrNull()?.takeIf { num -> num in 1..49 } 
                                        }.toSet()
                                    } catch (e: Exception) {
                                        Log.e("ExportUtils", "解析号码列表错误: $numberListStr", e)
                                        emptySet()
                                    }
                                } else emptySet()
                                
                                // 使用辅助函数或变量明确区分哪些玩法使用前六位，哪些使用全部七位
//                                val numbersForXZX = firstSixNumbers  // 用于x中x类玩法
                                val numbersForAll = firstSixNumbers + specialNumber  // 用于需要全部号码的玩法
                                
                                // 定义变量跟踪中奖状态
                                var isWin = false
                                var odds = 0.0
                                var matchingNumbers = emptySet<Int>()
                                var matchingZodiacs = emptySet<String>()
                                
                                // 根据不同类型处理逻辑
                                when {
                                    // 二中二 - 投注2个号码,需要全中
                                    actualType == "二中二" || betType == "二中二" -> {
                                        // 检查前6个号码，需要在2个投注号码中命中2个
                                        val frontSixOnly = firstSixNumbers.take(6)
                                        matchingNumbers = betNumbers.filter { it in frontSixOnly }.toSet()
                                        isWin = betNumbers.size == 2 && matchingNumbers.size == 2
                                        odds = BettingOdds.getCurrentSettings().normalOdds["二中二"] ?: 60.0
                                        Log.d("ExportUtils", "二中二计算: betSize=${betNumbers.size}, matchSize=${matchingNumbers.size}, isWin=$isWin")
                                    }
                                    
                                    // 三中二 - 投注3个号码,需要中至少2个
                                    actualType == "三中二" || betType == "三中二" -> {
                                        // 从多个来源尝试获取投注号码
                                        val finalBetNumbers = when {
                                            // 1. 优先从号码列表中获取
                                            !numberListStr.isNullOrEmpty() -> {
                                                numberListStr.split(",").mapNotNull { 
                                                    it.trim().toIntOrNull()?.takeIf { num -> num in 1..49 } 
                                                }
                                            }
                                            // 2. 从特殊组合类型中获取
                                            specialType?.startsWith("三中二_") == true -> {
                                                specialType.substringAfter("_").split("-").mapNotNull { 
                                                    it.trim().toIntOrNull()?.takeIf { num -> num in 1..49 } 
                                                }
                                            }
                                            // 3. 从原始数据中提取
                                            originalData.contains("三中二") -> {
                                                val numberPattern = "\\b(\\d{1,2})\\b".toRegex()
                                                numberPattern.findAll(originalData)
                                                    .map { it.groupValues[1].toIntOrNull() }
                                                    .filterNotNull()
                                                    .filter { it in 1..49 }
                                                    .take(3)
                                                    .toList()
                                            }
                                            // 4. 如果还有其他号码来源
                                            !betNumbers.isEmpty() -> betNumbers.toList()
                                            else -> emptyList()
                                        }
                                        if (finalBetNumbers.size == 3) {
                                            // 确保只使用前6位开奖号码
                                            val frontSixOnly = firstSixNumbers.take(6)
                                            // 检查前6个号码中命中了几个
                                            matchingNumbers = finalBetNumbers.filter { it in frontSixOnly }.toSet()
                                            
                                            // 修正：三中二是指投注3个号码，中了至少2个(包括全中3个的情况)
                                            isWin = matchingNumbers.size >= 2
                                            
                                            odds = BettingOdds.getCurrentSettings().normalOdds["三中二"] ?: 19.0
                                        } else {
                                            isWin = false
                                            matchingNumbers = emptySet()
                                        }
                                    }
                                    
                                    // 三中三 - 投注3个号码,需要全中
                                    actualType == "三中三" -> {
                                        // 明确只使用前6个号码，不包含特码
                                        val frontSixOnly = firstSixNumbers.take(6)
                                        matchingNumbers = betNumbers.filter { it in frontSixOnly }.toSet()
                                        isWin = betNumbers.size == 3 && matchingNumbers.size == 3
                                        odds = BettingOdds.getCurrentSettings().normalOdds["三中三"] ?: 600.0
                                    }
                                    
                                    // 平特尾 - 投注的号码任一与开奖号码匹配
                                    actualType == "平特尾" -> {
                                        matchingNumbers = betNumbers.filter { it in numbersForAll }.toSet()
                                        isWin = matchingNumbers.isNotEmpty()
                                        odds = BettingOdds.getCurrentSettings().normalOdds["平特尾"] ?: 1.8
                                    }
                                    
                                    // 不中类型 - 投注的号码都不在开奖号码中
                                    actualType.endsWith("不中") -> {
                                        // 从号码列表或特殊组合类型中提取投注号码
                                        val betNumbersList = if (!numberListStr.isNullOrEmpty()) {
                                            numberListStr.split(",").mapNotNull { 
                                                it.trim().toIntOrNull()?.takeIf { num -> num in 1..49 } 
                                            }
                                        } else if (specialType?.contains("_") == true) {
                                            specialType.substringAfter("_").split("-").mapNotNull { 
                                                it.trim().toIntOrNull()?.takeIf { num -> num in 1..49 } 
                                            }
                                        } else emptyList()

                                        // 检查投注号码数量是否符合要求
                                        val requiredNumbers = when (actualType) {
                                            "五不中" -> 5
                                            "六不中" -> 6
                                            "七不中" -> 7
                                            "八不中" -> 8
                                            "九不中" -> 9
                                            "十不中" -> 10
                                            "十一不中" -> 11
                                            else -> 0
                                        }

                                        // 验证投注号码数量和中奖条件
                                        if (betNumbersList.size == requiredNumbers) {
                                            val allWinningNumbers = firstSixNumbers + specialNumber
                                            isWin = betNumbersList.none { it in allWinningNumbers }
                                            matchingNumbers = betNumbersList.toSet()
                                            
                                            // 设置对应赔率
                                            odds = BettingOdds.getCurrentSettings().normalOdds[actualType] ?: when (actualType) {
                                                "五不中" -> 2.0
                                                "六不中" -> 2.4
                                                "七不中" -> 2.8
                                                "八不中" -> 3.4
                                                "九不中" -> 4.1
                                                "十不中" -> 5.0
                                                "十一不中" -> 6.0
                                                else -> 0.0
                                            }
                                        }
                                    }
                                    
                                    // 平特 - 投注生肖在开奖号码生肖中
                                    actualType == "平特" -> {
                                        // 从specialType中提取投注的生肖
                                        val zodiac = specialType?.substringAfter("平特_") ?: ""
                                        // 检查是否包含当年生肖
                                        val currentYearZodiac = ZodiacUtils.getZodiacForYear(ZodiacUtils.getCurrentBaseYear(context))
                                        val hasCurrentYearZodiac = currentYearZodiac == zodiac
                                        // 使用对应赔率
                                        odds = if (hasCurrentYearZodiac) {
                                            BettingOdds.getCurrentSettings().currentYearOdds["平特"] ?: 1.8
                                        } else {
                                            BettingOdds.getCurrentSettings().normalOdds["平特"] ?: 2.0
                                        }
                                        
                                        // 判断是否中奖 - winningZodiacs已经包含所有七位开奖号码的生肖
                                        isWin = winningZodiacs.contains(zodiac)
                                        matchingZodiacs = if (isWin) setOf(zodiac) else emptySet()
                                    }
                                    
                                    // 特肖 - 投注生肖与特码生肖匹配
                                    actualType == "特肖" -> {
                                        // 从specialType中提取投注的生肖
                                        val zodiac = specialType?.substringAfter("特肖_") ?: ""
                                        val specialZodiac = ZodiacUtils.getZodiacForNumber(specialNumber)
                                        // 检查是否包含当年生肖
                                        val currentYearZodiac = ZodiacUtils.getZodiacForYear(ZodiacUtils.getCurrentBaseYear(context))
                                        val hasCurrentYearZodiac = currentYearZodiac == zodiac
                                        // 使用对应赔率
                                        odds = if (hasCurrentYearZodiac) {
                                            BettingOdds.getCurrentSettings().currentYearOdds["特肖"] ?: 9.0
                                        } else {
                                            BettingOdds.getCurrentSettings().normalOdds["特肖"] ?: 11.0
                                        }
                                        // 判断是否中奖
                                        isWin = specialZodiac == zodiac
                                        matchingZodiacs = if (isWin) setOf(zodiac) else emptySet()
                                    }
                                    
                                    // 连肖类型 - 投注的所有生肖都在开奖号码生肖中
                                    actualType.matches(Regex("[2-5]连肖")) -> {
                                        // 从specialType中提取投注的多个生肖
                                        val zodiacs = specialType?.substringAfter("_")?.chunked(1)?.toSet() ?: emptySet()
                                        // 检查是否包含当年生肖
                                        val currentYearZodiac = ZodiacUtils.getZodiacForYear(ZodiacUtils.getCurrentBaseYear(context))
                                        val hasCurrentYearZodiac = currentYearZodiac?.let { zodiacs.contains(it) } ?: false
                                        // 使用对应赔率
                                        odds = if (hasCurrentYearZodiac) {
                                            BettingOdds.getCurrentSettings().currentYearOdds[actualType] ?: 0.0
                                        } else {
                                            BettingOdds.getCurrentSettings().normalOdds[actualType] ?: 0.0
                                        }
                                        // 判断是否中奖
                                        isWin = zodiacs.all { winningZodiacs.contains(it) }
                                        matchingZodiacs = if (isWin) zodiacs else emptySet()
                                    }
                                    
                                    // 包类型 - 特码符合条件
                                    actualType.startsWith("包") -> {
                                        // 获取投注的号码列表
                                        val betNumbersList = if (!numberListStr.isNullOrEmpty()) {
                                            numberListStr.split(",").mapNotNull { 
                                                it.trim().toIntOrNull()?.takeIf { num -> num in 1..49 } 
                                            }.toSet()
                                        } else emptySet()

                                        // 根据不同的包类型判断
                                        val matchingCondition = when {
                                            actualType == "包双" -> specialNumber % 2 == 0 && specialNumber in betNumbersList
                                            actualType == "包单" -> specialNumber % 2 == 1 && specialNumber in betNumbersList
                                            actualType == "包合双" -> {
                                                val sum = (specialNumber / 10 + specialNumber % 10)
                                                sum % 2 == 0 && specialNumber in betNumbersList
                                            }
                                            actualType == "包合单" -> {
                                                val sum = (specialNumber / 10 + specialNumber % 10)
                                                sum % 2 == 1 && specialNumber in betNumbersList
                                            }
                                            // 处理波色单双组合
                                            actualType.matches(Regex("包[红绿蓝](波)?(单|双)?(数)?")) -> {
                                                // 提取波色
                                                val color = when {
                                                    actualType.contains("红") -> DataParser.RED_NUMBERS
                                                    actualType.contains("绿") -> DataParser.GREEN_NUMBERS
                                                    actualType.contains("蓝") -> DataParser.BLUE_NUMBERS
                                                    else -> emptySet()
                                                }
                                                
                                                // 判断特码是否在该波色中
                                                val isCorrectColor = specialNumber in color
                                                
                                                // 判断是否有单双条件
                                                val hasSingleOrDouble = actualType.contains("单") || actualType.contains("双")
                                                
                                                // 判断单双条件
                                                val isCorrectType = if (!hasSingleOrDouble) {
                                                    true  // 如果没有单双条件，则认为符合
                                                } else if (actualType.contains("双")) {
                                                    specialNumber % 2 == 0
                                                } else {
                                                    specialNumber % 2 == 1
                                                }
                                                
                                                // 判断特码是否在投注号码列表中
                                                val isInBetNumbers = specialNumber in betNumbersList
                                                
                                                // 最终判断：颜色正确 + 单双正确(如适用) + 在投注号码中
                                                isCorrectColor && isCorrectType && isInBetNumbers
                                            }
                                            else -> false
                                        }
                                        
                                        isWin = matchingCondition
                                        matchingNumbers = if (isWin) setOf(specialNumber) else emptySet()
                                        
                                        // 获取对应赔率
                                        odds = when {
                                            actualType == "包双" || actualType == "包单" || 
                                            actualType == "包合双" || actualType == "包合单" -> {
                                                BettingOdds.getCurrentSettings().normalOdds["包双单合双单"] ?: 1.8
                                            }
                                            actualType.matches(Regex("包[红绿蓝]")) -> {
                                                BettingOdds.getCurrentSettings().normalOdds["包波色"] ?: 2.6
                                            }
                                             actualType.matches(Regex("包[红绿蓝](单|双)数")) -> {
                                                BettingOdds.getCurrentSettings().normalOdds["包波色双单"] ?: 5.0
                                            }
                                            else -> 0.0
                                        }
                                    }
                                }
                                
                                // 如果中奖，计算赔付
                                if (isWin) {
                                    // 精确计算赔付金额
                                    val payout = specialAmount * odds
                                    val roundedPayout = Math.round(payout * 100) / 100.0  // 保留两位小数
                                    
                                    totalBets += specialAmount
                                    totalPayouts += roundedPayout
                                    
                                    // 如果是简化模式，仅收集数据
                                    if (simplifiedMode) {
                                        simplifiedData.add(WinningRecord(
                                            index = dataIndex,
                                            type = actualType,
                                            amount = specialAmount,
                                            odds = odds,
                                            payout = roundedPayout
                                        ))
                                    } else {
                                        // 根据投注类型添加不同的输出
                                        when {
                                            // 数字类投注(x中x和不中类型)
                                            actualType == "二中二" || actualType == "三中二" || actualType == "三中三" || 
                                            betType == "二中二" || betType == "三中二" || betType == "三中三" ||
                                            actualType == "平特尾" || actualType.endsWith("不中") -> {
                                                zodiacPayouts += roundedPayout
                                                sb.appendLine("----- ${region} 开奖号码 ${firstSixNumbers.joinToString(" ")} -----")
                                                sb.appendLine("第 $dataIndex 条")  // 添加序号信息
                                                sb.appendLine("时间: $timestamp")
                                                sb.appendLine("标签: $region")
                                                sb.appendLine("来源: $source")
                                                sb.appendLine("原始数据: $originalData")
                                                sb.appendLine("中奖类型: $actualType")
                                                // 显示投注号码和匹配号码
                                                sb.appendLine("投注号码: ${betNumbers.joinToString(",")}")
                                                if (actualType == "二中二" || actualType == "三中二" || actualType == "三中三") {
                                                    sb.appendLine("匹配号码: ${matchingNumbers.joinToString(",")}")
                                                }
                                                sb.appendLine("中奖金额: $specialAmount")
                                                sb.appendLine("赔率: $odds")
                                                sb.appendLine("赔付: $roundedPayout")
                                                sb.appendLine("----------------------------------------------")
                                            }
                                            
                                            // 包类玩法
                                            actualType.startsWith("包") -> {
                                                zodiacPayouts += roundedPayout
                                                sb.appendLine("----- ${region} 特码 $specialNumber -----")
                                                sb.appendLine("第 $dataIndex 条")  // 添加序号信息
                                                sb.appendLine("时间: $timestamp")
                                                sb.appendLine("标签: $region")
                                                sb.appendLine("来源: $source")
                                                sb.appendLine("原始数据: $originalData")
                                                sb.appendLine("中奖类型: $actualType")
                                                sb.appendLine("中奖号码: $specialNumber")
                                                sb.appendLine("中奖金额: $specialAmount")
                                                sb.appendLine("赔率: $odds")
                                                sb.appendLine("赔付: $roundedPayout")
                                                sb.appendLine("----------------------------------------------")
                                            }
                                            
                                            // 生肖类投注
                                            else -> {
                                                zodiacPayouts += roundedPayout
                                                sb.appendLine("---${region}中奖生肖 ${winningZodiacs.joinToString("")} ---")
                                                sb.appendLine("第 $dataIndex 条")  // 添加序号信息
                                                sb.appendLine("时间: $timestamp")
                                                sb.appendLine("标签: $region")
                                                sb.appendLine("来源: $source")
                                                sb.appendLine("原始数据: $originalData")
                                                sb.appendLine("投注类型: $actualType")
                                                sb.appendLine("中奖生肖: ${matchingZodiacs.joinToString("")}")
                                                sb.appendLine("中奖金额: $specialAmount")
                                                sb.appendLine("赔率: $odds")
                                                sb.appendLine("赔付: $roundedPayout")
                                                sb.appendLine("----------------------------------------------")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        // Log.e("ExportUtils", "处理记录时发生错误", e)
                        continue // 跳过有问题的记录
                    }
                }
            }
            
            // 如果是简化模式，在这里添加简化输出
            if (simplifiedMode) {
                // 修复：确保有数据时才进行处理
                if (simplifiedData.isNotEmpty() || indexToBetInfo.isNotEmpty()) {
                    
                    // 获取中奖记录的序号
                    val winningIndices = simplifiedData.map { it.index }.toSet()
                    
                    // 1. 首先输出所有记录的总投注额
                    val sortedIndices = indexToBetInfo.keys.sorted()
                    
                    // 添加日志以便调试
                    // Log.d("ExportUtils", "找到 ${sortedIndices.size} 条投注记录, ${simplifiedData.size} 条中奖记录")
                    
                    // 2. 然后按序号顺序处理每条记录
                    for (index in sortedIndices) {
                        val (totalBet, _) = indexToBetInfo[index]!!
                        
                        // 查找对应序号的中奖记录
                        val winRecords = simplifiedData.filter { it.index == index }
                        
                        // 构建一行输出，包含标识-序号-总投注额，以及所有中奖信息
                        val line = StringBuilder("${region}-#$index-${totalBet.toInt()}")
                        
                        // 如果有中奖记录，在同一行添加中奖信息
                        if (winRecords.isNotEmpty()) {
                            line.append("      中 ")
                            
                            // 收集并合并所有中奖信息
                            val winInfos = winRecords.map { record ->
                                "${record.amount} ${record.type}"
                            }
                            
                            // 将所有中奖信息用空格连接起来
                            line.append(winInfos.joinToString(" "))
                        }
                        
                        // 输出这一行
                        sb.appendLine(line)
                        
                        // 添加分隔符，提高可读性
                        sb.appendLine("----------------------------------------")
                    }
                    
                    // 3. 添加汇总统计
                    val regionTotal = indexToBetInfo.values.sumOf { it.first } // 总投注额
                    val specialBets = simplifiedData.filter { it.type == "特码" } // 特码投注额
                    val zodiacBets = simplifiedData.filter { it.type != "特码" } // 平特投注额
                    
                    
                    val specialPayout = specialBets.sumOf { it.payout } // 特码派彩
                    val zodiacPayout = zodiacBets.sumOf { it.payout } // 平特派彩
                    val totalPayout = specialPayout + zodiacPayout 
                    
                    // 添加总计标题和汇总统计
                    sb.appendLine("\n=== ${region}盈亏统计 ===")
                    sb.appendLine("总数: ${regionTotalBets.toInt()}")
                    
                    if (specialBets.isNotEmpty()) {
                        val specialAmount = specialBets.sumOf { it.amount }
                        sb.appendLine("特码:(${specialAmount}) ${specialPayout.toInt()}")
                    }
                    
                    if (zodiacBets.isNotEmpty()) {
                        sb.appendLine("平特类:${zodiacPayout.toInt()}")
                    }
                    
                    // 计算返水（如有）
                    val rebateRate = getRebateValue(context, selectedTags)
                    if (rebateRate > 0) {
                        val rebateAmount = regionTotalBets * rebateRate / 100.0
                        sb.appendLine("返水(${rebateRate}%): ${rebateAmount.toInt()}")
                        // 计算最终盈亏
                        val profit = regionTotalBets - totalPayout - rebateAmount
                        val profitText = if (profit >= 0) "😊庄赢" else "😭庄输"
                        sb.appendLine("$profitText: ${Math.abs(profit).toInt()}") // 保留两位小数
                        sb.appendLine("----------------------------------------------")
                    } else {
                        // 不启用返水情况
                        val profit = regionTotalBets - totalPayout
                        val profitText = if (profit >= 0) "😊庄赢" else "😭庄输"
                        sb.appendLine("$profitText: ${Math.abs(profit).toInt()}") // 保留两位小数
                        sb.appendLine("----------------------------------------------")
                    }
                } else {
                    // 如果没有找到记录，添加提示信息
                    sb.appendLine("${region}：没有找到相关中奖记录")
                }
            }
            
            // 修改统计信息显示部分
            if (!simplifiedMode) {
                sb.appendLine("\n===== ${region}盈亏统计 =====")
                sb.appendLine("总数: ${regionTotalBets.toInt()}")
                
                if (totalPayouts > 0) {
                    if (specialNumberPayouts > 0) {
                        // 获取特码的总投注额和总注数，添加标识条件
                        val specialNumberStats = db.rawQuery("""
                            SELECT SUM(注额) as total, COUNT(*) as count
                            FROM ${DatabaseHelper.TABLE_NAME}
                            WHERE 特码 = ? AND 地区 = ?
                            $identifierCondition
                        """.trimIndent(), arrayOf(specialNumber.toString(), region))?.use { cursor ->
                            if (cursor.moveToFirst()) {
                                Pair(cursor.getInt(0), cursor.getInt(1))
                            } else Pair(0, 0)
                        } ?: Pair(0, 0)
                        
                        sb.appendLine("特码: (${specialNumberStats.first}/${specialNumberStats.second}注) ${specialNumberPayouts.toInt()}")
                    }
                    
                    if (zodiacPayouts > 0) {
                        // 获取平特的总投注额和总注数，添加标识条件
                        val zodiacStats = db.rawQuery("""
                            SELECT SUM(特殊组合注额) as total, COUNT(*) as count
                            FROM ${DatabaseHelper.TABLE_NAME}
                            WHERE 投注类型 IN ('平特', '特肖', '二连肖', '三连肖', '四连肖', '五连肖', '平特尾', '二中二', '三中三', 
                                            '三中二', '五不中', '六不中', '七不中', '八不中', '九不中', '十不中', '十一不中',
                                            '包单', '包双', '包合单', '包合双', 
                                            '包红波单', '包红波双', '包绿波单', '包绿波双', '包蓝波单', '包蓝波双')
                            AND 地区 = ?
                            $identifierCondition
                        """.trimIndent(), arrayOf(region))?.use { cursor ->
                            if (cursor.moveToFirst()) {
                                Pair(cursor.getInt(0), cursor.getInt(1))
                            } else Pair(0, 0)
                        } ?: Pair(0, 0)
                        
                        sb.appendLine("平特: ${zodiacPayouts.toInt()}")
                    }
                    
                    // 计算总派彩和盈亏
                    val totalPayout = specialNumberPayouts + zodiacPayouts
                    sb.appendLine("派彩: ${totalPayout.toInt()}")
                    
                    // 返水逻辑
                    val rebateRate = getRebateValue(context, selectedTags)
                    if (rebateRate > 0) {
                        val rebateAmount = regionTotalBets * rebateRate / 100.0
                        sb.appendLine("返水(${rebateRate}%): ${rebateAmount.toInt()}")
                        val profit = regionTotalBets - totalPayout - rebateAmount
                        val profitText = if (profit >= 0) "😊庄赢" else "😭庄输"
                        sb.appendLine("$profitText: ${Math.abs(profit).toInt()}")
                    } else {
                        val profit = regionTotalBets - totalPayout
                        val profitText = if (profit >= 0) "😊庄赢" else "😭庄输"
                        sb.appendLine("$profitText: ${Math.abs(profit).toInt()}")
                    }
                } else {
                    // 没有中奖情况
                    val rebateRate = getRebateValue(context, selectedTags)
                    if (rebateRate > 0) {
                        val rebateAmount = regionTotalBets * rebateRate / 100.0
                        sb.appendLine("返水(${rebateRate}%): ${rebateAmount.toInt()}")
                        val profit = regionTotalBets - rebateAmount
                        sb.appendLine("庄赢: ${profit.toInt()}")
                    } else {
                        sb.appendLine("庄赢: ${regionTotalBets.toInt()}")
                    }
                }
                sb.appendLine("----------------------------------------------")
            }
        } catch (e: Exception) {
            // Log.e("ExportUtils", "处理地区数据时发生错误: $region", e)
            sb.appendLine("处理 $region 数据时发生错误: ${e.message}")
        }
    }

    // 导出详细的原始数据和解析结果
    fun exportFilteredDataAsText(
        context: Context,
        dataList: List<DatabaseUtils.OriginalDataInfo>,
        onComplete: (Uri) -> Unit
    ) {
        try {
            // 创建导出文件
            val exportDir = File(context.getExternalFilesDir(null), "exports")
            exportDir.mkdirs()

            // 使用格式化的日期时间作为文件名
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
                .format(Date())
            val file = File(exportDir, "export_$timestamp.txt")

            // 按时间戳排序数据，确保最旧的在前面
            val sortedDataList = dataList.sortedBy { it.timestamp }

            // 计算总金额
            var grandTotal = 0
            
            // 写入数据
            FileWriter(file).use { writer ->
                sortedDataList.forEachIndexed { index, data ->
                    // 序号从1开始，最旧的为#1
                    val displayIndex = index + 1
                    
                    // 写入序号和时间信息
                    writer.append("#${displayIndex} ${data.timestamp} ${data.area}\n\n")
                    
                    // 写入原始数据
                    writer.append("${data.originalData}\n\n")
                    
                    // 写入分隔线和箭头
                    writer.append("------------------👇------------------\n\n")
                    
                    // 分别计算普通注额和特殊注额
                    var normalTotal = 0
                    var specialTotal = 0
                    
                    // 写入普通注额
                    val numberDetails = data.numberDetails.joinToString(" ") { detail ->
                        normalTotal += detail.amount
                        "${detail.number}:${detail.amount}"
                    }
                    if (numberDetails.isNotEmpty()) {
                        writer.append("$numberDetails\n\n")
                    }
                    
                    // 写入特殊注额（如果有）
                    data.specialBets.forEach { special ->
                        specialTotal += special.amount
                        writer.append("${special.type}:${special.amount}\n\n")
                    }
                    
                    // 计算这条记录的总金额
                    val recordTotal = normalTotal + specialTotal
                    grandTotal += recordTotal
                    
                    // 写入统计信息
                    if (normalTotal > 0) {
                        writer.append("特码类:$normalTotal(${data.numberDetails.size}注)\n\n")
                    }
                    if (specialTotal > 0) {
                        if (normalTotal > 0) writer.append(" ")
                        writer.append("平特类:$specialTotal(${data.specialBets.size}注)\n\n")
                    }
                    writer.append("总计:$recordTotal\n\n\n")
                    
                    // 添加分隔线
                    writer.append("+++++=========分割线=========+++++\n\n\n")
                }
                
                // 在文件末尾添加总体统计信息
                writer.append("总记录:${sortedDataList.size}\n")
                
                // 添加总计
                writer.append("总计: $grandTotal 元\n")
            }

            // 获取文件 URI
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file
            )
            
            onComplete(uri)
        } catch (e: Exception) {
            // Log.e("ExportUtils", "导出失败", e)
            Toast.makeText(context, "导出失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    // 修改函数签名，添加开奖号码参数
//    fun exportWinningData(
//        context: Context,
//        specialNumber: Int,
//        firstSixNumbers: List<Int>,  // 添加前6个开奖号码参数
//        onComplete: (Uri) -> Unit
//    ) {
//        try {
//            val sb = StringBuilder()
//            var totalBets = 0.0
//            var totalPayouts = 0.0
//            var specialNumberPayouts = 0.0
//            var zodiacPayouts = 0.0
//
//            // 获取开奖生肖
//            val winningZodiacs = (firstSixNumbers + specialNumber).mapNotNull {
//                ZodiacUtils.getZodiacForNumber(it)
//            }.toSet()
//
//            // 处理每个地区的投注
//            val dbHelper = DatabaseHelper(context)
//            dbHelper.readableDatabase.use { db ->
//                processRegionBets(context, db, "澳门", specialNumber, firstSixNumbers, winningZodiacs, sb)
//                processRegionBets(context, db, "香港", specialNumber, firstSixNumbers, winningZodiacs, sb)
//            }
//
//            // 如果没有生成任何内容，显示提示
//            if (sb.isBlank()) {
//                Toast.makeText(context, "没有找到相关中奖记录", Toast.LENGTH_SHORT).show()
//                return
//            }
//
//            // 创建分享 Intent
//            val shareIntent = Intent().apply {
//                action = Intent.ACTION_SEND
//                type = "text/plain"
//                putExtra(Intent.EXTRA_TEXT, sb.toString())
//            }
//            context.startActivity(Intent.createChooser(shareIntent, "分享到"))
//
//        } catch (e: Exception) {
//            Log.e(TAG, "处理过程中发生错误", e)
//            Toast.makeText(context, "导出失败：${e.message}", Toast.LENGTH_SHORT).show()
//        }
//    }

    // 添加一个获取所有标签的函数
//    fun getAllTags(context: Context): List<String> {
//        val tags = mutableListOf<String>()
//        DatabaseHelper(context).readableDatabase.use { db ->
//            db.rawQuery("""
//                SELECT DISTINCT 标识
//                FROM ${DatabaseHelper.TABLE_NAME}
//                ORDER BY 标识
//            """.trimIndent(), null)?.use { cursor ->
//                while (cursor.moveToNext()) {
//                    cursor.getString(0)?.let { tags.add(it) } ?: tags.add("")
//                }
//            }
//        }
//        return tags
//    }

    /**
     * 分享特殊关键字选中的号码数据
     */
    fun shareSpecialNumbersData(
        context: Context,
        selectedData: Map<Int, Int>,
        keyword: String,
        showMacau: Boolean,
        showHongKong: Boolean
    ) {
        val content = buildString {
            // 构建地区字符串
            val regions = mutableListOf<String>()
            if (showMacau) regions.add("澳门")
            if (showHongKong) regions.add("香港")
            
            // 添加标题和时间
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            // appendLine("${keyword}对应号码：")
            // appendLine("时间: $timestamp")
            appendLine("${regions.joinToString("/")}")
            appendLine("---------------")
            
            // 按号码排序输出
            selectedData.entries
                .sortedBy { it.key }
                .forEach { (number, amount) ->
                    appendLine("${String.format("%02d", number)}各${amount}元")
                }
            
            // 添加总计
            appendLine("\n== 总计 ==")
            appendLine("    ${selectedData.values.sum()}元")
        }

        // 创建分享 Intent
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, content)
        }
        
        // 启动分享选择器
        context.startActivity(Intent.createChooser(shareIntent, "分享到"))
    }

    // 进一步优化文本转图片函数，使用超清字体和更高分辨率，添加重要信息居中显示
    private fun textToImage(context: Context, text: String): Bitmap {
        // 设置文本样式 - 使用更深的背景色
        val backgroundColor = Color.rgb(230, 230, 225) // 更深的米白色背景
        val textColor = Color.rgb(35, 35, 35) // 稍微加深的文字颜色
        val titleColor = Color.rgb(25, 118, 210) // 保持当前的蓝色标题
        val totalColor = Color.rgb(56, 142, 60) // 保持当前的绿色总计
        
        // 交替行背景色 - 更深的颜色
        val alternateRowColor1 = Color.rgb(220, 220, 215) // 更深的浅灰色
        val alternateRowColor2 = Color.rgb(210, 210, 205) // 更深的灰色
        
        // 使用超清字体设置
        val paint = Paint().apply {
            color = textColor
            textSize = 42f // 保持当前字体大小
            typeface = Typeface.create(Typeface.SANS_SERIF, Typeface.NORMAL)
            isAntiAlias = true
            isDither = true // 启用抖动，提高渲染质量
            isFilterBitmap = true // 启用位图过滤，提高缩放质量
            isSubpixelText = true // 启用亚像素文本渲染
            hinting = Paint.HINTING_ON // 启用字体微调
            flags = flags or Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG // 添加额外的抗锯齿和过滤标志
        }
        
        val titlePaint = Paint(paint).apply {
            color = titleColor
            textSize = 50f // 保持当前标题字体大小
            typeface = Typeface.create(Typeface.SANS_SERIF, Typeface.BOLD)
        }
        
        val totalPaint = Paint(paint).apply {
            color = totalColor
            textSize = 46f // 保持当前总计字体大小
            typeface = Typeface.create(Typeface.SANS_SERIF, Typeface.BOLD)
        }
        
        // 计算文本尺寸
        val lines = text.split("\n")
        var maxWidth = 0f
        for (line in lines) {
            val width = when {
                line.startsWith("=== ") && line.endsWith(" ===") -> titlePaint.measureText(line)
                line.contains("总计:") || line.trim().startsWith("总计") -> totalPaint.measureText(line)
                else -> paint.measureText(line)
            }
            if (width > maxWidth) {
                maxWidth = width
            }
        }
        
        // 添加边距，保持当前设置
        val padding = 70
        
        // 计算基本尺寸
        val baseWidth = maxWidth.toInt() + padding * 2
        val lineHeight = paint.fontSpacing.toInt()
        val baseHeight = lineHeight * lines.size + padding * 2 + 50 // 保持当前额外高度
        
        // 保持当前分辨率设置
        val scaleFactor = 2.0f
        val width = (baseWidth * scaleFactor).toInt()
        val height = (baseHeight * scaleFactor).toInt()
        
        // 创建高分辨率位图
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 缩放画布以适应高分辨率
        canvas.scale(scaleFactor, scaleFactor)
        
        // 设置背景 - 确保背景色正确应用
        canvas.drawColor(backgroundColor)
        
        // 添加边框
        val borderPaint = Paint().apply {
            color = Color.rgb(190, 190, 185) // 稍微加深的边框颜色
            style = Paint.Style.STROKE
            strokeWidth = 4f
            isAntiAlias = true
        }
        canvas.drawRect(10f, 10f, baseWidth - 10f, baseHeight - 10f, borderPaint)
        
        // 绘制文本
        var y = padding + titlePaint.textSize
        var isDataRow = false
        var rowIndex = 0
        var inTotalSection = false // 标记是否已进入总计部分
        
        for (line in lines) {
            when {
                line.startsWith("=== ") && line.endsWith(" ===") -> {
                    // 标题行 - 添加标题背景并居中
                    val titleBgPaint = Paint().apply {
                        color = Color.rgb(215, 225, 240) // 稍微加深的浅蓝色背景
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawRect(
                        20f,
                        y - lineHeight + 10,
                        baseWidth - 20f,
                        y + 10,
                        titleBgPaint
                    )
                    
                    // 居中绘制标题文本
                    val textWidth = titlePaint.measureText(line)
                    val xPosition = (baseWidth - textWidth) / 2
                    canvas.drawText(line, xPosition, y, titlePaint)
                    
                    // 如果是总计标题，标记已进入总计部分
                    if (line.contains("总计")) {
                        inTotalSection = true
                    }
                    
                    y += lineHeight
                    isDataRow = false
                }
                line.startsWith("总记录:") -> {
                    // 总记录行 - 添加背景并居中
                    val recordBgPaint = Paint().apply {
                        color = Color.rgb(225, 235, 225) // 浅绿色背景
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawRect(
                        20f,
                        y - lineHeight + 10,
                        baseWidth - 20f,
                        y + 10,
                        recordBgPaint
                    )
                    
                    // 居中绘制总记录文本
                    val textWidth = paint.measureText(line)
                    val xPosition = (baseWidth - textWidth) / 2
                    canvas.drawText(line, xPosition, y, paint)
                    y += lineHeight
                    isDataRow = false
                }
                line.contains("总计:") || (line.trim().startsWith("总计") || line.trim() == "A") -> {
                    // 总计行 - 使用更明显的背景并居中
                    val totalBgPaint = Paint().apply {
                        color = Color.rgb(210, 235, 210) // 稍微加深的浅绿色背景
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawRect(
                        20f,
                        y - lineHeight + 10,
                        baseWidth - 20f,
                        y + 15,
                        totalBgPaint
                    )
                    
                    // 居中绘制总计文本
                    val textWidth = totalPaint.measureText(line)
                    val xPosition = (baseWidth - textWidth) / 2
                    canvas.drawText(line, xPosition, y, totalPaint)
                    y += lineHeight
                    isDataRow = false
                }
                // 添加新条件，匹配纯数字+A的行（总计金额）
                inTotalSection && line.matches(Regex("\\s*\\d+\\s*A\\s*")) -> {
                    // 总计金额行 - 使用更明显的背景并居中
                    val totalBgPaint = Paint().apply {
                        color = Color.rgb(210, 235, 210) // 稍微加深的浅绿色背景
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawRect(
                        20f,
                        y - lineHeight + 10,
                        baseWidth - 20f,
                        y + 15,
                        totalBgPaint
                    )
                    
                    // 居中绘制总金额文本，使用总计样式
                    val textWidth = totalPaint.measureText(line)
                    val xPosition = (baseWidth - textWidth) / 2
                    canvas.drawText(line, xPosition, y, totalPaint)
                    y += lineHeight
                    isDataRow = false
                }
                line.startsWith("------------------------") -> {
                    // 分隔线
                    val linePaint = Paint().apply {
                        color = Color.rgb(170, 170, 170) // 稍微加深的灰色
                        strokeWidth = 4f
                        isAntiAlias = true
                    }
                    canvas.drawLine(
                        30f, 
                        y - lineHeight/2, 
                        baseWidth - 30f, 
                        y - lineHeight/2, 
                        linePaint
                    )
                    y += lineHeight
                    isDataRow = false
                }
                line.matches(Regex(".*#\\d+.*\\d+.*[A元]")) -> {
                    // 数据行 - 匹配类似 "澳门 - #1 - 400 A" 的格式
                    isDataRow = true
                    rowIndex++
                    
                    // 绘制交替行背景
                    val rowBgPaint = Paint().apply {
                        color = if (rowIndex % 2 == 0) alternateRowColor1 else alternateRowColor2
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawRect(
                        20f,
                        y - lineHeight + 10,
                        baseWidth - 20f,
                        y + 10,
                        rowBgPaint
                    )
                    
                    // 提取金额部分并用不同颜色显示
                    val parts = line.split(" - ")
                    if (parts.size >= 3) {
                        // 绘制区域和序号
                        val prefix = "${parts[0]} - ${parts[1]}"
                        canvas.drawText(prefix, padding.toFloat(), y, paint)
                        
                        // 绘制金额，使用绿色
                        val amountPaint = Paint(paint)
                        amountPaint.color = Color.rgb(0, 125, 0) // 稍微加深的绿色金额
                        amountPaint.isFakeBoldText = true // 使金额加粗
                        val amountText = " - ${parts[2]}"
                        canvas.drawText(
                            amountText,
                            padding.toFloat() + paint.measureText(prefix),
                            y,
                            amountPaint
                        )
                    } else {
                        // 如果无法拆分，则正常绘制
                        canvas.drawText(line, padding.toFloat(), y, paint)
                    }
                    
                    y += lineHeight
                }
                else -> {
                    // 其他行
                    canvas.drawText(line, padding.toFloat(), y, paint)
                    y += lineHeight
                    isDataRow = false
                }
            }
        }
        
        return bitmap
    }

    // 修改简化数据图片导出函数
    fun exportSimplifiedDataAsImage(
        context: Context,
        dataList: List<DatabaseUtils.OriginalDataInfo>,
        filteredList: List<DatabaseUtils.OriginalDataInfo>
    ) {
        // 生成文本内容
        val content = buildString {
            // 按标签分组统计记录数量
            val tagGroups = dataList.groupBy { it.identifier.ifEmpty { "无标签" } }
            
            appendLine("=== 数据概览 ===")
            // 添加总记录以及标签分布信息
            appendLine("总记录: ${dataList.size} (${tagGroups.map { "${it.key}:${it.value.size}" }.joinToString(", ")})")
            appendLine("------------------------")
            
            // 首先按时间戳排序，确保最旧的在前面
            val sortedDataList = dataList.sortedBy { it.timestamp }
            
            // 然后按顺序生成序号
            sortedDataList.forEachIndexed { index, data ->
                // 计算总金额
                val normalTotal = data.numberDetails.sumOf { it.amount }
                val specialTotal = data.specialBets.sumOf { it.amount }
                val total = normalTotal + specialTotal
                
                // 序号从1开始，最旧的为#1
                val displayIndex = index + 1
                
                // 添加序号、标签和总金额
                val tagDisplay = if (data.identifier.isNotEmpty()) "[${data.identifier}]" else ""
                appendLine("${data.area} ${tagDisplay} - #$displayIndex - $total A")
            }
            
            // 添加总计
            val grandTotal = dataList.sumOf { data -> 
                data.numberDetails.sumOf { it.amount } + data.specialBets.sumOf { it.amount }
            }
            appendLine("\n=== 总计 ===")
            appendLine("    $grandTotal A")
        }
        
        try {
            // 将文本转换为图片
            val bitmap = textToImage(context, content)
            
            // 保存图片到临时文件
            val imagesDir = File(context.cacheDir, "images")
            imagesDir.mkdirs()
            val file = File(imagesDir, "export_${System.currentTimeMillis()}.png")
            
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
            }
            
            // 获取文件 URI
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file
            )
            
            // 创建分享 Intent
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_STREAM, uri)
                type = "image/png"
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            // 启动分享选择器
            context.startActivity(Intent.createChooser(
                shareIntent, 
                if (dataList.size < filteredList.size) "分享标记数据概览" else "分享数据概览"
            ))
        } catch (e: Exception) {
            // Log.e("ExportUtils", "图片导出失败", e)
            Toast.makeText(context, "图片导出失败: ${e.message}", Toast.LENGTH_SHORT).show()
            
            // 如果图片导出失败，回退到文本导出
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND 
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, content)
            }
            
            context.startActivity(Intent.createChooser(
                shareIntent, 
                if (dataList.size < filteredList.size) "分享标记数据概览" else "分享数据概览"
            ))
        }
    }

    // 添加详细数据图片导出函数
//    fun exportDetailedDataAsImage(
//        context: Context,
//        dataList: List<DatabaseUtils.OriginalDataInfo>,
//        filteredList: List<DatabaseUtils.OriginalDataInfo>
//    ) {
//        // 生成详细文本内容
//        val content = buildString {
//            // 按标签分组统计记录数量
//            val tagGroups = dataList.groupBy { it.identifier.ifEmpty { "无标签" } }
//
//            appendLine("=== 详细数据概览 ===")
//            // 添加总记录以及标签分布信息
//            appendLine("总记录: ${dataList.size} (${tagGroups.map { "${it.key}:${it.value.size}" }.joinToString(", ")})")
//            appendLine("------------------------")
//
//            // 首先按时间戳排序，确保最旧的在前面
//            val sortedDataList = dataList.sortedBy { it.timestamp }
//
//            // 然后按顺序生成序号
//            sortedDataList.forEachIndexed { index, data ->
//                // 计算总金额
//                val normalTotal = data.numberDetails.sumOf { it.amount }
//                val specialTotal = data.specialBets.sumOf { it.amount }
//                val total = normalTotal + specialTotal
//
//                // 序号从1开始，最旧的为#1
//                val displayIndex = index + 1
//
//                // 添加序号、标签和总金额
//                val tagDisplay = if (data.identifier.isNotEmpty()) "[${data.identifier}]" else ""
//                appendLine("${data.area} ${tagDisplay} - #$displayIndex - $total A")
//                appendLine("  时间: ${data.timestamp}")
//
//                // 添加标识（如果有）
//                if (data.identifier.isNotEmpty()) {
//                    appendLine("  标识: ${data.identifier}")
//                }
//
//                // 添加号码详情
//                if (data.numberDetails.isNotEmpty()) {
//                    appendLine("  号码详情:")
//                    data.numberDetails.forEach { detail ->
//                        appendLine("    ${detail.number}: ${detail.amount} A")
//                    }
//                }
//
//                // 添加特殊投注
//                if (data.specialBets.isNotEmpty()) {
//                    appendLine("  特殊投注:")
//                    data.specialBets.forEach { bet ->
//                        appendLine("    ${bet.type}: ${bet.amount} A")
//                    }
//                }
//
//                appendLine("------------------------")
//            }
//
//            // 添加总计
//            val grandTotal = dataList.sumOf { data ->
//                data.numberDetails.sumOf { it.amount } + data.specialBets.sumOf { it.amount }
//            }
//            appendLine("\n=== 总计 ===")
//            appendLine("    $grandTotal A")
//        }
//
//        try {
//            // 将文本转换为图片
//            val bitmap = textToImage(context, content)
//
//            // 保存图片到临时文件
//            val imagesDir = File(context.cacheDir, "images")
//            imagesDir.mkdirs()
//            val file = File(imagesDir, "export_detailed_${System.currentTimeMillis()}.png")
//
//            FileOutputStream(file).use { out ->
//                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
//            }
//
//            // 获取文件 URI
//            val uri = FileProvider.getUriForFile(
//                context,
//                "${context.packageName}.provider",
//                file
//            )
//
//            // 创建分享 Intent
//            val shareIntent = Intent().apply {
//                action = Intent.ACTION_SEND
//                putExtra(Intent.EXTRA_STREAM, uri)
//                type = "image/png"
//                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
//            }
//
//            // 启动分享选择器
//            context.startActivity(Intent.createChooser(
//                shareIntent,
//                if (dataList.size < filteredList.size) "分享标记数据详情" else "分享数据详情"
//            ))
//        } catch (e: Exception) {
//            Log.e("ExportUtils", "详细图片导出失败", e)
//            Toast.makeText(context, "详细图片导出失败: ${e.message}", Toast.LENGTH_SHORT).show()
//
//            // 如果图片导出失败，回退到文本导出
//            val shareIntent = Intent().apply {
//                action = Intent.ACTION_SEND
//                type = "text/plain"
//                putExtra(Intent.EXTRA_TEXT, content)
//            }
//
//            context.startActivity(Intent.createChooser(
//                shareIntent,
//                if (dataList.size < filteredList.size) "分享标记数据详情" else "分享数据详情"
//            ))
//        }
//    }

    // 添加新函数，导出简化版中奖数据为图片
    fun shareSimplifiedWinningData(
        context: Context,
        inputNumbers: String,
        showMacau: Boolean,
        showHongKong: Boolean,
        selectedTags: Set<String>? = null
    ) {
        try {
            // 改进号码解析逻辑
            val numbers = inputNumbers
                .replace("，", ",") // 统一中英文逗号
                .split(Regex("[,\\s]+")) // 按逗号或空格分割
                .map { it.trim() } // 去除空白
                .filter { it.isNotEmpty() } // 过滤空字符串
                .mapNotNull { 
                    try {
                        it.toInt().takeIf { num -> num in 1..49 }
                    } catch (e: NumberFormatException) {
                        Log.w(TAG, "无效号码: $it")
                        null
                    }
                }

            if (numbers.isEmpty()) {
                Log.e(TAG, "无效的输入号码: $inputNumbers")
                Toast.makeText(context, "请输入有效号码", Toast.LENGTH_SHORT).show()
                return
            }

            // 获取开奖号码和生肖
            val winningNumbers = numbers.joinToString(",")
            val winningZodiacs = numbers.mapNotNull { ZodiacUtils.getZodiacForNumber(it) }
            
            val content = buildString {
                // 添加开奖信息头部
                appendLine("=== 开奖码: $winningNumbers ===")
                appendLine("对应生肖：${winningZodiacs.joinToString("")}")
                appendLine()

                // 获取总投注额
                var macauTotalBets = 0.0
                var hkTotalBets = 0.0
                
                DatabaseHelper(context).readableDatabase.use { db ->
                    if (showMacau) {
                        // 获取澳门总投注额
                        val identifierCondition = if (selectedTags != null && selectedTags.isNotEmpty()) {
                            val conditions = mutableListOf<String>()
                            if (selectedTags.any { it.isNotEmpty() }) {
                                conditions.add("标识 IN (${selectedTags.filter { it.isNotEmpty() }.joinToString(",") { "'$it'" }})")
                            }
                            if (selectedTags.contains("")) {
                                conditions.add("(标识 IS NULL OR 标识 = '')")
                            }
                            if (conditions.isEmpty()) "" else "AND (${conditions.joinToString(" OR ")})"
                        } else ""
                        
                        val totalQuery = """
                            SELECT SUM(CASE 
                                WHEN 特码 IS NOT NULL THEN 注额
                                WHEN 特殊组合注额 IS NOT NULL THEN 特殊组合注额
                                ELSE 0 
                            END) as total
                            FROM ${DatabaseHelper.TABLE_NAME}
                            WHERE 地区 = '澳门'
                            $identifierCondition
                        """.trimIndent()
                        
                        db.rawQuery(totalQuery, null)?.use { cursor ->
                            if (cursor.moveToFirst()) {
                                macauTotalBets = cursor.getDouble(0)
                            }
                        }
                        
                        processRegionBets(context, db, "澳门", numbers.last(), numbers, winningZodiacs.toSet(), this, selectedTags, true)
                    }
                    
                    if (showHongKong) {
                        // 获取香港总投注额
                        val identifierCondition = if (selectedTags != null && selectedTags.isNotEmpty()) {
                            val conditions = mutableListOf<String>()
                            if (selectedTags.any { it.isNotEmpty() }) {
                                conditions.add("标识 IN (${selectedTags.filter { it.isNotEmpty() }.joinToString(",") { "'$it'" }})")
                            }
                            if (selectedTags.contains("")) {
                                conditions.add("(标识 IS NULL OR 标识 = '')")
                            }
                            if (conditions.isEmpty()) "" else "AND (${conditions.joinToString(" OR ")})"
                        } else ""
                        
                        val totalQuery = """
                            SELECT SUM(CASE 
                                WHEN 特码 IS NOT NULL THEN 注额
                                WHEN 特殊组合注额 IS NOT NULL THEN 特殊组合注额
                                ELSE 0 
                            END) as total
                            FROM ${DatabaseHelper.TABLE_NAME}
                            WHERE 地区 = '香港'
                            $identifierCondition
                        """.trimIndent()
                        
                        db.rawQuery(totalQuery, null)?.use { cursor ->
                            if (cursor.moveToFirst()) {
                                hkTotalBets = cursor.getDouble(0)
                            }
                        }
                        
                        processRegionBets(context, db, "香港", numbers.last(), numbers, winningZodiacs.toSet(), this, selectedTags, true)
                    }
                }
            }

            // 如果没有生成任何内容，显示提示
            if (content.isBlank()) {
                Toast.makeText(context, "没有找到相关中奖记录", Toast.LENGTH_SHORT).show()
                return
            }

            try {
                // 使用修改过的textToImage函数生成带有彩色文本的图片
                val bitmap = textToImageWithColoredAmount(context, content)
                
                // 保存图片到临时文件
                val imagesDir = File(context.cacheDir, "images")
                imagesDir.mkdirs()
                val file = File(imagesDir, "winning_${System.currentTimeMillis()}.png")
                
                FileOutputStream(file).use { out ->
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
                }
                
                // 获取文件 URI
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.provider",
                    file
                )
                
                // 创建分享 Intent
                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    putExtra(Intent.EXTRA_STREAM, uri)
                    type = "image/png"
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                
                // 启动分享选择器
                context.startActivity(Intent.createChooser(
                    shareIntent, 
                    "分享中奖概览"
                ))
            } catch (e: Exception) {
                // Log.e("ExportUtils", "图片导出失败", e)
                Toast.makeText(context, "图片导出失败: ${e.message}", Toast.LENGTH_SHORT).show()
                
                // 如果图片导出失败，回退到文本导出
                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND 
                    type = "text/plain"
                    putExtra(Intent.EXTRA_TEXT, content)
                }
                
                context.startActivity(Intent.createChooser(
                    shareIntent, 
                    "分享中奖概览"
                ))
            }
        } catch (e: Exception) {
            // Log.e(TAG, "处理过程中发生错误", e)
            Toast.makeText(context, "导出失败：${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    // 添加一个新的文本转图片函数，支持彩色文本和优化的格式
    private fun textToImageWithColoredAmount(context: Context, text: String): Bitmap {
        // 设置文本样式
        val backgroundColor = Color.rgb(230, 230, 225) // 背景色
        val textColor = Color.rgb(35, 35, 35) // 普通文字颜色
        val amountColor = Color.rgb(220, 10, 10) // 中奖金额使用红色
        val titleColor = Color.rgb(25, 118, 210) // 标题蓝色
        val totalColor = Color.rgb(56, 142, 60) // 总计绿色
        
        // 交替行背景色
        val alternateRowColor1 = Color.rgb(220, 220, 215) // 浅灰色
        val alternateRowColor2 = Color.rgb(210, 210, 205) // 灰色
        
        // 使用高清字体设置
        val paint = Paint().apply {
            color = textColor
            textSize = 42f
            typeface = Typeface.create(Typeface.SANS_SERIF, Typeface.NORMAL)
            isAntiAlias = true
            isDither = true
            isFilterBitmap = true
            isSubpixelText = true
            hinting = Paint.HINTING_ON
            flags = flags or Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG
        }
        
        val titlePaint = Paint(paint).apply {
            color = titleColor
            textSize = 50f
            typeface = Typeface.create(Typeface.SANS_SERIF, Typeface.BOLD)
        }
        
        val totalPaint = Paint(paint).apply {
            color = totalColor
            textSize = 46f
            typeface = Typeface.create(Typeface.SANS_SERIF, Typeface.BOLD)
        }
        
        val amountPaint = Paint(paint).apply {
            color = amountColor
            isFakeBoldText = true
        }
        
        // 计算文本尺寸
        val lines = text.split("\n")
        var maxWidth = 0f
        for (line in lines) {
            val width = when {
                line.startsWith("=== ") && line.endsWith(" ===") -> titlePaint.measureText(line) //
                line.contains("总计:") || line.trim().startsWith("总计") -> totalPaint.measureText(line) //
                else -> paint.measureText(line)
            }
            if (width > maxWidth) {
                maxWidth = width
            }
        }
        
        // 添加边距
        val padding = 70
        
        // 计算基本尺寸
        val baseWidth = maxWidth.toInt() + padding * 2
        val lineHeight = paint.fontSpacing.toInt()
        val baseHeight = lineHeight * lines.size + padding * 2 + 50
        
        // 设置高分辨率
        val scaleFactor = 2.0f
        val width = (baseWidth * scaleFactor).toInt()
        val height = (baseHeight * scaleFactor).toInt()
        
        // 创建高分辨率位图
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 缩放画布
        canvas.scale(scaleFactor, scaleFactor)
        
        // 设置背景
        canvas.drawColor(backgroundColor)
        
        // 添加边框
        val borderPaint = Paint().apply {
            color = Color.rgb(190, 190, 185)
            style = Paint.Style.STROKE
            strokeWidth = 4f
            isAntiAlias = true
        }
        canvas.drawRect(10f, 10f, baseWidth - 10f, baseHeight - 10f, borderPaint)
        
        // 绘制文本
        var y = padding + titlePaint.textSize
        var rowIndex = 0
        
        // 创建用于投注额的加粗画笔
        val betAmountPaint = Paint(paint).apply {
            isFakeBoldText = true  // 加粗
            textSize = 46f  // 稍微大一点
        }

        // 创建用于中奖金额的红色加粗画笔
        val winAmountPaint = Paint(paint).apply {
            color = Color.rgb(220, 0, 0)  // 红色
            isFakeBoldText = true  // 加粗
            textSize = 43f  // 稍微大一点
        }
        
        for (line in lines) {
            when {
                // 标题行保持不变
                line.startsWith("=== ") && line.endsWith(" ===") -> {
                    val titleBgPaint = Paint().apply {
                        color = Color.rgb(215, 225, 240)
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawRect(
                        20f,
                        y - lineHeight + 10,
                        baseWidth - 20f,
                        y + 10,
                        titleBgPaint
                    )
                    
                    // 居中绘制
                    val x = (baseWidth - titlePaint.measureText(line)) / 2
                    canvas.drawText(line, x, y, titlePaint)
                }
                
                // 匹配常规投注行，删除或注释掉背景色设置代码
                line.matches(Regex(".*-#\\d+-\\d+")) -> {
                    // 删除或注释以下代码块
                    /*
                    val rowBgPaint = Paint().apply {
                        color = if (rowIndex % 2 == 0) alternateRowColor1 else alternateRowColor2
                        style = Paint.Style.FILL
                        isAntiAlias = true
                    }
                    canvas.drawRect(
                        20f,
                        y - lineHeight + 10,
                        baseWidth - 20f,
                        y + 10,
                        rowBgPaint
                    )
                    */
                    
                    // 只保留文本绘制部分
                    canvas.drawText(line, 40f, y, paint)
                    rowIndex++ // 继续递增行索引以跟踪奇偶行
                }
                
                // 匹配带有中奖信息的行，使用红色字体绘制中奖金额
                line.contains("      中 ") -> {
                    // 将行分割为两部分：投注信息和中奖信息
                    val parts = line.split("      中 ", limit = 2)
                    if (parts.size == 2) {
                        // 绘制投注信息部分（使用普通文本颜色）
                        canvas.drawText(parts[0], 40f, y, paint)
                        
                        // 绘制"中"字（使用普通文本颜色）
                        val midText = "      中 "
                        val midX = 40f + paint.measureText(parts[0])
                        canvas.drawText(midText, midX, y, paint)
                        
                        // 绘制中奖信息部分（使用红色字体）
                        val winInfo = parts[1]
                        val winX = midX + paint.measureText(midText)
                        canvas.drawText(winInfo, winX, y, winAmountPaint)
                    } else {
                        // 如果无法分割，则绘制整行
                        canvas.drawText(line, 40f, y, paint)
                    }
                }
                
                // 其他行保持不变
                else -> {
                    canvas.drawText(line, 40f, y, paint)
                }
            }
            y += lineHeight
        }
        
        return bitmap
    }

    fun exportSimplifiedDataAsTxt(
        context: Context,
        dataList: List<DatabaseUtils.OriginalDataInfo>,
        onComplete: (Uri) -> Unit
    ) {
        try {
            // 获取标签名称
            val tagName = dataList.firstOrNull()?.identifier?.takeIf { it.isNotEmpty() } ?: "无标签"
            
            // 生成文件名（使用标签名+时间戳）
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "${tagName}_$timestamp.txt"

            // 创建文件
            val file = File(context.cacheDir, fileName)
            
            // 生成内容
            val content = buildString {
                // 按标签分组统计记录数量
                val tagGroups = dataList.groupBy { it.identifier.ifEmpty { "无标签" } }
                
                appendLine("=== 数据概览 ===")
                // 添加总记录以及标签分布信息
                appendLine("总记录: ${dataList.size} (${tagGroups.map { "${it.key}:${it.value.size}" }.joinToString(", ")})")
                appendLine("------------------------")
                
                // 首先按时间戳排序，确保最旧的在前面
                val sortedDataList = dataList.sortedBy { it.timestamp }
                
                // 然后按顺序生成序号
                sortedDataList.forEachIndexed { index, data ->
                    // 计算总金额
                    val normalTotal = data.numberDetails.sumOf { it.amount }
                    val specialTotal = data.specialBets.sumOf { it.amount }
                    val total = normalTotal + specialTotal
                    
                    // 序号从1开始，最旧的为#1
                    val displayIndex = index + 1
                    
                    // 添加序号、标签和总金额
                    val tagDisplay = if (data.identifier.isNotEmpty()) "[${data.identifier}]" else ""
                    appendLine("${data.area} $tagDisplay - #$displayIndex - $total A")
                }
                
                // 添加总计
                val grandTotal = dataList.sumOf { data -> 
                    data.numberDetails.sumOf { it.amount } + data.specialBets.sumOf { it.amount }
                }
                appendLine("\n=== 总计 ===")
                appendLine("    $grandTotal A")
            }

            // 写入文件
            file.writeText(content)

            // 获取文件的 URI
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file
            )

            // 调用回调函数
            onComplete(uri)
        } catch (e: Exception) {
            Toast.makeText(context, "导出失败：${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
} 

