package com.example.myshujuguanli.utils

import android.content.Context
import android.util.Log

//import com.example.myshujuguanli.utils.parser.ParseResult

object DataParser {
    private var originalInput: String? = null  // 记录原始输入
    private var lastSuccessInput: String? = null  // 记录最后一次成功解析的输入
    private var debugCleanedText: String = ""  // 添加调试信息存储
    private var debugTempLine: String = ""     // 添加调试信息存储
    private const val TAG = "DataParser"
    private var appContext: Context? = null  // 添加 Context 变量

    // 常量定义和预编译正则表达式
    private val REPLACE_EACH = Regex("每个数|每个号|各个|一个|每个|各号|号|各数|名号|个数|买|个(?!\\d)")
    private val REPLACE_CURRENCY = Regex("米|文|快|块|A|a|园|员|圆|大洋|斤")
    // 修改正则表达式，匹配两个"各"字之间有数字或中文数字的情况，且中间可能有点号
    private val REPLACE_DUPLICATE_EACH = Regex("各([\\d一二三四五六七八九十百千万]+\\.?)元?各")
    // 添加新的正则表达式，匹配两个"各"之间没有数字的情况，包括"各.各"这样的情况
    private val REPLACE_EMPTY_BETWEEN_EACH = Regex("各([^\\d一二三四五六七八九十百千万]|\\.)*各")
    private val REPLACE_SEPARATORS = Regex("[^\\p{L}\\p{N}\\u4e00-\\u9fa5]") // 替换所有非中英文数字的字符
//    private val REPLACE_SEPARATORS = Regex("[：｜|s'；,:，。！·<>《》「」{}】【!、·～`@#$%^&*()（）_——+=\\\\\\-\\/\\n\\r]")

    private val REPLACE_PERIOD = Regex("\\d+期|[香港澳门特包彩码奥噢的]")
    private val HEAD_PATTERN = Regex("([零一二三四01234]+)(?:字头数|字头|头数|头)各?(\\d+|[一二三四五六七八九十百千万]+)元?")
    private val HEAD_NUMBERS = mapOf(
        "0" to listOf(1, 2, 3, 4, 5, 6, 7, 8, 9),
        "零" to listOf(1, 2, 3, 4, 5, 6, 7, 8, 9),
        "1" to listOf(10, 11, 12, 13, 14, 15, 16, 17, 18, 19),
        "一" to listOf(10, 11, 12, 13, 14, 15, 16, 17, 18, 19),
        "2" to listOf(20, 21, 22, 23, 24, 25, 26, 27, 28, 29),
        "二" to listOf(20, 21, 22, 23, 24, 25, 26, 27, 28, 29),
        "3" to listOf(30, 31, 32, 33, 34, 35, 36, 37, 38, 39),
        "三" to listOf(30, 31, 32, 33, 34, 35, 36, 37, 38, 39),
        "4" to listOf(40, 41, 42, 43, 44, 45, 46, 47, 48, 49),
        "四" to listOf(40, 41, 42, 43, 44, 45, 46, 47, 48, 49)
    )

        // 添加波色常量定义
    val RED_NUMBERS = setOf(1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46)
    val BLUE_NUMBERS = setOf(3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48)
    val GREEN_NUMBERS = setOf(5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49)

    private val CHINESE_NUMBERS = mapOf(
        "零" to 0, "一" to 1, "二" to 2, "三" to 3, "四" to 4,
        "五" to 5, "六" to 6, "七" to 7, "八" to 8, "九" to 9,
        "十" to 10, "百" to 100, "千" to 1000, "万" to 10000
    )
    private val CURRENCY_CHARS = setOf('元')
    private val SPECIAL_BET_PATTERNS = mapOf(
        "平特" to listOf(
            // 重构后的平特肖格式，支持生肖在前或在后，以及各种可选关键字
            """([牛羊马虎鸡猪狗兔龙蛇鼠猴]+(?:[,，.。:：；、|｜\s\-－_~～]+[牛羊马虎鸡猪狗兔龙蛇鼠猴]+)*)\s*平特(?:一?肖)?\s*各?\s*([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            """平特(?:一?肖)?\s*([牛羊马虎鸡猪狗兔龙蛇鼠猴]+(?:[,，.。:：；、|｜\s\-－_~～]+[牛羊马虎鸡猪狗兔龙蛇鼠猴]+)*)\s*各?\s*([一二三四五六七八九十百千万\d]+)元?""".toRegex()
        ),
        "平特尾" to listOf(
            """平特([0-9一二三四五六七八九]+)尾各?([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            """平特尾([0-9一二三四五六七八九]+)各?([一二三四五六七八九十百千万\d]+)元?""".toRegex()
        ),
        "特肖" to listOf(
            """特\.?肖\.?([牛羊马虎鸡猪狗兔龙蛇鼠猴]+).*?各?([一二三四五六七八九十百千万\d]+)""".toRegex(),
            """([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)特\.?肖\.?.*?各?([一二三四五六七八九十百千万\d]+)""".toRegex()
        ),
        "连肖" to listOf(
            // 连肖数在前的格式（非贪婪匹配）- 支持复式在前或在后
            """(?:复式)?([2-5二三四五])连(?:肖|(?:\.?肖?))?(?:复式)?\s*([牛羊马虎鸡猪狗兔龙蛇鼠猴]+(?:[^牛羊马虎鸡猪狗兔龙蛇鼠猴0-9]+[牛羊马虎鸡猪狗兔龙蛇鼠猴]+)*?)\s*[.]?各?([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            
                       
            // 生肖在前的格式（非贪婪匹配）
            """(?:复式)?([^0-9]{1,15}?[牛羊马虎鸡猪狗兔龙蛇鼠猴]+[^0-9]{0,15}?)([2-5二三四五])连(?:肖|(?:\.?肖?))?(?:复式)?\s*[.]?各?([一二三四五六七八九十百千万\d]+)元?""".toRegex()
        ),
        "尾数" to listOf(
            """尾数([0-9一二三四五六七八九]+)各([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            """尾数([0-9一二三四五六七八九]+)([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            """([0-9一二三四五六七八九]+)尾([一二三四五六七八九十百千万\d]+)(?:一个数)?元?""".toRegex(),
            """([0-9一二三四五六七八九]+)尾各([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            """尾号([0-9一二三四五六七八九]+)各([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            """尾号([0-9一二三四五六七八九]+)([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            """([0-9一二三四五六七八九]+)尾号([一二三四五六七八九十百千万\d]+)元?""".toRegex()
        ),
        "波色" to listOf(
            // 基本波色格式（更通用的匹配）
            """包?([红绿蓝]{1,3})波?各?([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            // 波色带单双格式（更通用的匹配）
            """包?([红绿蓝]{1,3})波?[单双]数?各?([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
            // 新增：单独的波色单双数格式
            """包?([红绿蓝]{1,3})波?[单双]数?各?([一二三四五六七八九十百千万\d]+)元?""".toRegex()
        ),
        // // 新增：组合类型模式
        // "组合类型" to listOf(
        //     """(大单|小单|大双|小双|合单|合双)各?([一二三四五六七八九十百千万\d]+)元?""".toRegex(),
        //     """([单双大小])各?([一二三四五六七八九十百千万\d]+)元?""".toRegex()
        // )
    )
//    private val COMPLEX_LIANXIAO_PATTERNS = listOf(
//        Regex("([2-5二三四五])连(?:肖)?复式[：:]?([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
//        Regex("([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)([2-5二三四五])连(?:肖)?复式各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
//        Regex("复式([2-5二三四五])连(?:肖)?[：:]?([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
//        Regex("([2-5二三四五])连(?:肖)?([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"), // 新增
//        Regex("([鼠牛虎兔龙蛇马羊鸡狗猪]+)([2-5二三四五])连(?:肖)?各?([一二三四五六七八九十百千万\\d]+)(?:元)?") // 新增
//    )
    private val TAIL_NUMBERS = mapOf(
        "0" to listOf(10, 20, 30, 40),
        "1" to listOf(1, 11, 21, 31, 41),
        "2" to listOf(2, 12, 22, 32, 42),
        "3" to listOf(3, 13, 23, 33, 43),
        "4" to listOf(4, 14, 24, 34, 44),
        "5" to listOf(5, 15, 25, 35, 45),
        "6" to listOf(6, 16, 26, 36, 46),
        "7" to listOf(7, 17, 27, 37, 47),
        "8" to listOf(8, 18, 28, 38, 48),
        "9" to listOf(9, 19, 29, 39, 49)
    )
    private val COMBINATION_PATTERNS = mapOf(
        "二中二" to Regex("(?:复式)?二中二(?:复式)?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+|(?:[0-9一二三四五六七八九]+尾[\\s,，.。:：；、|｜\\-－_~～]*)+)各?([一二三四五六七八九十百千万\\d]+)元?"),
        "三中三" to Regex("(?:复式)?三中三(?:复式)?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+|(?:[0-9一二三四五六七八九]+尾[\\s,，.。:：；、|｜\\-－_~～]*)+)各?([一二三四五六七八九十百千万\\d]+)元?"),
        "三中二" to Regex("(?:复式)?三中二(?:复式)?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+|(?:[0-9一二三四五六七八九]+尾[\\s,，.。:：；、|｜\\-－_~～]*)+)各?([一二三四五六七八九十百千万\\d]+)元?")
    )

    // 添加不中型的模式
    private val NOT_HIT_PATTERNS = mapOf(
        "五不中" to Regex("(?:特|正码)?五不中?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)元?"),
        "六不中" to Regex("(?:特|正码)?六不中?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)元?"),
        "七不中" to Regex("(?:特|正码)?七不中?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)元?"),
        "八不中" to Regex("(?:特|正码)?八不中?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)元?"),
        "九不中" to Regex("(?:特|正码)?九不中?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)元?"),
        "十不中" to Regex("(?:特|正码)?十不中?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)元?"),
        "十一不中" to Regex("(?:特|正码)?十一不中?号?([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)元?")
    )

    // 添加中文数字到阿拉伯数字的映射
    private val CHINESE_DIGIT_MAP = mapOf(
        "一" to "1", "二" to "2", "三" to "3", "四" to "4", "五" to "5",
        "六" to "6", "七" to "7", "八" to "8", "九" to "9", "零" to "0"
    )

    // 这个函数用于检查给定文本中从指定位置开始是否包含中文数字金额
    // 参数:
    // text - 要检查的文本字符串
    // pos - 开始检查的位置
    // 返回值:
    // Pair<Boolean, Int> - 第一个值表示是否找到中文数字,第二个值表示中文数字的长度
    private fun isChineseAmount(text: String, pos: Int): Pair<Boolean, Int> {
        if (pos >= text.length) return Pair(false, 0)
        
        val char = text[pos].toString()
        if (char !in CHINESE_NUMBERS) return Pair(false, 0) 
        
        var endPos = pos
        var foundNumber = false
        
        while (endPos < text.length) {
            val currentChar = text[endPos].toString()
            if (currentChar in CHINESE_NUMBERS) {
                foundNumber = true
                endPos++
            } else {
                break
            }
        }
        
        return if (foundNumber) Pair(true, endPos - pos) else Pair(false, 0)
    }

    // 添加用于存储匹配到的文本的变量
    private var debugMatchedTexts = mutableListOf<String>()

    // 添加清理匹配文本的方法，在解析前调用
    private fun clearDebugMatchedTexts() {
        debugMatchedTexts.clear()
    }

    // 添加方法用于收集匹配到的文本
    fun addDebugMatchedText(text: String) {
        debugMatchedTexts.add(text)
    }

    // 添加获取所有匹配文本的方法
    fun getDebugMatchedTexts(): String {
        return debugMatchedTexts.joinToString("\n")
    }

    fun parseInput(input: String, isUserConfirmed: Boolean = false): Pair<List<String>, List<Pair<String, SpecialBetResult>>> {
        // 1. 检查地区冲突
        val hasHongKong = input.contains("香港") || input.contains("港") || input.contains("香")
        val hasMacau = input.contains("澳门") || input.contains("澳") || input.contains("门")
        if (hasHongKong && hasMacau) {
            return Pair(listOf("⚠️ 检测到同时存在香港和澳门\n请分开单独输入"), emptyList())
        }

        // 2. 记录原始输入
        if (originalInput == null) {
            originalInput = input
        }
        
        // 清理之前收集的匹配文本
        clearDebugMatchedTexts()
        
        try {
            // 检查是否启用自动纠正
            val textToProcess = if (shouldAutoCorrect()) {
                val correctedText = ParseLearningEngine.tryAutoCorrect(input)
                // Log.d(TAG, "自动纠正: $correctedText")
                correctedText
            } else {
                input
            }

            // 先进行文本清洗，然后再匹配
            var cleanedText = textToProcess.trim()
                .replace(REPLACE_EACH, "各")
                .replace(REPLACE_CURRENCY, "元")
                .replace(REPLACE_SEPARATORS, ".")
            
            

            // 处理连续的点号，将多个连续的点号替换为单个点号
            var oldResult = ""
            while (cleanedText != oldResult) {
                oldResult = cleanedText
                cleanedText = cleanedText.replace(Regex("\\.{2,}"), ".")
            }

            

            // 处理重复的"各"字，将"各(数字)各"模式替换为"各(数字)."
            oldResult = ""
            while (cleanedText != oldResult) {
                oldResult = cleanedText
                // 将"各(数字)各"替换为"各(数字)."
                cleanedText = REPLACE_DUPLICATE_EACH.replace(cleanedText, "各$1.")
                // 将两个"各"之间没有数字的情况替换为只保留一个"各"
                cleanedText = REPLACE_EMPTY_BETWEEN_EACH.replace(cleanedText, "各")
            }

            // 新增：循环将 ".元" 和 "元." 替换为 "元"
            var oldYuanResult = ""
            while (cleanedText != oldYuanResult) {
                oldYuanResult = cleanedText
                cleanedText = cleanedText.replace(".元", "元").replace("元.", "元")
            }

            // 保存调试信息
            debugCleanedText = cleanedText
            // 然后进行特殊投注匹配
            val (tailResults, specialBets, parsedText) = parseSpecialBets(cleanedText) // 特殊投注
            val normalResults = mutableListOf<String>() // 普通注额
            val specialResults = mutableListOf<Pair<String, SpecialBetResult>>() // 特殊投注
            
            normalResults.addAll(tailResults) // 普通注额
            specialResults.addAll(specialBets) // 特殊投注
            
            // 修改这部分代码，只处理normal_开头的项
            specialBets.forEach { (key, result) ->
                if (key.startsWith("normal_")) {
                    // 如果是 normal_ 开头的，添加到普通注额中
                    val number = key.removePrefix("normal_")
                    normalResults.add(String.format("%02d:%d", number.toInt(), result.amount))
                }
                // 移除处理非normal_开头项的代码，避免重复添加
            }
            
            // 使用剩余文本进行后续处理
            var tempLine = parsedText.trim()
                .replace(REPLACE_PERIOD, "")
            
            

            // 处理字头数格式
            HEAD_PATTERN.findAll(tempLine).forEach { match ->
                addDebugMatchedText(match.value)
                // Log.d(TAG, "match.value: ${match.value}")
                val heads = match.groupValues[1]  // 可能包含多个字头
                val amountStr = match.groupValues[2]
                val amount = convertChineseToNumber(amountStr)
                
                if (amount > 0 && heads.isNotEmpty()) {
                    
                    // 处理每个字头字符
                    heads.forEach { headChar ->
                        val head = headChar.toString()
                        if (HEAD_NUMBERS.containsKey(head)) {
                            HEAD_NUMBERS[head]?.forEach { num ->
                                normalResults.add(String.format("%02d:%d", num, amount))
                            }
                        }
                    }
                    // 移除已处理的文本，继续处理剩余部分
                    tempLine = tempLine.replace(match.value, "").trim()
                }
            }

            // 添加分割处理逻辑
            var currentPos = 0
            val sb = StringBuilder(tempLine)
            // println("分割前: $tempLine")
            
            // 处理货币单位和中文金额的分割
            while (currentPos < sb.length) {
                // 检查货币单位（元）
                if (sb[currentPos] in CURRENCY_CHARS) {
                    if (currentPos + 1 < sb.length) {
                        sb.insert(currentPos + 1, '\n')
                        currentPos += 2
                    } else {
                        currentPos++
                    }
                    continue
                }
                
                // 检查是否是中文金额
                val (isAmount, length) = isChineseAmount(sb.toString(), currentPos)
                if (isAmount) {
                    // 检查后面是否有元
                    var nextPos = currentPos + length
                    while (nextPos < sb.length && sb[nextPos].isWhitespace()) {
                        nextPos++
                    }
                    
                    // 如果后面没有"元"，则在中文金额后添加换行符
                    if (nextPos >= sb.length || sb[nextPos] !in CURRENCY_CHARS) {
                        if (currentPos + length < sb.length) {
                            sb.insert(currentPos + length, '\n')
                            currentPos += length + 1
                        } else {
                            currentPos += length
                        }
                        continue
                    }
                }
                currentPos++
            }
            // 添加对"各"的分割逻辑
            currentPos = 0
            while (currentPos < sb.length) {
                if (sb[currentPos] == '各') {
                    // 向后查找分割点
                    var nextPos = currentPos + 1
                    var foundSplit = false
                    
                    while (nextPos < sb.length) {
                        // 检查是否为中文数字
                        val (isAmount, _) = isChineseAmount(sb.toString(), nextPos)
                        
                        if (sb[nextPos] in CURRENCY_CHARS) {
                            // 遇到"元"字符时，在其后分割
                            sb.insert(nextPos + 1, '\n')
                            currentPos = nextPos + 2
                            foundSplit = true
                            break
                        } else if (sb[nextPos] == '.') {
                            // 遇到"."字符时，在其前分割
                            sb.insert(nextPos, '\n')
                            currentPos = nextPos + 1
                            foundSplit = true
                            break
                        } else if (isAmount) {
                            // 找到中文数字，继续向后查找
                            val chinesePart = sb.substring(nextPos)
                            var chineseEnd = 0
                            
                            // 查找中文数字的结束位置
                            while (chineseEnd < chinesePart.length) {
                                if (chinesePart[chineseEnd].toString() !in CHINESE_NUMBERS) {
                                    break
                                }
                                chineseEnd++
                            }
                            
                            // 检查中文数字后是否有"元"字符
                            val hasYuan = chineseEnd < chinesePart.length && chinesePart[chineseEnd] in CURRENCY_CHARS
                            
                            if (hasYuan) {
                                // 如果有"元"，在"元"后添加换行符
                                sb.insert(nextPos + chineseEnd + 1, '\n')
                                currentPos = nextPos + chineseEnd + 2
                            } else {
                                // 如果没有"元"，在中文数字后添加换行符
                                sb.insert(nextPos + chineseEnd, '\n')
                                currentPos = nextPos + chineseEnd + 1
                            }
                            
                            foundSplit = true
                            break
                        }
                        nextPos++
                    }
                    
                    // 如果没找到分割点，移动到下一个位置
                    if (!foundSplit) {
                        currentPos++
                    }
                    continue
                }
                currentPos++
            }
            tempLine = sb.toString()
            tempLine = tempLine.split("\n").joinToString("\n") { it.trim('.') }
            
            // 处理行首非法字符：去掉每行行首非中文数字、非十二生肖、非数字的字符
            tempLine = tempLine.split("\n").joinToString("\n") { line ->
                if (line.isNotEmpty()) {
                    // 查找第一个有效字符的位置（数字、中文数字或生肖）
                    var validStartPos = 0
                    while (validStartPos < line.length) {
                        val c = line[validStartPos]
                        val charStr = c.toString()
                        // 检查是否为数字、中文数字或生肖
                        if (c.isDigit() || 
                            CHINESE_NUMBERS.containsKey(charStr) || 
                            ZodiacUtils.getZodiacMappings().containsKey(charStr)) {
                            break
                        }
                        validStartPos++
                    }
                    
                    // 如果找到了有效起始位置，从该位置开始截取字符串
                    if (validStartPos < line.length) {
                        line.substring(validStartPos)
                    } else {
                        line // 如果没有找到有效字符，保留原始行
                    }
                } else {
                    line // 空行保持不变
                }
            }
            
            // 保存调试信息
            debugTempLine = tempLine

            // Log.d(TAG, "tempLine: $tempLine")
            
            // 先处理头数
            tempLine.split("\n").forEach { rawLine ->
                // 检查每一行是否包含大于49的数字
                val invalidNumberRegex = Regex("\\b(?!49)(5[0-9]|[6-9][0-9]|[1-9]\\d{2,})\\b")
                val invalidNumberMatch = invalidNumberRegex.find(rawLine)
                if (invalidNumberMatch != null) {
                    // 检查大于49的数字是否在行末尾
                    val matchPosition = invalidNumberMatch.range.first
                    val restOfLine = rawLine.substring(matchPosition + invalidNumberMatch.value.length).trim()
                    
                    // 如果不在行末尾,显示错误提示并停止解析
                    if (restOfLine.isNotEmpty()) {
                        appContext?.let { ctx ->
                            // 提取"各"之前的部分作为错误上下文
                            val lineParts = rawLine.split("各", limit = 2)
                            val errorContext = lineParts.getOrElse(0) { rawLine }.trim()
                            val warningMsg = "⚠️ 输入存在错误 ⚠️\n查看$errorContext 中的${invalidNumberMatch.value}\n如果是金额加元在后面 ${invalidNumberMatch.value}元\n如果是号码请在中间加空格\n\n" +
                                "可以到设置里看标准格式\n设置界面里可以看标准格式\n\n"
                            // 直接返回错误消息作为解析结果
                            return Pair(listOf(warningMsg), emptyList())
                        }
                        // 直接返回空结果，停止后续处理
                        return Pair(emptyList(), emptyList())
                    }
                }
                val line = rawLine.trim().trim('.')
                if (line.isBlank()) return@forEach

                

                var processed = false
                // var hasInvalidNumber = false
                
                // 使用同一个 headPattern 处理分割后的行
                HEAD_PATTERN.matchEntire(line)?.let { match ->
                    val head = match.groupValues[1]
                    val amount = convertChineseToNumber(match.groupValues[2])
                    if (amount > 0) {
                        HEAD_NUMBERS[head]?.forEach { num ->
                            normalResults.add(String.format("%02d:%d", num, amount))
                        }
                        processed = true
                        return@forEach
                    }
                }
                // 5. 处理范围格式
                if (!processed && (line.contains("到") || line.contains("至"))) {
                    Regex("(\\d+)[到至](\\d+)各?([一二三四五六七九十百千万\\d]+)元?").find(line)?.let { match ->
                        addDebugMatchedText(match.value)
                        val start = match.groupValues[1]
                        val end = match.groupValues[2]
                        val amount = convertChineseToNumber(match.groupValues[3])
                        if (start.toIntOrNull() in 1..49 && end.toIntOrNull() in 1..49 && amount > 0) {
                            (start.toInt()..end.toInt()).forEach { num ->
                                normalResults.add(String.format("%02d:%d", num, amount))
                            }
                            processed = true
                        }
                    }
                }
                // 在处理每行之前，先尝试解析连在一起的号码和金额格式
                if (!processed) {
                    // 匹配格式：1-2位数字后跟金额再跟"元"
                    Regex("^(\\d{1,2})(\\d+)元$").matchEntire(line)?.let { match ->
                        addDebugMatchedText(match.value)
                        // Log.d(TAG, "匹配格式：1-2位数字后跟金额再跟元: ${match.value}")
                        val number = match.groupValues[1]  // 直使用原始字符串
                        val amount = match.groupValues[2].toIntOrNull()
                        if (amount != null && number.toIntOrNull() in 1..49) {
                            normalResults.add("$number:$amount")
                            processed = true
                            return@forEach
                        }
                    }
                }
                // 处理每行，移除首尾的分隔符和空白
                if (!processed) {
                    // 1. 处理纯数字+元格式
                    Regex("^(\\d+)元?$").matchEntire(line)?.let { match ->
                        addDebugMatchedText(match.value)
                        // Log.d(TAG, "匹配格式：纯数字+元格式: ${match.value}")
                        val numStr = match.groupValues[1]
                        if (numStr.length >= 3) {
                            val numPart = numStr.dropLast(2)
                            val amount = numStr.takeLast(2).toIntOrNull()
                            val num = numPart.toIntOrNull()
                            if (num != null && amount != null && num in 1..49) {
                                // 保持原始数字格式
                                normalResults.add("$numPart:$amount")
                                processed = true
                            }
                        }
                    }
                }
                
                // 2. 处理数字+中文数字混合格式
                if (!processed) {
                    Regex("^(\\d+)([一二三四五六七八九十百千万]+)元?$").matchEntire(line)?.let { match ->
                        addDebugMatchedText(match.value)
                        // Log.d(TAG, "匹配格式：数字+中文数字混合格式: ${match.value}")
                        val numStr = match.groupValues[1]  // 保持原始数字字符串
                        val num = numStr.toIntOrNull()
                        val amount = convertChineseToNumber(match.groupValues[2])
                        if (num != null && num in 1..49 && amount > 0) {
                            normalResults.add("$numStr:$amount")  // 使用原始数字字符串
                            processed = true
                        }
                    }
                }
                
                // 3. 处理数字.中文数字格式
                if (!processed) {
                    Regex("^(\\d+)\\.([一二三四五六七八九十百千万]+)元?$").matchEntire(line)?.let { match ->
                        addDebugMatchedText(match.value)
                        // Log.d(TAG, "匹配格式：数字.中文数字格式: ${match.value}")
                        val numStr = match.groupValues[1]  // 保持原始数字字符串
                        val num = numStr.toIntOrNull()
                        val amount = convertChineseToNumber(match.groupValues[2])
                        if (num != null && num in 1..49 && amount > 0) {
                            normalResults.add("$numStr:$amount")  // 使用原始数字字符串
                            processed = true
                        }
                    }
                }
                
                // 6. 处理带"各"的标准格式
                if (!processed && line.contains("各")) {
                    addDebugMatchedText(line)
                    // Log.d(TAG, "匹配格式：带各的标准格式: ${line}")
                    val parts = line.split("各")
                    if (parts.size >= 2) {
                        val itemsPart = parts[0].trim()
                        val amountPart = parts[1].trim().replace("元", "")
                        val amount = convertChineseToNumber(amountPart)
                        
                        if (amount > 0) {
                            // 处理点号分隔的数字序列
                            itemsPart.split('.')
                                .filter { it.isNotBlank() }
                                .forEach { item ->
                                    // 处理可能的前导零和分离生肖与数字
                                    var processedItem = item
                                    
                                    // 分离生肖和数字
                                    var currentPos = 0

                                    while (currentPos < processedItem.length) {
                                        val currentChar = processedItem[currentPos].toString()
                                        if (ZodiacUtils.getZodiacMappings().containsKey(currentChar)) {
                                            processShengxiao(currentChar).forEach { shengxiaoNum ->
                                                val formattedNum = if (shengxiaoNum < 10) 
                                                    "0$shengxiaoNum" 
                                                else 
                                                    "$shengxiaoNum"
                                                normalResults.add("$formattedNum:$amount")
                                            }
                                            // 移除已处理的生肖符
                                            processedItem = processedItem.substring(currentPos + 1)
                                            // 重置位置以处理剩余部分
                                            currentPos = 0
                                        } else {
                                            currentPos++
                                        }
                                    }
                                    
                                    // 处理剩余的数字部分
                                    if (processedItem.isNotEmpty()) {
                                        // 只检查是否在1-49范围内，但不转换为Int
                                        val numCheck = processedItem.toIntOrNull()
                                        if (numCheck != null && numCheck in 1..49) {
                                            normalResults.add("$processedItem:$amount")  // 直接使用原始字符串
                                        } else if (numCheck != null) {
                                            // 数字超出范围
                                            appContext?.let { ctx ->
                                                val errorContext = line
                                                val warningMsg = "⚠️ 数字格式错误 ⚠️\n号码 $processedItem 不在1-49范围内: $errorContext"
                                                
                                                // 直接返回错误消息作为解析结果
                                                return Pair(listOf(warningMsg), emptyList())
                                            }
                                            // 中断所有解析，返回空结果
                                            return Pair(emptyList(), emptyList())
                                        }
                                    }
                                }
                            processed = true
                        } else {
                            // 金额转换失败，提取完整的错误上下文，并中断所有解析
                            appContext?.let { ctx ->
                                // 提取完整的错误上下文
                                val errorContext = line  // 使用整行作为上下文
                                val warningMsg = "⚠️ 金额格式错误 ⚠️\n无法识别金额: $amountPart\n检查: $errorContext\n\n⚠️需要改为标准的金额\n\n标准格式:10元 十元 一百元 二百元\n\n⚠️ 再仔细检查看看有没有其他错误 ⚠️\n"
                                
                                // 直接返回错误消息作为解析结果
                                return Pair(listOf(warningMsg), emptyList())
                            }
                            // 中断所有解析，返回空结果
                            return Pair(emptyList(), emptyList())
                        }
                    }
                }
                
                // 7. 处理号分隔的字+金额格式
                if (!processed && line.contains('.')) {
                    val parts = line.split('.').filter { it.isNotBlank() }
                    if (parts.size == 2 && parts[1].replace("元", "").all { it.isDigit() }) {
                        val num = parts[0]  // 直接使用原始字符串
                        val amount = convertChineseToNumber(parts[1].replace("元", ""))
                        if (num.toIntOrNull() in 1..49 && amount > 0) {
                            normalResults.add("$num:$amount")
                            processed = true
                        } else if (amount <= 0) {
                            // 金额转换失败，提取完整的错误上下文，并中断所有解析
                            appContext?.let { ctx ->
                                val errorContext = line  // 使用整行作为上下文
                                val warningMsg = "⚠️ 金额格式错误 ⚠️\n无法识别: $errorContext"
                                
                                // 直接返回错误消息作为解析结果
                                return Pair(listOf(warningMsg), emptyList())
                            }
                            // 中断所有解析，返回空结果
                            return Pair(emptyList(), emptyList())
                        }
                    }
                    // 新增：处理多个号码列表和一个金额的情况，如"03.07.300元"
                    else if (parts.size > 1 && parts.last().contains("元")) {
                        // 对于复杂格式，直接报错更稳妥
                        appContext?.let { ctx ->
                            val errorContext = line
                            val warningMsg = "⚠️ 格式需要调整 ⚠️\n请检查: $errorContext\n\n标准格式示例:\n3.5各100元\n4.5各100元\n"
                            return Pair(listOf(warningMsg), emptyList())
                        }
                        return Pair(emptyList(), emptyList())
                    }
                }
                
                // 如果所有尝试都失败，且行不为空，可能是有无法解析的格式
                if (!processed && line.isNotEmpty()) {
                    // 先过滤掉"元"字再进行检查
                    val lineWithoutYuan = line.replace("元", "").replace("平", "").replace("各", "").replace("特", "").trim()
                    if (lineWithoutYuan.isEmpty()) {
                        processed = true
                        return@forEach
                    }
                    
                    // 检查是否包含中文金额但格式不正确
                    val containsChineseAmount = lineWithoutYuan.any { it.toString() in CHINESE_NUMBERS.keys }
                    if (containsChineseAmount) {
                        appContext?.let { ctx ->
                            val errorContext = line
                            val warningMsg = "⚠️ 无法解析的格式 ⚠️\n检查: $errorContext\n\n是金额就在后面加元,是多余的就删除\n可以到设置里看标准格式\n设置界面里可以看标准格式\n\n" 
                                
                            // 直接返回错误消息作为解析结果
                            return Pair(listOf(warningMsg), emptyList())
                        }
                        // 中断所有解析，返回空结果
                        return Pair(emptyList(), emptyList())
                    } else {
                        // 任何未处理的非空行都报错
                        appContext?.let { ctx ->
                            val errorContext = line
                            val warningMsg = "⚠️ 无法解析的格式 ⚠️\n检查: $errorContext\n\n不符合标准格式，请修改后重试\n"
                                
                            // 直接返回错误消息作为解析结果
                            return Pair(listOf(warningMsg), emptyList())
                        }
                        // 中断所有解析，返回空结果
                        return Pair(emptyList(), emptyList())
                    }
                }
            }

            // 只在启用自动纠正且用户确认时才学习
            if (isUserConfirmed && shouldAutoCorrect() && originalInput != null && originalInput != input) {
                val similarity = calculateSimilarity(originalInput!!, input)
                if (similarity > 0.7) {
                    ParseLearningEngine.tryLearnNewMappings(originalInput!!, input)
                }
                originalInput = null
            }
            
            // 检查是否有任何解析结果
            if (normalResults.isEmpty() && specialResults.isEmpty() && tempLine.isNotBlank()) {
                // 如果没有结果但有内容，报错
                appContext?.let { ctx ->
                    val warningMsg = "⚠️ 解析失败 ⚠️\n无法解析输入：$tempLine\n请检查格式并重试"
                    return Pair(listOf(warningMsg), emptyList())
                }
                return Pair(emptyList(), emptyList())
            }
            
            if (normalResults.isNotEmpty() || specialResults.isNotEmpty()) {
                // 记录成功解析的输入
                lastSuccessInput = input
            }
            
            return Pair(normalResults, specialResults)
        } catch (e: Exception) {
            Log.e(TAG, "解析失败", e)
            return Pair(emptyList(), emptyList())
        }
    }
    private fun convertChineseToNumber(str: String): Int {
        // println("金额: $str")
        // 如果是纯数字，直接返回
        str.toIntOrNull()?.let { return it }
        
        var result = 0
        var temp = 0
        var base = 1
        var lastNum = 0
        
        // 记录无法识别的字符
        val unrecognizedChars = mutableListOf<Char>()
        
        str.forEach { char ->
            val charStr = char.toString()
            val value = CHINESE_NUMBERS[charStr]
            if (value == null) {
                unrecognizedChars.add(char)
                return@forEach
            }
            
            when (value) {
                in listOf(10, 100, 1000, 10000) -> {
                    temp = if (temp == 0) 1 else temp
                    result += temp * value
                    temp = 0
                    base = value
                }
                else -> {
                    temp = value
                    lastNum = value
                }
            }
        }
        
        if (temp > 0) {
            result += temp
        }
        
        val finalResult = if (result == 0 && lastNum > 0) lastNum else result
        
        // 如果有无法识别的字符或者结果为0，返回0表示转换失败
        // 但不在这里显示错误提示，由调用处负责处理
        if (unrecognizedChars.isNotEmpty() || finalResult == 0) {
            return 0  // 返回0表示转换失败
        }
        
        return finalResult
    }
    private fun parseSpecialBets(input: String): Triple<List<String>, List<Pair<String, SpecialBetResult>>, String> {
        val results = mutableListOf<Pair<String, SpecialBetResult>>()
        val normalResults = mutableListOf<String>()
        var remainingText = input

        // 首先处理多重拖式投注
        MULTI_TOW_PATTERNS.forEach { (towType, pattern) ->
            pattern.findAll(remainingText).forEach { match ->
                addDebugMatchedText(match.value)  // 添加匹配文本
                
                val part1 = match.groupValues[1]
                val part2 = match.groupValues[2]
                
                if (towType.startsWith("二中二")) {
                    val amount = convertChineseToNumber(match.groupValues[3])
                    
                    if (amount > 0) {
                        // 处理每个部分（可能是数字或生肖）
                        val numbers1 = processMultiPartInput(part1)
                        val numbers2 = processMultiPartInput(part2)
                        
                        processMultiTowBet(
                            betType = towType.replace("多重拖", ""),
                            part1Numbers = numbers1,
                            part2Numbers = numbers2,
                            amount = amount,
                            results = results
                        )
                    }
                } else {
                    // 三中三和三中二需要三部分
                    val part3 = match.groupValues[3]
                    val amount = convertChineseToNumber(match.groupValues[4])
                    
                    if (amount > 0) {
                        // 处理每个部分（可能是数字或生肖）
                        val numbers1 = processMultiPartInput(part1)
                        val numbers2 = processMultiPartInput(part2)
                        val numbers3 = processMultiPartInput(part3)
                        
                        processMultiTowBet(
                            betType = towType.replace("多重拖", ""),
                            part1Numbers = numbers1,
                            part2Numbers = numbers2,
                            part3Numbers = numbers3,
                            amount = amount,
                            results = results
                        )
                    }
                }
                
                remainingText = remainingText.replace(match.value, "").trim()
            }
        }

        // 然后再处理其他类型投注...
        // 处理二中二、三中三和三中二
        COMBINATION_PATTERNS.forEach { (betType, pattern) ->
            pattern.findAll(remainingText).forEach { match ->
                addDebugMatchedText(match.value)  // 添加匹配文本
                
                val amount = convertChineseToNumber(match.groupValues[2])
                
                if (amount > 0) {
                    // 获取原始文本
                    val originalNumbersText = match.groupValues[1].trim()
                    
                    // 检查是否包含尾数格式
                    val containsTailNumbers = originalNumbersText.contains(Regex("[0-9一二三四五六七八九]+尾"))
                    
                    // 获取所有数字 - 处理尾数或普通数字
                    val numbers = if (containsTailNumbers) {
                        // 处理尾数格式
                        val tailNumberPattern = Regex("([0-9一二三四五六七八九]+)尾")
                        tailNumberPattern.findAll(originalNumbersText).flatMap { tailMatch ->
                            val tailDigit = tailMatch.groupValues[1]
                            // 转换中文数字到阿拉伯数字
                            val tail = CHINESE_DIGIT_MAP[tailDigit] ?: tailDigit
                            // 获取对应尾数的所有号码
                            TAIL_NUMBERS[tail] ?: emptyList()
                        }.filter { it in 1..49 }.distinct().toList()
                    } else {
                        // 处理普通数字格式
                        originalNumbersText
                            .split(Regex("[,，\\s.。:：；、|｜\\-－_~～]+"))
                            .filter { it.isNotBlank() }
                            .mapNotNull { it.trim().toIntOrNull() }
                            .filter { it in 1..49 }
                            .distinct() // 确保数字不重复
                    }
                    
                    // 确定需要的组合数量
                    val combinationSize = when(betType) {
                        "二中二" -> 2
                        "三中三", "三中二" -> 3
                        else -> return@forEach
                    }
                    
                    // 检查是否包含复式字眼
                    val isComplex = match.value.contains("复式")
                    
                    if (isComplex) {
                        // 是复式，生成所有可能的组合
                        if (numbers.size >= combinationSize) {
                            combinations(numbers, combinationSize).forEach { combo ->
                                val key = "${betType}_${combo.joinToString("-")}"
                                results.add(Pair(key, SpecialBetResult(
                                    numbers = combo,
                                    amount = amount,
                                    type = betType
                                )))
                            }
                        }
                    } else {
                        // 不是复式，按原始输入处理
                        // 检查是否可能包含多组投注，更全面检测多种分隔符
                        val originalNumbersText = match.groupValues[1].trim()
                        val containsMultipleGroups = originalNumbersText.contains(Regex("[,，.。\\s:：；、|｜\\-－_~～]+"))

                        if (containsMultipleGroups) {
                            // 可能是多组投注，按多种分隔符分隔
                            // 先按明显的分隔符(如逗号、空格、点等)分成大组
                            val possibleGroups = originalNumbersText
                                .split(Regex("[,，.。\\s:：；、|｜\\-－_~～]+"))
                                .filter { it.isNotBlank() }
                            
                            // 从这些可能的组中构建number组
                            val groups = mutableListOf<List<Int>>()
                            var currentGroup = mutableListOf<Int>()
                            
                            for (item in possibleGroups) {
                                val number = item.trim().toIntOrNull()
                                if (number != null && number in 1..49) {
                                    currentGroup.add(number)
                                    if (currentGroup.size >= combinationSize) {
                                        groups.add(currentGroup.toList())
                                        currentGroup = mutableListOf()
                                    }
                                }
                            }
                            
                            // 处理可能的最后一个不完整组
                            if (currentGroup.size >= combinationSize) {
                                groups.add(currentGroup.toList())
                            }
                            
                            // 确保至少有一个有效组
                            if (groups.isEmpty() && numbers.size >= combinationSize) {
                                groups.add(numbers.take(combinationSize))
                            }
                            
                            // 每组单独处理
                            groups.forEach { groupNumbers ->
                                val key = "${betType}_${groupNumbers.joinToString("-")}"
                                results.add(Pair(key, SpecialBetResult(
                                    numbers = groupNumbers,
                                    amount = amount,
                                    type = betType
                                )))
                            }
                        } else {
                            // 单组投注，如果数字足够，使用前combinationSize个
                            if (numbers.size >= combinationSize) {
                                val combo = numbers.take(combinationSize)
                                val key = "${betType}_${combo.joinToString("-")}"
                                results.add(Pair(key, SpecialBetResult(
                                    numbers = combo,
                                    amount = amount,
                                    type = betType
                                )))
                            }
                        }
                    }
                }
                // 移除已处理的文本
                remainingText = remainingText.replace(match.value, "").trim()
            }
        }

        // 处理不中型投注
        NOT_HIT_PATTERNS.forEach { (betType, pattern) ->
            pattern.findAll(remainingText).forEach { match ->
                addDebugMatchedText(match.value)  // 添加匹配文本
                
                // 获取需要的号码数量
                val requiredCount = when (betType) {
                    "五不中" -> 5
                    "六不中" -> 6
                    "七不中" -> 7
                    "八不中" -> 8
                    "九不中" -> 9
                    "十不中" -> 10
                    "十一不中" -> 11
                    else -> 0
                }
                
                // 解析号码，保持原始顺序
                val numbers = match.groupValues[1]
                    .split(Regex("[,，\\s.。:：；、|｜\\-－_~～]+"))
                    .filter { it.isNotBlank() }
                    .mapNotNull { it.trim().toIntOrNull() }
                    .filter { it in 1..49 }
                    .distinct() // 去重
                
                val amount = convertChineseToNumber(match.groupValues[2])
                
                
                // 严格验证号码数量
                if (amount > 0 && numbers.size == requiredCount) {
                    // 创建特殊投注结果
                    val sortedNumbers = numbers.sorted() // 排序以保持一致性
                    val key = "${betType}_${sortedNumbers.joinToString("-")}"
                    results.add(Pair(key, SpecialBetResult(
                        numbers = sortedNumbers,
                        amount = amount,
                        type = betType
                    )))
                } else {
                    // 添加错误日志
                    if (numbers.size != requiredCount) {
                        // 使用 appContext 而不是 context
                        appContext?.let { ctx ->
                            // 根据号码数量比较动态调整消息
                            val compareMsg = if (numbers.size < requiredCount) {
                                "实际只有${numbers.size}个"
                            } else {
                                "实际有${numbers.size}个"
                            }
                            
                            // 使用 LENGTH_LONG 让提示停留更长时间
                            val warningMsg = "⚠️ ${betType}号码数量不符 ⚠️\n需要${requiredCount}个，${compareMsg}"
                            
                            // 直接返回错误消息
                            return Triple(emptyList(), listOf(Pair("error", SpecialBetResult(
                                numbers = emptyList(),
                                amount = 0,
                                type = warningMsg
                            ))), remainingText)
                        }
                    }
                    if (amount <= 0) {
                        // 添加金额无效的提示
                        appContext?.let { ctx ->
                            val warningMsg = "⚠️ ${betType}金额无效 ⚠️\n请检查投注金额格式"
                            
                            // 直接返回错误消息
                            return Triple(emptyList(), listOf(Pair("error", SpecialBetResult(
                                numbers = emptyList(),
                                amount = 0,
                                type = warningMsg
                            ))), remainingText)
                        }
                    }
                }
                
                remainingText = remainingText.replace(match.value, "").trim()
            }
        }
        
        // 然后处理其他特殊投注类型...
        // 处理平特和特肖
        for ((betType, patterns) in SPECIAL_BET_PATTERNS) {
            if (betType in listOf("平特", "特肖")) {
                patterns.forEach { pattern ->
                    pattern.findAll(remainingText).forEach { match ->
                        addDebugMatchedText(match.value)  // 添加匹配文本
                        
                        val zodiacsStr = match.groupValues[1]
                        val amount = convertChineseToNumber(match.groupValues[2])
                        
                        if (amount > 0) {
                            // 从捕获的字符串中提取所有生肖字符
                            val validZodiacs = zodiacsStr.filter { 
                                it.toString() in "牛羊马虎鸡猪狗兔龙蛇鼠猴" 
                            }

                            // 为每个识别出的生肖创建投注
                            validZodiacs.forEach { zodiac ->
                                val key = "${betType}_$zodiac"
                                val numbers = processShengxiao(zodiac.toString())
                                if (numbers.isNotEmpty()) {
                                    results.add(Pair(key, SpecialBetResult(
                                        numbers = numbers,
                                        amount = amount,
                                        type = betType
                                    )))
                                }
                            }
                            
                            remainingText = remainingText.replace(match.value, "").trim()
                        }
                    }
                }
            }
        }

        // 处理连肖
        SPECIAL_BET_PATTERNS["连肖"]?.forEach { pattern ->
            pattern.findAll(remainingText).forEach { match ->
                addDebugMatchedText(match.value)  // 添加匹配文本
                
                // 获取连肖数和金额
                val (count, zodiacsStr, amountStr) = when {
                    match.groupValues[1].matches(Regex("[2-5二三四五]")) -> {
                        // 处理数字在前的格式（包括多组）
                        val countNum = when(match.groupValues[1]) {
                            "二", "2" -> 2
                            "三", "3" -> 3
                            "四", "4" -> 4
                            "五", "5" -> 5
                            else -> return@forEach
                        }
                        Triple(countNum, match.groupValues[2], match.groupValues[3])
                    }
                    // 判断条件改为：首组字符串中是否包含生肖字符
                    match.groupValues[1].any { it.toString() in "牛羊马虎鸡猪狗兔龙蛇鼠猴" } && 
                    match.groupValues[2].matches(Regex("[2-5二三四五]")) -> {
                        // 处理生肖在前的格式
                        val countNum = when(match.groupValues[2]) {
                            "二", "2" -> 2
                            "三", "3" -> 3
                            "四", "4" -> 4
                            "五", "5" -> 5
                            else -> return@forEach
                        }
                        // 不要在这里做清洗，保留原始字符串以便后续分隔符检测
                        Triple(countNum, match.groupValues[1], match.groupValues[3])
                    }
                    else -> return@forEach
                }
                
                val amount = convertChineseToNumber(amountStr)
                // println("连肖数: $count, 生肖组合: $zodiacsStr, 金额: $amount")
                
                if (amount > 0) {
                    // 对生肖字符串做检测前的清洗，获取纯生肖部分用于判断长度等
                    val cleanZodiacsStr = zodiacsStr.filter { it.toString() in "牛羊马虎鸡猪狗兔龙蛇鼠猴" }
                    // 检测是否有非生肖字符作为分隔符
                    val hasNonZodiacChars = zodiacsStr.any { it.toString() !in "牛羊马虎鸡猪狗兔龙蛇鼠猴" }

                    // 智能分组逻辑
                    val zodiacGroups = if (hasNonZodiacChars) {
                        // 包含非生肖字符(任何分隔符) - 尝试智能分组
                        // println("检测到分隔符特征，尝试智能分组")
                        
                        // 首先根据任何非生肖字符分割
                        val tempGroups = mutableListOf<String>()
                        var currentGroup = StringBuilder()
                        
                        for (char in zodiacsStr) {
                            if (char.toString() in "牛羊马虎鸡猪狗兔龙蛇鼠猴") {
                                currentGroup.append(char)
                            } else if (currentGroup.isNotEmpty()) {
                                // 遇到非生肖字符且当前组不为空时，添加当前组并重置
                                tempGroups.add(currentGroup.toString())
                                currentGroup = StringBuilder()
                            }
                        }
                        
                        // 添加最后一个组
                        if (currentGroup.isNotEmpty()) {
                            tempGroups.add(currentGroup.toString())
                        }
                        
                        // 进一步处理长度问题
                        tempGroups.flatMap { group ->
                            // 如果组长度是连肖数的倍数，考虑进一步拆分
                            if (group.length > count && group.length % count == 0) {
                                val subGroups = mutableListOf<String>()
                                for (i in group.indices step count) {
                                    subGroups.add(group.substring(i, i + count))
                                }
                                subGroups
                            } else {
                                listOf(group)
                            }
                        }
                    } else {
                        // 纯生肖字符串 - 默认处理为复式（没有分隔符的情况）
                        // println("纯生肖字符串，没有分隔符，处理为复式")
                        
                        // 直接返回整个生肖字符串作为一个组
                        listOf(cleanZodiacsStr)
                    }

                    // println("智能分割后的生肖组: $zodiacGroups")
                    
                    // 处理每个生肖组
                    zodiacGroups.forEach { group ->
                        if (group.length >= count) {
                            // 如果生肖组长度等于连肖数，作为普通连肖处理
                            if (group.length == count) {
                                val zodiacList = group.map { it.toString() }
                                val sortedZodiacs = zodiacList.sorted().joinToString("")
                                val key = "${count}连肖_$sortedZodiacs"
                                
                                val numbers = zodiacList.flatMap { processShengxiao(it) }.sorted()
                                results.add(Pair(key, SpecialBetResult(
                                    numbers = numbers,
                                    amount = amount,
                                    type = "${count}连肖"
                                )))
                                // println("添加连肖结果: $key -> ${results.last()}")
                            } else {
                                // 如果生肖组长度大于连肖数，作为复式连肖处理
                                val zodiacChars = group.map { it.toString() }
                                combinations(zodiacChars.sorted(), count).forEach { combo ->
                                    val key = "${count}连肖复式_${combo.joinToString("")}"
                                    val numbers = combo.flatMap { processShengxiao(it) }.sorted()
                                    results.add(Pair(key, SpecialBetResult(
                                        numbers = numbers,
                                        amount = amount,
                                        type = "${count}连肖"
                                    )))
                                    // println("添加复式连肖结果: $key -> ${results.last()}")
                                }
                            }
                        }
                    }
                }
                
                remainingText = remainingText.replace(match.value, "").trim()
            }
        }

        // 处理平特尾
        SPECIAL_BET_PATTERNS["平特尾"]?.forEach { pattern ->
            pattern.findAll(remainingText).forEach { match ->
                addDebugMatchedText(match.value)  // 添加匹配文本
                
                val tailDigits = match.groupValues[1]  // 获取可能包含多个尾数
                val amount = convertChineseToNumber(match.groupValues[2])
                
                if (amount > 0) {
                    // 处理每个尾数字符
                    tailDigits.forEach { tailDigit ->
                        // 转换中文数字到阿拉伯数字
                        val tail = CHINESE_DIGIT_MAP[tailDigit.toString()] ?: tailDigit.toString()
                        
                        // 确保tail是有效的数字字符
                        if (tail.matches(Regex("\\d"))) {
                            // 获取所有对应尾数的号码 - 直接使用已定义的TAIL_NUMBERS
                            val numbers = TAIL_NUMBERS[tail] ?: (1..49).filter { it.toString().endsWith(tail) }.sorted()
                            
                            if (numbers.isNotEmpty()) {
                                // 创建特殊投注结果
                                val key = "平特尾_$tail"
                                results.add(Pair(key, SpecialBetResult(
                                    numbers = numbers,
                                    amount = amount,
                                    type = "平特尾"
                                )))
                            }
                        }
                    }
                    
                    remainingText = remainingText.replace(match.value, "").trim()
                }
            }
        }

        // 添加尾数处理
        SPECIAL_BET_PATTERNS["尾数"]?.forEach { pattern ->
            pattern.findAll(remainingText).forEach { match ->
                addDebugMatchedText(match.value)  // 添加匹配文本
                
                val tailDigits = match.groupValues[1]  // 可能包含多个尾数
                val amount = convertChineseToNumber(match.groupValues[2])
                
                if (amount > 0 && tailDigits.isNotEmpty()) {
                    // 处理每个尾数字符
                    tailDigits.forEach { tailDigit ->
                        // 转换中文数字到阿拉伯数字
                        val tail = CHINESE_DIGIT_MAP[tailDigit.toString()] ?: tailDigit.toString()
                        
                        // 确保tail是有效的数字字符
                        if (tail.matches(Regex("\\d"))) {
                            val numbers = TAIL_NUMBERS[tail] ?: return@forEach
                            numbers.forEach { number ->
                                val key = String.format("%02d:%d", number, amount)
                                normalResults.add(key)
                            }
                        }
                    }
                    
                    remainingText = remainingText.replace(match.value, "").trim()
                }
            }
        }

        // 处理波色
        SPECIAL_BET_PATTERNS["波色"]?.forEach { pattern ->
            pattern.findAll(remainingText).forEach { match ->
                addDebugMatchedText(match.value)  // 添加匹配文本
                
                val colors = match.groupValues[1].map { it.toString() }  // 将多个波色字符分开处理
                
                // 检查是否是"包"开头的特殊组合格式
                val isPackageFormat = match.value.startsWith("包")
                
                // 检查是否包含单双数
                val oddEven = when {
                    match.value.contains("单数") || match.value.contains("单") -> "单"
                    match.value.contains("双数") || match.value.contains("双") -> "双"
                    else -> null
                }
                
                // 检查是否包含"各"字
                val hasGe = match.value.contains("各")
                
                // 获取金额（根据正则表达式的分组位置）
                val amount = if (oddEven != null) {
                    convertChineseToNumber(match.groupValues[2])
                } else {
                    convertChineseToNumber(match.groupValues[2])
                }
                
                if (amount > 0) {
                    // 处理每个波色
                    colors.forEach { color ->
                        val numbers = when (color) {
                            "红" -> RED_NUMBERS
                            "蓝" -> BLUE_NUMBERS
                            "绿" -> GREEN_NUMBERS
                            else -> emptySet()
                        }
                        
                        val filteredNumbers = when (oddEven) {
                            "单" -> numbers.filter { it % 2 == 1 }
                            "双" -> numbers.filter { it % 2 == 0 }
                            else -> numbers
                        }.sorted()
                        
                        if (isPackageFormat) {
                            if (hasGe) {
                                // 包和各同时存在，按普通投注处理
                                filteredNumbers.forEach { number ->
                                    normalResults.add(String.format("%02d:%d", number, amount))
                                }
                            } else {
                                // 只有包，按特殊组合处理
                                val key = buildString {
                                    append("包${color}")
                                    if (oddEven != null) append("${oddEven}数")
                                }
                                results.add(Pair(key, SpecialBetResult(
                                    numbers = filteredNumbers,
                                    amount = amount,
                                    type = key
                                )))
                            }
                        } else {
                            // 普通投注格式
                            filteredNumbers.forEach { number ->
                                normalResults.add(String.format("%02d:%d", number, amount))
                            }
                        }
                    }
                }
                
                remainingText = remainingText.replace(match.value, "").trim()
            }
        }

        // 处理单双和合数单双 - 扩展正则表达式匹配更多格式
        val singleDoublePattern = """包?([大小]?[单双]数?|[单双]数?[大小]?|[大小]数?[单双]?|合[单双][大小]?|合[单双]?数?|大|小)数?各?([一二三四五六七八九十百千万\d]+)元?""".toRegex()
        singleDoublePattern.findAll(remainingText).forEach { match ->
            addDebugMatchedText(match.value)  // 添加匹配文本

            val type = match.groupValues[1].replace("数", "")  // 移除"数"后再判断类型
            val amount = convertChineseToNumber(match.groupValues[2])
            val isPackageFormat = match.value.startsWith("包")
            val hasGe = match.value.contains("各")  // 检查是否包含"各"字
            
            if (amount > 0) {
                // 处理可能的组合形式（"小数双" => "小双"，"双数小" => "小双"等）
                val processedType = when {
                    // 处理"双数小"或"双小"这样的格式 - 应该解析为"小双"
                    type.contains("双") && type.contains("小") -> "小双"
                    // 处理"单数小"或"单小"这样的格式 - 应该解析为"小单"
                    type.contains("单") && type.contains("小") -> "小单"
                    // 处理"双数大"或"双大"这样的格式 - 应该解析为"大双"
                    type.contains("双") && type.contains("大") -> "大双"
                    // 处理"单数大"或"单大"这样的格式 - 应该解析为"大单"
                    type.contains("单") && type.contains("大") -> "大单"
                    // 如果只是简单的"单"或"双"带"数"后缀
                    else -> type
                }
                
                val numbers = when (processedType) {
                    "单" -> (1..49).filter { it % 2 == 1 }
                    "双" -> (1..49).filter { it % 2 == 0 }
                    "大单" -> (25..49).filter { it % 2 == 1 }
                    "小单" -> (1..24).filter { it % 2 == 1 }
                    "大双" -> (25..49).filter { it % 2 == 0 }
                    "小双" -> (1..24).filter { it % 2 == 0 }
                    "大" -> (25..49).toList()
                    "小" -> (1..24).toList()
                    "合单" -> (1..49).filter { (it / 10 + it % 10) % 2 == 1 }
                    "合双" -> (1..49).filter { (it / 10 + it % 10) % 2 == 0 }
                    "合单大" -> (25..49).filter { (it / 10 + it % 10) % 2 == 1 }
                    "合单小" -> (1..24).filter { (it / 10 + it % 10) % 2 == 1 }
                    "合双大" -> (25..49).filter { (it / 10 + it % 10) % 2 == 0 }
                    "合双小" -> (1..24).filter { (it / 10 + it % 10) % 2 == 0 }
                    else -> emptyList()
                }.sorted()
                
                if (isPackageFormat) {
                    if (hasGe) {
                        // 包和各同时存在，按普通投注处理
                        numbers.forEach { number ->
                            normalResults.add(String.format("%02d:%d", number, amount))
                        }
                    } else {
                        // 只有包，按特殊组合处理
                        val key = "包$processedType"
                        results.add(Pair(key, SpecialBetResult(
                            numbers = numbers,
                            amount = amount,
                            type = key
                        )))
                    }
                } else {
                    // 普通投注格式
                    numbers.forEach { number ->
                        normalResults.add(String.format("%02d:%d", number, amount))
                    }
                }
            }
            
            remainingText = remainingText.replace(match.value, "").trim()
        }

        return Triple(normalResults, results, remainingText)
    }

    // 辅助函数：生成组合
    private fun <T> combinations(items: List<T>, count: Int): List<List<T>> {
        if (count == 0) return listOf(emptyList())
        if (items.isEmpty()) return emptyList()
        if (count == 1) return items.map { listOf(it) }
        
        val result = mutableListOf<List<T>>()
        
        // 直接实现，避免过深的递归
        if (count == 2) {
            for (i in 0 until items.size - 1) {
                for (j in i + 1 until items.size) {
                    result.add(listOf(items[i], items[j]))
                }
            }
            return result
        }
        
        for (i in 0..items.size - count) {
            val head = items[i]
            val subCombinations = combinations(items.drop(i + 1), count - 1)
            result.addAll(subCombinations.map { listOf(head) + it })
        }
        
        return result
    }

    private fun processShengxiao(zodiac: String): List<Int> {
        return ZodiacUtils.getZodiacMappings()[zodiac] ?: emptyList()
    }

    private fun shouldAutoCorrect(): Boolean {
        return appContext?.getSharedPreferences("settings", Context.MODE_PRIVATE)
            ?.getBoolean("auto_correct_enabled", true) ?: true
    }

    fun parse(input: String, isUserConfirmed: Boolean = false): Pair<List<String>, List<Pair<String, SpecialBetResult>>> {
        // 检查是否启用自动纠正
        val textToProcess = if (shouldAutoCorrect()) {
            ParseLearningEngine.tryAutoCorrect(input)
        } else {
            input
        }
        
        try {
            val (normalBets, specialBets) = parseInput(textToProcess)
            
            // 只在启用自动纠正时才学习
            if (isUserConfirmed && shouldAutoCorrect() && originalInput != null && originalInput != input) {
                val similarity = calculateSimilarity(originalInput!!, input)
                if (similarity > 0.7) {
                    ParseLearningEngine.tryLearnNewMappings(originalInput!!, input)
                }
                originalInput = null
            } else {
                // Log.d(TAG, "跳过学习 - 自动纠正已禁用或条件不满足")
            }
            
            return Pair(normalBets, specialBets)
        } catch (e: Exception) {
            // Log.e(TAG, "解析失败", e)
            return Pair(emptyList(), emptyList())
        }
    }

    // 记录原始输入
    fun recordOriginalInput(input: String) {
        // 只在没有原始输入时记录
        if (originalInput == null) {
            originalInput = input
            // Log.d(TAG, "记录原始输入: '$input'")
        }
    }

    // 获取原始输入
    fun getOriginalInput(): String? = originalInput

    // 重置状态
    fun reset() {
        originalInput = null
    }

    // 将 calculateSimilarity 改为公开方法
    fun calculateSimilarity(str1: String, str2: String): Double {
        val longer = if (str1.length > str2.length) str1 else str2
        val shorter = if (str1.length > str2.length) str2 else str1
        
        if (longer.isEmpty()) return 1.0
        
        // 计算编辑距离
        val costs = IntArray(shorter.length + 1) { it }
        for (i in 1..longer.length) {
            var lastValue = i
            for (j in 1..shorter.length) {
                val newValue: Int = if (longer[i - 1] == shorter[j - 1]) {
                    costs[j - 1]
                } else {
                    1 + minOf(costs[j - 1], costs[j], lastValue)
                }
                costs[j - 1] = lastValue
                lastValue = newValue
            }
            costs[shorter.length] = lastValue
        }
        
        return (longer.length - costs[shorter.length].toDouble()) / longer.length
    }

    // 添加初始化方法
    fun init(context: Context) {
        appContext = context.applicationContext
    }



    // 添加获取波色的辅助方法
    fun getNumberColor(number: Int): NumberColor {
        return when (number) {
            in RED_NUMBERS -> NumberColor.RED
            in BLUE_NUMBERS -> NumberColor.BLUE
            in GREEN_NUMBERS -> NumberColor.GREEN
            else -> NumberColor.NONE
        }
    }

    // // 修改TOW_PATTERNS，支持所有三种拖码形式
    // private val TOW_PATTERNS = mapOf(
    //     // 1. 号码拖号码 (普通拖码)
    //     "二中二拖" to Regex("二中二(?:号)?([\\d\\s,，.。:：；、|｜\\-－_~～]+)拖([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
    //     "三中三拖" to Regex("三中三(?:号)?([\\d\\s,，.。:：；、|｜\\-－_~～]+)拖([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
    //     "三中二拖" to Regex("三中二(?:号)?([\\d\\s,，.。:：；、|｜\\-－_~～]+)拖([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
        
    //     // 2. 生肖拖号码
    //     "二中二生肖拖号码" to Regex("二中二(?:号)?([牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～x]+)拖([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
    //     "三中三生肖拖号码" to Regex("三中三(?:号)?([牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～x]+)拖([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
    //     "三中二生肖拖号码" to Regex("三中二(?:号)?([牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～x]+)拖([\\d\\s,，.。:：；、|｜\\-－_~～]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
        
    //     // 3. 号码拖生肖 - 增强对多个胆码的支持
    //     "二中二号码拖生肖" to Regex("二中二(?:号)?([\\d\\s,，.。:：；、|｜\\-－_~～]+)拖([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
    //     "三中三号码拖生肖" to Regex("三中三(?:号)?([\\d\\s,，.。:：；、|｜\\-－_~～]+)拖([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
    //     "三中二号码拖生肖" to Regex("三中二(?:号)?([\\d\\s,，.。:：；、|｜\\-－_~～]+)拖([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
        
    //     // 4. 生肖拖生肖
    //     "二中二生肖拖生肖" to Regex("二中二(?:号)?([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)拖([牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～x]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
    //     "三中三生肖拖生肖" to Regex("三中三(?:号)?([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)拖([牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～x]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
    //     "三中二生肖拖生肖" to Regex("三中二(?:号)?([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)拖([牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～x]+)各?([一二三四五六七八九十百千万\\d]+)(?:元)?"),
        
    //     // 5. 特殊格式(增强版) - 支持"二中二18.20拖马各10"这种格式
    //     "二中二特殊格式" to Regex("二中二(\\d+)\\s*[,，.。]\\s*(\\d+)\\s*拖\\s*([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)\\s*各\\s*([一二三四五六七八九十百千万\\d]+)"),
    //     "三中三特殊格式" to Regex("三中三(\\d+)\\s*[,，.。]\\s*(\\d+)\\s*拖\\s*([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)\\s*各\\s*([一二三四五六七八九十百千万\\d]+)"),
    //     "三中二特殊格式" to Regex("三中二(\\d+)\\s*[,，.。]\\s*(\\d+)\\s*拖\\s*([牛羊马虎鸡猪狗兔龙蛇鼠猴]+)\\s*各\\s*([一二三四五六七八九十百千万\\d]+)")
    // )

    // 添加新的方法处理可能包含多个生肖和分隔符的字符串
    private fun processZodiacString(zodiacString: String): List<Int> {
        // 提取所有有效生肖字符
        val validZodiacs = zodiacString.filter { 
            ZodiacUtils.getZodiacMappings().containsKey(it.toString()) 
        }
        
        // // 添加调试日志
        // Log.d(TAG, "处理生肖字符串: '$zodiacString' -> 找到生肖: '${validZodiacs}', 对应号码数: ${
        //     validZodiacs.flatMap { processShengxiao(it.toString()) }.size
        // }")
        
        // 返回所有生肖对应的号码，去重
        return validZodiacs.flatMap { 
            processShengxiao(it.toString())
        }.distinct()
    }

    // 添加新的多重拖码模式正则表达式
    private val MULTI_TOW_PATTERNS = mapOf(
        // 二中二只需要一个拖，格式如：二中二1,2拖3,4,5各10
        // 增加对尾号和生肖的支持
        "二中二多重拖" to Regex("二中二号?([\\d牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～]+|[0-9一二三四五六七八九]+尾)拖([\\d牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～]+|[0-9一二三四五六七八九]+尾)各?([一二三四五六七八九十百千万\\d]+)元?"),
        
        // 三中三需要两个拖，格式如：三中三1拖2,3拖4,5各10
        // 增加对尾号和生肖的支持
        "三中三多重拖" to Regex("三中三号?([\\d牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～]+|[0-9一二三四五六七八九]+尾)拖([\\d牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～]+|[0-9一二三四五六七八九]+尾)拖([\\d牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～]+|[0-9一二三四五六七八九]+尾)各?([一二三四五六七八九十百千万\\d]+)元?"),
        
        // 三中二需要两个拖，格式如：三中二1拖2,3拖4,5各10
        // 增加对尾号和生肖的支持
        "三中二多重拖" to Regex("三中二号?([\\d牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～]+|[0-9一二三四五六七八九]+尾)拖([\\d牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～]+|[0-9一二三四五六七八九]+尾)拖([\\d牛羊马虎鸡猪狗兔龙蛇鼠猴\\s,，.。:：；、|｜\\-－_~～]+|[0-9一二三四五六七八九]+尾)各?([一二三四五六七八九十百千万\\d]+)元?")
    )

    // 添加处理多部分输入的辅助函数
    private fun processMultiPartInput(input: String): List<Int> {
        // 首先检查是否是尾号格式
        val tailMatch = Regex("([0-9一二三四五六七八九]+)尾").matchEntire(input.trim())
        if (tailMatch != null) {
            // 处理尾号
            val tailDigit = CHINESE_DIGIT_MAP[tailMatch.groupValues[1]] ?: tailMatch.groupValues[1]
            return TAIL_NUMBERS[tailDigit] ?: emptyList()
        }
        
        // 然后尝试解析生肖
        val zodiacNumbers = processZodiacString(input)
        if (zodiacNumbers.isNotEmpty()) {
            return zodiacNumbers
        }
        
        // 如果不是生肖，尝试解析数字
        return parseNumberList(input)
    }

    // 添加处理多重拖码的函数
    private fun processMultiTowBet(
        betType: String,
        part1Numbers: List<Int>,
        part2Numbers: List<Int>,
        part3Numbers: List<Int> = emptyList(),  // 为二中二情况提供默认值
        amount: Int,
        results: MutableList<Pair<String, SpecialBetResult>>
    ) {
        when (betType) {
            "二中二" -> {
                // 二中二只使用前两部分的号码
                if (part1Numbers.isEmpty() || part2Numbers.isEmpty()) {
                    // Log.e(TAG, "二中二拖码处理：缺少必要的号码")
                    return
                }

                // 生成组合
                for (num1 in part1Numbers) {
                    for (num2 in part2Numbers) {
                        if (num2 == num1) continue  // 跳过重复号码
                        
                        val combo = listOf(num1, num2).sorted()
                        val key = "${betType}_${combo.joinToString("-")}"
                        results.add(Pair(key, SpecialBetResult(
                            numbers = combo,
                            amount = amount,
                            type = betType
                        )))
                    }
                }
            }
            
            "三中三", "三中二" -> {
                // 三中三和三中二需要所有三部分的号码
                if (part1Numbers.isEmpty() || part2Numbers.isEmpty() || part3Numbers.isEmpty()) {
                    // Log.e(TAG, "${betType}拖码处理：缺少必要的号码")
                    return
                }

                // 生成组合
                for (num1 in part1Numbers) {
                    for (num2 in part2Numbers) {
                        if (num2 == num1) continue  // 跳过重复号码
                        
                        for (num3 in part3Numbers) {
                            if (num3 == num1 || num3 == num2) continue  // 跳过重复号码
                            
                            val combo = listOf(num1, num2, num3).sorted()
                            val key = "${betType}_${combo.joinToString("-")}"
                            results.add(Pair(key, SpecialBetResult(
                                numbers = combo,
                                amount = amount,
                                type = betType
                            )))
                        }
                    }
                }
            }
        }
    }

    // 添加获取调试信息的方法
    fun getDebugCleanedText(): String = debugCleanedText
    fun getDebugTempLine(): String = debugTempLine
}

// 添加辅助函数：解析号码列表
private fun parseNumberList(input: String): List<Int> {
    // 先按常见分隔符拆分
    return input
        .split(Regex("[\\s,，.。:：；、|｜\\-－_~～]+"))
        .filter { it.isNotBlank() }
        .mapNotNull { it.toIntOrNull() }
        .filter { it in 1..49 }
        .distinct()
}