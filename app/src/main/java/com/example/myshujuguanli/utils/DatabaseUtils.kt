package com.example.myshujuguanli.utils

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.util.Log
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import kotlin.math.roundToInt

class DatabaseHelper(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {
    companion object {
        const val DATABASE_NAME = "注额管理.db"
        const val DATABASE_VERSION = 4
        const val TABLE_NAME = "注额管理"

        private const val SQL_CREATE_TABLE = """
            CREATE TABLE IF NOT EXISTS $TABLE_NAME (
                特码 INTEGER,
                注额 INTEGER,
                计数 INTEGER,
                地区 TEXT,
                标识 TEXT,
                原始数据 TEXT,
                特殊组合注额 INTEGER,
                特殊组合类型 TEXT,
                投注类型 TEXT DEFAULT 'normal',
                时间戳 TEXT,
                号码列表 TEXT
            )
        """
    }
    override fun onCreate(db: SQLiteDatabase) {
        db.execSQL(SQL_CREATE_TABLE)
    }
    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        if (oldVersion < 3) {
            db.execSQL("ALTER TABLE $TABLE_NAME ADD COLUMN 投注类型 TEXT DEFAULT 'normal'")
        }
        if (oldVersion < 4) {
            try {
                db.execSQL("ALTER TABLE $TABLE_NAME ADD COLUMN 号码列表 TEXT")
            } catch (e: Exception) {
                // 如果列已存在，忽略错误
                e.printStackTrace()
            }
        }
    }
}
object DatabaseUtils {
    private const val PREFS_NAME = "DataCountPrefs"
    private const val KEY_DATA_COUNT = "data_count"
    
    // 使用 StringBuilder 池来减少内存分配
    private val stringBuilderPool = object : ThreadLocal<StringBuilder>() {
        override fun initialValue(): StringBuilder {
            return StringBuilder(1024)  // 预分配合适的初始容量
        }
    }

    // 添加时区设置
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).apply {
        timeZone = TimeZone.getTimeZone("Asia/Shanghai")
    }

    // 在 DatabaseUtils 中添加一个变量来存储当前批次的时间戳
    private var currentBatchTimestamp: String? = null

    // 添加一个函数来获取或创建批次时间戳
    private fun getBatchTimestamp(): String {
        return currentBatchTimestamp ?: SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            .format(Date()).also { currentBatchTimestamp = it }
    }

    // 添加一个函数来重置批次时间戳
    fun resetBatchTimestamp() {
        currentBatchTimestamp = null
    }

    fun incrementDataCount(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentCount = prefs.getInt(KEY_DATA_COUNT, 0)
        prefs.edit().putInt(KEY_DATA_COUNT, currentCount + 1).apply()
    }

    fun getDataCount(context: Context): Int {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .getInt(KEY_DATA_COUNT, 0)
    }

    fun resetDataCount(context: Context) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putInt(KEY_DATA_COUNT, 0)
            .apply()
    }

    // 添加减少记录数量的函数
    fun decrementDataCount(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentCount = prefs.getInt(KEY_DATA_COUNT, 0)
        if (currentCount > 0) {
            prefs.edit().putInt(KEY_DATA_COUNT, currentCount - 1).apply()
        }
    }

    // 修改插入数据的时间戳获取方法
    fun insertData(
        context: Context,
        number: Int,
        amount: Int,
        tag: String,
        identifier: String,
        originalData: String
    ): Boolean {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.writableDatabase.use { db ->
            var success = false
            val values = ContentValues().apply {
                put("特码", number)
                put("注额", amount)
                put("计数", 1)
                put("地区", tag)
                put("原始数据", originalData)
                put("标识", identifier)
                put("时间戳", getBatchTimestamp())
            }
            
            db.beginTransaction()
            try {
                db.insert(DatabaseHelper.TABLE_NAME, null, values)
                db.setTransactionSuccessful()
                success = true
            } catch (e: Exception) {
                Log.e("DatabaseUtils", "插入数据失败", e)
                // 可以添加自动重试逻辑
                var retryCount = 0
                while (!success && retryCount < 3) {
                    try {
                        db.insert(DatabaseHelper.TABLE_NAME, null, values)
                        db.setTransactionSuccessful()
                        success = true
                    } catch (e: Exception) {
                        Log.e("DatabaseUtils", "重试 ${retryCount + 1} 失败", e)
                        retryCount++
                        Thread.sleep(100) // 短暂延迟后重试
                    }
                }
            } finally {
                db.endTransaction()
            }
            success
        }
    }

    // 批量插入数据的方法
    fun insertBatchData(context: Context, dataList: List<BetData>): Boolean {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.writableDatabase.use { db ->
            var success = false
            db.beginTransaction()
            try {
                dataList.forEach { data ->
                    val values = ContentValues().apply {
                        put("特码", data.number)
                        put("注额", data.amount)
                        put("计数", 1)
                        put("地区", data.tag)
                        put("原始数据", data.originalData)
                        data.specialType?.let { put("投注类型", it) }
                        data.specialAmount?.let { put("特殊组合注额", it) }
                    }
                    db.insert(DatabaseHelper.TABLE_NAME, null, values)
                }
                db.setTransactionSuccessful()
                success = true
            } catch (e: Exception) {
                Log.e("DatabaseUtils", "批量插入失败", e)
            } finally {
                db.endTransaction()
            }
            success
        }
    }

    // 其他查询方法优化
    fun getTotalAmount(context: Context): Int {
        val dbHelper = DatabaseHelper(context)
        return try {
            dbHelper.readableDatabase.use { db ->
                val query = """
                    SELECT (
                        COALESCE(SUM(CASE 
                            WHEN 投注类型 IS NULL OR 投注类型 = 'normal' THEN 注额 
                            ELSE 特殊组合注额 
                        END), 0)
                    ) as total 
                    FROM ${DatabaseHelper.TABLE_NAME}
                """
                db.rawQuery(query, null).use { cursor ->
                    if (cursor.moveToFirst()) cursor.getInt(0) else 0
                }
            }
        } catch (e: Exception) {
            Log.e("DatabaseUtils", "获取总额失败", e)
            0
        }
    }

    // 数据类定义
    data class BetData(
        val number: Int,
        val amount: Int,
        val tag: String,
        val originalData: String,
        val specialType: String? = null,
        val specialAmount: Int? = null
    )

    private fun getTagSelection(showMacau: Boolean, showHongKong: Boolean): Pair<String, Array<String>> {
        return when {
            !showMacau && !showHongKong -> "" to emptyArray()
            !showMacau -> "地区 = '香港'" to emptyArray()
            !showHongKong -> "地区 = '澳门'" to emptyArray()
            else -> "" to emptyArray()
        }
    }
    fun getNumberStats(
        context: Context, 
        number: Int, 
        showMacau: Boolean = true, 
        showHongKong: Boolean = true,
        selectedIdentifiers: List<String>? = null
    ): NumberData {
        val dbHelper = DatabaseHelper(context)
        val db = dbHelper.readableDatabase

        var totalAmount = 0
        var macauAmount = 0
        var hongKongAmount = 0
        var count = 0
        val specialBets = mutableMapOf<String, Int>()

        try {
            // 构建查询条件
            var normalSelection = "特码 = ? AND (投注类型 IS NULL OR 投注类型 = 'normal')"
            val selectionArgs = mutableListOf(number.toString())

            // 如果没有选择任何地区，直接返回空数据
            if (!showMacau && !showHongKong) {
                return NumberData(0, 0, 0, 0, 0, 0, emptyMap())
            }

            // 添加地区过滤
            if (!showMacau) {
                normalSelection += " AND 地区 = '香港'"
            } else if (!showHongKong) {
                normalSelection += " AND 地区 = '澳门'"
            }

            // 添加标签过滤
            if (selectedIdentifiers != null) {
                if (selectedIdentifiers.isEmpty()) {
                    // 空列表表示只查询空标签
                    normalSelection += " AND (标识 IS NULL OR 标识 = '')"
                } else {
                    // 检查是否包含空字符串(表示空标签)
                    val hasEmptyTag = selectedIdentifiers.contains("")
                    val nonEmptyTags = selectedIdentifiers.filter { it.isNotEmpty() }
                    
                    if (hasEmptyTag && nonEmptyTags.isNotEmpty()) {
                        // 既有空标签又有其他标签
                        val placeholders = nonEmptyTags.joinToString(", ") { "?" }
                        normalSelection += " AND (标识 IN ($placeholders) OR 标识 IS NULL OR 标识 = '')"
                        selectionArgs.addAll(nonEmptyTags)
                    } else if (hasEmptyTag) {
                        // 只有空标签
                        normalSelection += " AND (标识 IS NULL OR 标识 = '')"
                    } else {
                        // 只有非空标签
                        val placeholders = selectedIdentifiers.joinToString(", ") { "?" }
                        normalSelection += " AND 标识 IN ($placeholders)"
                        selectionArgs.addAll(selectedIdentifiers)
                    }
                }
            }

            // 查询普通注额
            db.query(
                DatabaseHelper.TABLE_NAME,
                arrayOf("地区", "注额", "计数"),
                normalSelection,
                selectionArgs.toTypedArray(),
                null, null, null
            ).use { cursor ->
                while (cursor.moveToNext()) {
                    val tag = cursor.getString(0)
                    val amount = cursor.getInt(1)
                    when (tag) {
                        "澳门" -> macauAmount += amount
                        "香港" -> hongKongAmount += amount
                    }
                    count += cursor.getInt(2)
                    totalAmount += amount
                }
            }

            // 查询特殊注额 (同样添加标签过滤)
            var specialSelection = "特码 = ? AND 投注类型 != 'normal'"
            val specialSelectionArgs = mutableListOf(number.toString())

            // 添加地区过滤
            if (!showMacau) {
                specialSelection += " AND 地区 = '香港'"
            } else if (!showHongKong) {
                specialSelection += " AND 地区 = '澳门'"
            }

            // 添加标签过滤
            if (selectedIdentifiers != null) {
                if (selectedIdentifiers.isEmpty()) {
                    // 空列表表示只查询空标签
                    specialSelection += " AND (标识 IS NULL OR 标识 = '')"
                } else {
                    // 检查是否包含空字符串(表示空标签)
                    val hasEmptyTag = selectedIdentifiers.contains("")
                    val nonEmptyTags = selectedIdentifiers.filter { it.isNotEmpty() }
                    
                    if (hasEmptyTag && nonEmptyTags.isNotEmpty()) {
                        // 既有空标签又有其他标签
                        val placeholders = nonEmptyTags.joinToString(", ") { "?" }
                        specialSelection += " AND (标识 IN ($placeholders) OR 标识 IS NULL OR 标识 = '')"
                        specialSelectionArgs.addAll(nonEmptyTags)
                    } else if (hasEmptyTag) {
                        // 只有空标签
                        specialSelection += " AND (标识 IS NULL OR 标识 = '')"
                    } else {
                        // 只有非空标签
                        val placeholders = selectedIdentifiers.joinToString(", ") { "?" }
                        specialSelection += " AND 标识 IN ($placeholders)"
                        specialSelectionArgs.addAll(selectedIdentifiers)
                    }
                }
            }

            // 查询特殊注额
            db.query(
                DatabaseHelper.TABLE_NAME,
                arrayOf("投注类型", "SUM(注额) as amount"),
                specialSelection,
                specialSelectionArgs.toTypedArray(),
                "投注类型",
                null,
                null
            ).use { cursor ->
                while (cursor.moveToNext()) {
                    val type = cursor.getString(0)
                    val amount = cursor.getInt(1)
                    specialBets[type] = amount
                    totalAmount += amount
                }
            }

            return NumberData(
                totalAmount = totalAmount,
                macauAmount = macauAmount,
                hongKongAmount = hongKongAmount,
                count = count,
                specialAmount = specialBets.values.sum().toInt(),
                specialBets = specialBets
            )
        } finally {
            db.close()
        }
    }
    fun calculateNumberProfit(
        context: Context,
        number: Int,
        maxLoss: Int,
        showMacau: Boolean = true,
        showHongKong: Boolean = true
    ): Int {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            // 获取总额
            val totalSumCursor = db.rawQuery(
                "SELECT SUM(注额) FROM ${DatabaseHelper.TABLE_NAME}",
                null
            )
            val totalSum = if (totalSumCursor.moveToFirst()) totalSumCursor.getInt(0) else 0
            totalSumCursor.close()

            // 获取当前号码的注额
            val numberStats = getNumberStats(context, number, showMacau, showHongKong)
            
            // 计算盈利 = 总额 - (当前号码注额 × 47)
            totalSum - (numberStats.totalAmount * 47)
        }
    }
    fun calculateProfit(context: Context, maxLoss: Int): Map<Int, Int> {
        val profits = mutableMapOf<Int, Int>()
        for (number in 1..49) {
            profits[number] = calculateNumberProfit(context, number, maxLoss)
        }
        return profits
    }
    fun getStats(context: Context, showMacau: Boolean, showHongKong: Boolean): Stats {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            var query = "SELECT SUM(注额) FROM ${DatabaseHelper.TABLE_NAME}"
            if (!showMacau || !showHongKong) {
                query += " WHERE 地区 IN (${
                    listOfNotNull(
                        if (showMacau) "'澳门'" else null,
                        if (showHongKong) "'香港'" else null
                    ).joinToString(",")
                })"
            }
            
            val totalAmount = db.rawQuery(query, null).use { cursor ->
                if (cursor.moveToFirst()) cursor.getInt(0) else 0
            }
            val macauAmount = if (showMacau) {
                db.rawQuery(
                    "SELECT SUM(注额) FROM ${DatabaseHelper.TABLE_NAME} WHERE 地区 = '澳门'",
                    null
                ).use { cursor ->
                    if (cursor.moveToFirst()) cursor.getInt(0) else 0
                }
            } else 0
            val hongKongAmount = if (showHongKong) {
                db.rawQuery(
                    "SELECT SUM(注额) FROM ${DatabaseHelper.TABLE_NAME} WHERE 地区 = '香港'",
                    null
                ).use { cursor ->
                    if (cursor.moveToFirst()) cursor.getInt(0) else 0
                }
            } else 0
            // 计算最大盈利和赢率
            val (maxProfit, winRate) = calculateProfitStats(db)
            Stats(
                totalAmount = totalAmount,
                maxProfit = maxProfit,
                winRate = winRate,
                macauAmount = macauAmount,
                hongKongAmount = hongKongAmount
            )
        }
    }
    private fun calculateProfitStats(db: SQLiteDatabase): Pair<Int, Float> {
        var maxProfit = 0
        var winCount = 0
        var totalCount = 0
        // 获取总额
        val totalSum = db.rawQuery(
            "SELECT SUM(注额) FROM ${DatabaseHelper.TABLE_NAME}",
            null
        ).use { cursor ->
            if (cursor.moveToFirst()) cursor.getInt(0) else 0
        }
        // 获取所有不同的号码
        db.rawQuery("SELECT DISTINCT 特码, SUM(注额) FROM ${DatabaseHelper.TABLE_NAME} GROUP BY 特码", null).use { cursor ->
            while (cursor.moveToNext()) {
                val number = cursor.getInt(0)
                val numberAmount = cursor.getInt(1)
                val profit = totalSum - (numberAmount * 47)
                
                if (profit > maxProfit) {
                    maxProfit = profit
                }
                if (profit > 0) {
                    winCount++
                }
                totalCount++
            }
        }
        val winRate = if (totalCount > 0) {
            (winCount.toFloat() / totalCount) * 100
        } else {
            0f
        }
        return Pair(maxProfit, winRate)
    }
    fun clearDatabase(context: Context) {
        val dbHelper = DatabaseHelper(context)
        dbHelper.writableDatabase.use { db ->
            db.delete(DatabaseHelper.TABLE_NAME, null, null)
        }
    }
    fun getNumberHistory(context: Context, number: Int): List<Triple<Int, String, String>> {
        val history = mutableListOf<Triple<Int, String, String>>()
        val dbHelper = DatabaseHelper(context)
        
        dbHelper.readableDatabase.use { db ->
            db.rawQuery(
                """
                SELECT 注额, 地区, 时间戳 
                FROM ${DatabaseHelper.TABLE_NAME} 
                WHERE 特码 = ? 
                ORDER BY 时间戳 DESC
                LIMIT 10
                """,
                arrayOf(number.toString())
            ).use { cursor ->
                while (cursor.moveToNext()) {
                    history.add(
                        Triple(
                            cursor.getInt(0),
                            cursor.getString(1),
                            cursor.getString(2)
                        )
                    )
                }
            }
        }
        return history
    }
    fun deleteData(context: Context, number: Int, timestamp: String): Boolean {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.writableDatabase.use { db ->
            try {
                db.delete(
                    DatabaseHelper.TABLE_NAME,
                    "特码 = ? AND 时间戳 = ?",
                    arrayOf(number.toString(), timestamp)
                ) > 0
            } catch (e: Exception) {
                e.printStackTrace()
                false
            }
        }
    }
    fun getNumberCount(
        context: Context,
        number: Int,
        showMacau: Boolean = true,
        showHongKong: Boolean = true
    ): Int {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            var selection = "特码 = ?"
            val selectionArgs = mutableListOf(number.toString())
            if (!showMacau && !showHongKong) {
                return 0
            } else if (!showMacau) {
                selection += " AND 地区 = '香港'"
            } else if (!showHongKong) {
                selection += " AND 地区 = '澳门'"
            }
            db.query(
                DatabaseHelper.TABLE_NAME,
                arrayOf("SUM(计数)"),
                selection,
                selectionArgs.toTypedArray(),
                null,
                null,
                null
            ).use { cursor ->
                if (cursor.moveToFirst()) {
                    cursor.getInt(0)
                } else {
                    0
                }
            }
        }
    }
    fun getNumberCountByLabel(context: Context, number: Int, label: String): Int {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            val selection = "特码 = ? AND 地区 = ?"
            val selectionArgs = arrayOf(number.toString(), label)
            
            db.query(
                DatabaseHelper.TABLE_NAME,
                arrayOf("SUM(计数)"),
                selection,
                selectionArgs,
                null,
                null,
                null
            ).use { cursor ->
                if (cursor.moveToFirst()) {
                    cursor.getInt(0)
                } else {
                    0
                }
            }
        }
    }
    fun calculateProfitStats(
        context: Context, 
        maxLoss: Int,
        showMacau: Boolean = true,
        showHongKong: Boolean = true,
        selectedIdentifiers: List<String>? = null
    ): ProfitStats {
        // 获取设置中的特码赔率
        val specialNumberOdds = BettingOdds.getCurrentSettings().normalOdds["特码"]?.toDouble() ?: 47.0
        
        // 使用getNumberStats获取每个号码的数据，确保查询逻辑一致
        val allNumbersData = (1..49).associateWith { number ->
            getNumberStats(context, number, showMacau, showHongKong, selectedIdentifiers)
        }
        
        // 计算总额
        var totalAmount = 0
        var macauAmount = 0
        var hongKongAmount = 0
        
        // 通过汇总每个号码的数据计算总计
        allNumbersData.values.forEach { data ->
            totalAmount += data.totalAmount
            macauAmount += data.macauAmount
            hongKongAmount += data.hongKongAmount
        }
        
        // 计算每个号码的盈利，使用相同的赔率
        val profits = allNumbersData.map { (number, data) ->
            val profit = when {
                showMacau && showHongKong -> totalAmount - (data.totalAmount * specialNumberOdds).toInt()
                showMacau -> macauAmount - (data.macauAmount * specialNumberOdds).toInt()
                showHongKong -> hongKongAmount - (data.hongKongAmount * specialNumberOdds).toInt() 
                else -> 0
            }
            Triple(number, data.totalAmount, profit)
        }
        
        // 计算统计信息
        var maxProfit = Int.MIN_VALUE
        var winCount = 0
        
        profits.forEach { (_, _, profit) ->
            if (profit > maxProfit) {
                maxProfit = profit
            }
            if (profit >= 0) {
                winCount++
            }
        }
        
        val winRate = if (profits.isNotEmpty()) {
            (winCount.toFloat() / profits.size) * 100
        } else {
            0f
        }
        
        // 计算调整建议
        val adjustments = mutableMapOf<Int, Int>()
        var totalReduction = 0
        
        profits.forEach { (number, amount, profit) ->
            if (profit < -maxLoss && amount > 0) {
                val neededReduction = ((kotlin.math.abs(profit) - maxLoss) / specialNumberOdds).roundToInt()
                val adjustedAmount = maxOf(0, amount - neededReduction)
                val reduction = amount - adjustedAmount
                adjustments[number] = reduction
                totalReduction += reduction
            }
        }

        return ProfitStats(
            totalAmount = totalAmount,
            macauAmount = macauAmount,
            hongKongAmount = hongKongAmount,
            maxProfit = maxProfit,
            winRate = winRate,
            totalReduction = totalReduction,
            adjustedTotal = totalAmount - totalReduction,
            adjustments = adjustments
        )
    }

    fun saveToDatabase(context: Context, parsedResults: List<String>, originalData: String, tag: String, identifier: String) {
        val dbHelper = DatabaseHelper(context)
        val db = dbHelper.writableDatabase

        db.beginTransaction()
        try {
            var success = true
            parsedResults.forEach { result ->
                val parts = result.split(":")
                if (parts.size >= 2) {
                    val number = parts[0].toInt()
                    val amount = parts[1].toInt()
                    val betType = if (parts.size >= 3) parts[2] else "normal"

                    val values = ContentValues().apply {
                        put("特码", number)
                        put("注额", amount)
                        put("计数", 1)
                        put("地区", tag)
                        put("原始数据", originalData)
                        put("标识", identifier)
                        put("时间戳", getBatchTimestamp())
                        put("投注类型", betType)
                    }
                    if (db.insert(DatabaseHelper.TABLE_NAME, null, values) == -1L) {
                        success = false
                    }
                }
            }
            if (success) {
                db.setTransactionSuccessful()
            }
        } finally {
            db.endTransaction()
            db.close()
        }
    }

    // 添加一个方法来获取特殊组合的统计信息
    fun getSpecialBetsTotal(
        context: Context,
        selectedIdentifiers: List<String>? = null
    ): Int {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            var whereClause = "WHERE 特殊组合注额 IS NOT NULL"
            val selectionArgs = mutableListOf<String>()
            
            // 应用标签过滤
            if (selectedIdentifiers != null) {
                if (selectedIdentifiers.isEmpty()) {
                    // 空列表表示只查询空标签
                    whereClause += " AND (标识 IS NULL OR 标识 = '')"
                } else {
                    // 检查是否包含空字符串(表示空标签)
                    val hasEmptyTag = selectedIdentifiers.contains("")
                    val nonEmptyTags = selectedIdentifiers.filter { it.isNotEmpty() }
                    
                    if (hasEmptyTag && nonEmptyTags.isNotEmpty()) {
                        // 既有空标签又有其他标签
                        val placeholders = nonEmptyTags.joinToString(", ") { "?" }
                        whereClause += " AND (标识 IN ($placeholders) OR 标识 IS NULL OR 标识 = '')"
                        selectionArgs.addAll(nonEmptyTags)
                    } else if (hasEmptyTag) {
                        // 只有空标签
                        whereClause += " AND (标识 IS NULL OR 标识 = '')"
                    } else {
                        // 只有非空标签
                        val placeholders = selectedIdentifiers.joinToString(", ") { "?" }
                        whereClause += " AND 标识 IN ($placeholders)"
                        selectionArgs.addAll(selectedIdentifiers)
                    }
                }
            }
            
            db.rawQuery(
                "SELECT SUM(特殊组合注额) FROM ${DatabaseHelper.TABLE_NAME} $whereClause",
                if (selectionArgs.isEmpty()) null else selectionArgs.toTypedArray()
            ).use { cursor ->
                if (cursor.moveToFirst()) cursor.getInt(0) else 0
            }
        }
    }

    // 获取澳门特殊组合总额
    fun getMacauSpecialBetsTotal(
        context: Context,
        selectedIdentifiers: List<String>? = null
    ): Int {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            var whereClause = "WHERE 特殊组合注额 IS NOT NULL AND 地区 = '澳门'"
            val selectionArgs = mutableListOf<String>()
            
            // 应用标签过滤
            if (selectedIdentifiers != null) {
                if (selectedIdentifiers.isEmpty()) {
                    // 空列表表示只查询空标签
                    whereClause += " AND (标识 IS NULL OR 标识 = '')"
                } else {
                    // 检查是否包含空字符串(表示空标签)
                    val hasEmptyTag = selectedIdentifiers.contains("")
                    val nonEmptyTags = selectedIdentifiers.filter { it.isNotEmpty() }
                    
                    if (hasEmptyTag && nonEmptyTags.isNotEmpty()) {
                        // 既有空标签又有其他标签
                        val placeholders = nonEmptyTags.joinToString(", ") { "?" }
                        whereClause += " AND (标识 IN ($placeholders) OR 标识 IS NULL OR 标识 = '')"
                        selectionArgs.addAll(nonEmptyTags)
                    } else if (hasEmptyTag) {
                        // 只有空标签
                        whereClause += " AND (标识 IS NULL OR 标识 = '')"
                    } else {
                        // 只有非空标签
                        val placeholders = selectedIdentifiers.joinToString(", ") { "?" }
                        whereClause += " AND 标识 IN ($placeholders)"
                        selectionArgs.addAll(selectedIdentifiers)
                    }
                }
            }
            
            db.rawQuery(
                "SELECT SUM(特殊组合注额) FROM ${DatabaseHelper.TABLE_NAME} $whereClause",
                if (selectionArgs.isEmpty()) null else selectionArgs.toTypedArray()
            ).use { cursor ->
                if (cursor.moveToFirst()) cursor.getInt(0) else 0
            }
        }
    }

    // 获取香港特殊组合总额
    fun getHongKongSpecialBetsTotal(
        context: Context,
        selectedIdentifiers: List<String>? = null
    ): Int {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            var whereClause = "WHERE 特殊组合注额 IS NOT NULL AND 地区 = '香港'"
            val selectionArgs = mutableListOf<String>()
            
            // 应用标签过滤
            if (selectedIdentifiers != null) {
                if (selectedIdentifiers.isEmpty()) {
                    // 空列表表示只查询空标签
                    whereClause += " AND (标识 IS NULL OR 标识 = '')"
                } else {
                    // 检查是否包含空字符串(表示空标签)
                    val hasEmptyTag = selectedIdentifiers.contains("")
                    val nonEmptyTags = selectedIdentifiers.filter { it.isNotEmpty() }
                    
                    if (hasEmptyTag && nonEmptyTags.isNotEmpty()) {
                        // 既有空标签又有其他标签
                        val placeholders = nonEmptyTags.joinToString(", ") { "?" }
                        whereClause += " AND (标识 IN ($placeholders) OR 标识 IS NULL OR 标识 = '')"
                        selectionArgs.addAll(nonEmptyTags)
                    } else if (hasEmptyTag) {
                        // 只有空标签
                        whereClause += " AND (标识 IS NULL OR 标识 = '')"
                    } else {
                        // 只有非空标签
                        val placeholders = selectedIdentifiers.joinToString(", ") { "?" }
                        whereClause += " AND 标识 IN ($placeholders)"
                        selectionArgs.addAll(selectedIdentifiers)
                    }
                }
            }
            
            db.rawQuery(
                "SELECT SUM(特殊组合注额) FROM ${DatabaseHelper.TABLE_NAME} $whereClause",
                if (selectionArgs.isEmpty()) null else selectionArgs.toTypedArray()
            ).use { cursor ->
                if (cursor.moveToFirst()) cursor.getInt(0) else 0
            }
        }
    }

    // 获取澳门总额
    fun getMacauTotal(context: Context): Int {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            db.rawQuery(
                """
                SELECT SUM(CASE 
                    WHEN 投注类型 IS NULL OR 投注类型 = 'normal' THEN 注额 
                    ELSE 特殊组合注额 
                END)
                FROM ${DatabaseHelper.TABLE_NAME} 
                WHERE 地区 = '澳门'
                """,
                null
            ).use { cursor ->
                if (cursor.moveToFirst()) cursor.getInt(0) else 0
            }
        }
    }

    // 获取香港总额
    fun getHongKongTotal(context: Context): Int {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            db.rawQuery(
                """
                SELECT SUM(CASE 
                    WHEN 投注类型 IS NULL OR 投注类型 = 'normal' THEN 注额 
                    ELSE 特殊组合注额 
                END)
                FROM ${DatabaseHelper.TABLE_NAME} 
                WHERE 地区 = '香港'
                """,
                null
            ).use { cursor ->
                if (cursor.moveToFirst()) cursor.getInt(0) else 0
            }
        }
    }
    fun getSpecialBetsStats(context: Context, betType: String? = null): Map<String, Int> {
        val dbHelper = DatabaseHelper(context)
        val db = dbHelper.readableDatabase
        val stats = mutableMapOf<String, Int>()

        try {
            val selection = if (betType != null) "投注类型 = ?" else "投注类型 != 'normal'"
            val selectionArgs = if (betType != null) arrayOf(betType) else null

            val cursor = db.query(
                DatabaseHelper.TABLE_NAME,
                arrayOf("投注类型", "SUM(注额) as total"),
                selection,
                selectionArgs,
                "投注类型",
                null,
                null
            )

            cursor.use {
                while (it.moveToNext()) {
                    val type = it.getString(0)
                    val total = it.getInt(1)
                    stats[type] = total
                }
            }
        } finally {
            db.close()
        }

        return stats
    }

    /**
     * 插入特殊注额数据
     */
    fun insertSpecialBet(
        context: Context,
        numbers: List<Int>,
        amount: Int,
        betType: String,
        tag: String,
        identifier: String,
        originalData: String
    ): Boolean {
        // Log.d("DatabaseUtils", "开始插入特殊注额: betType=$betType, amount=$amount, originalData=$originalData")
        val dbHelper = DatabaseHelper(context)
        return try {
            dbHelper.writableDatabase.use { db ->
                val values = ContentValues().apply {
                    putNull("特码")
                    putNull("注额")
                    put("计数", 1)
                    put("地区", tag)
                    put("原始数据", originalData)
                    put("特殊组合注额", amount)
                    put("标识", identifier)
                    put("时间戳", getBatchTimestamp())
                    put("投注类型", betType)

                    val formattedType = when {
                        betType.contains("平特尾") -> {
                            // Log.d("DatabaseUtils", "处理平特尾数据保存: $betType")
                            
                            // 从betType中提取尾数，而不是从原始数据中提取
                            // 首先检查是否已经包含尾数信息
                            if (betType.contains("平特尾_")) {
                                // 已经包含尾数信息，直接使用
                                val tail = betType.substringAfter("平特尾_").firstOrNull()?.toString() ?: "0"
                                val tailNumber = tail.toIntOrNull() ?: 0
                                
                                val numbersList = (1..49)
                                    .filter { it % 10 == tailNumber }
                                    .map { String.format("%02d", it) }
                                    .joinToString(",")
                                
                                // Log.d("DatabaseUtils", "平特尾号码列表: $numbersList")
                                put("号码列表", numbersList)
                                betType  // 直接使用原始betType
                            } else {
                                // 从号码列表中推断尾数
                                val tailDigit = numbers.firstOrNull()?.let { it % 10 } ?: 0
                                val numbersList = numbers.joinToString(",") { String.format("%02d", it) }
                                
                                // Log.d("DatabaseUtils", "平特尾号码列表: $numbersList")
                                put("号码列表", numbersList)
                                val formattedType = "平特尾_$tailDigit"
                                // Log.d("DatabaseUtils", "特殊组合类型: $formattedType")
                                formattedType
                            }
                        }
                        betType.contains("二中二") -> {
                            put("号码列表", numbers.joinToString(","))
                            "二中二_${numbers.joinToString("-")}"
                        }
                        betType.contains("三中二") -> {
                            put("号码列表", numbers.joinToString(","))
                            "三中二_${numbers.joinToString("-")}"
                        }
                        betType.contains("三中三") -> {
                            put("号码列表", numbers.joinToString(","))
                            "三中三_${numbers.joinToString("-")}"
                        }
                        betType.contains("平特") -> {
                            val allZodiacs = numbers
                                .mapNotNull { ZodiacUtils.getZodiacForNumber(it) }
                                .distinct()
                                .sorted()
                            put("号码列表", numbers.joinToString(","))
                            "平特_${allZodiacs.first()}"
                        }
                        betType.contains("特肖") -> {
                            val allZodiacs = numbers
                                .mapNotNull { ZodiacUtils.getZodiacForNumber(it) }
                                .distinct()
                                .sorted()
                            put("号码列表", numbers.joinToString(","))
                            "特肖_${allZodiacs.first()}"
                        }
                        betType.contains("连肖") -> {
                            val allZodiacs = numbers
                                .mapNotNull { ZodiacUtils.getZodiacForNumber(it) }
                                .distinct()
                                .sorted()
                            put("号码列表", numbers.joinToString(","))
                            "${allZodiacs.size}连肖_${allZodiacs.joinToString("")}"
                        }
                        betType.contains("不中") -> {
                            // 处理五不中到十一不中
                            put("号码列表", numbers.joinToString(","))
                            "${betType}_${numbers.joinToString("-")}"
                        }
                        betType.startsWith("包") -> {
                            // 处理包类型的号码列表保存
                            put("号码列表", numbers.joinToString(","))
                            betType
                        }
                        betType.contains("波") -> {
                            // 处理波色类型
                            put("号码列表", numbers.joinToString(","))
                            betType
                        }
                        betType.contains("合数") -> {
                            // 处理合数类型
                            put("号码列表", numbers.joinToString(","))
                            betType
                        }
                        else -> {
                            // 其他所有特殊类型都保存号码列表
                            put("号码列表", numbers.joinToString(","))
                            betType
                        }
                    }
                    
                    // Log.d("DatabaseUtils", "格式化后的特殊组合类型: $formattedType")
                    put("特殊组合类型", formattedType)
                }
                val result = db.insert(DatabaseHelper.TABLE_NAME, null, values) != -1L
                // Log.d("DatabaseUtils", "特殊注额插入结果: $result")
                result
            }
        } catch (e: Exception) {
            // Log.e("DatabaseUtils", "插入特殊注额失败", e)
            e.printStackTrace()
            false
        }
    }

    // 数据类
    data class OriginalDataInfo(
        val originalData: String,
        val area: String,
        val timestamp: String,
        val numberDetails: List<NumberAmountPair>,
        val totalAmount: Int,
        val count: Int,
        val betType: String = "normal",
        val specialAmount: Int = 0,
        val specialType: String? = null,
        val specialBets: List<SpecialBet> = emptyList(),
        val identifier: String = ""
    )

    data class NumberAmountPair(
        val number: Int,
        val amount: Int
    )

    // 新增数据类来存储特殊组合信息
    data class SpecialBet(
        val type: String,
        val amount: Int
    )

    // 获取原始数据列表
    fun getOriginalDataList(context: Context): List<OriginalDataInfo> {
        // Log.d("DatabaseUtils", "开始查询原始数据列表")
        val dbHelper = DatabaseHelper(context)
        return dbHelper.readableDatabase.use { db ->
            val result = mutableListOf<OriginalDataInfo>()
            
            // 1. 获取不重复的原始数据，按时间戳分组
            val distinctQuery = """
                SELECT 
                    原始数据, 
                    地区, 
                    时间戳,
                    GROUP_CONCAT(DISTINCT 投注类型) as bet_types,
                    标识
                FROM ${DatabaseHelper.TABLE_NAME}
                WHERE 原始数据 IS NOT NULL
                GROUP BY 原始数据, 地区, 时间戳, 标识
                ORDER BY ROWID DESC
            """.trimIndent()
            
            db.rawQuery(distinctQuery, null).use { distinctCursor ->
                while (distinctCursor.moveToNext()) {
                    try {
                        val originalData = distinctCursor.getString(0) ?: continue
                        val area = distinctCursor.getString(1) ?: continue
                        val timestamp = distinctCursor.getString(2) ?: continue
                        val betTypes = distinctCursor.getString(3)?.split(",") ?: listOf("normal")
                        val identifier = distinctCursor.getString(4) ?: ""
                        
                        // 2. 查询这条原始数据的所有特码记录
                        val numberDetails = mutableListOf<NumberAmountPair>()
                        val numberQuery = """
                            SELECT 特码, 注额
                            FROM ${DatabaseHelper.TABLE_NAME}
                            WHERE 原始数据 = ? 
                            AND 地区 = ? 
                            AND 时间戳 = ? 
                            AND 特码 IS NOT NULL
                        """
                        db.rawQuery(numberQuery, arrayOf(originalData, area, timestamp)).use { numberCursor ->
                            while (numberCursor.moveToNext()) {
                                val number = numberCursor.getInt(0)
                                val amount = numberCursor.getInt(1)
                                numberDetails.add(NumberAmountPair(number, amount))
                            }
                        }
                        
                        // 3. 查询这条原始数据的所有特殊组合记录
                        val specialBets = mutableListOf<SpecialBet>()
                        val specialQuery = """
                            SELECT 特殊组合类型, 特殊组合注额
                            FROM ${DatabaseHelper.TABLE_NAME}
                            WHERE 原始数据 = ? 
                            AND 地区 = ? 
                            AND 时间戳 = ? 
                            AND 特殊组合类型 IS NOT NULL
                        """
                        db.rawQuery(specialQuery, arrayOf(originalData, area, timestamp)).use { specialCursor ->
                            while (specialCursor.moveToNext()) {
                                val type = specialCursor.getString(0)
                                val amount = specialCursor.getInt(1)
                                specialBets.add(SpecialBet(type, amount))
                            }
                        }
                        
                        // 4. 计算总金额和总注数
                        val normalAmount = numberDetails.sumOf { it.amount }
                        val specialAmount = specialBets.sumOf { it.amount }
                        
                        result.add(OriginalDataInfo(
                            originalData = originalData,
                            area = area,
                            timestamp = timestamp,
                            numberDetails = numberDetails,
                            totalAmount = normalAmount + specialAmount,
                            count = numberDetails.size + specialBets.size,
                            betType = betTypes.joinToString(","),
                            specialAmount = specialAmount,
                            specialType = specialBets.map { it.type }.joinToString(","),
                            specialBets = specialBets,
                            identifier = identifier
                        ))
                    } catch (e: Exception) {
                        Log.e("DatabaseUtils", "解析数据失败: ${e.message}")
                        continue
                    }
                }
            }
            
            // Log.d("DatabaseUtils", "查询完成，共 ${result.size} 条数据")
            result
        }
    }

    // 删除原始数据及相关记录
    fun deleteOriginalDataAndRelated(context: Context, originalData: String): Boolean {
        val dbHelper = DatabaseHelper(context)
        return dbHelper.writableDatabase.use { db ->
            var success = false
            db.beginTransaction()
            try {
                // 直接删除这条原始数据对应的所有记录
                // 不需要更新其他记录，因为我们只想删除这条原始数据解析出来的内容
                val deleteResult = db.delete(
                    DatabaseHelper.TABLE_NAME,
                    "原始数据 = ?",
                    arrayOf(originalData)
                )
                
                if (deleteResult > 0) {
                    // Log.d("DatabaseUtils", "成功删除原始数据: $originalData, 影响行数: $deleteResult")
                    db.setTransactionSuccessful()
                    success = true
                } else {
                    Log.e("DatabaseUtils", "删除原始数据失败: $originalData")
                }
            } catch (e: Exception) {
                Log.e("DatabaseUtils", "删除原始数据失败", e)
            } finally {
                db.endTransaction()
            }
            success
        }
    }

    // 扩展函数：安全获取Int值
    private fun android.database.Cursor.getIntOrNull(columnIndex: Int): Int? {
        return if (isNull(columnIndex)) null else getInt(columnIndex)
    }

    fun deleteOriginalDataByTimestamp(context: Context, timestamp: String) {
        val dbHelper = DatabaseHelper(context)
        dbHelper.writableDatabase.use { db ->
            db.beginTransaction()
            try {
                // 使用正确的表名和列名删除数据
                db.delete(
                    DatabaseHelper.TABLE_NAME,  // 使用常量定义的表名
                    "时间戳 = ?",
                    arrayOf(timestamp)
                )
                
                // 提交事务
                db.setTransactionSuccessful()
            } catch (e: Exception) {
                Log.e("DatabaseUtils", "删除数据失败: ${e.message}")
            } finally {
                db.endTransaction()
            }
        }
    }

    /**
     * 插入普通注额数据
     */
    fun insertBet(
        context: Context,
        number: Int,
        amount: Int,
        tag: String,
        identifier: String,
        originalData: String
    ): Boolean {
        // Log.d("DatabaseUtils", "开始插入普通注额: number=$number, amount=$amount")
        val dbHelper = DatabaseHelper(context)
        return try {
            dbHelper.writableDatabase.use { db ->
                val values = ContentValues().apply {
                    put("特码", number)
                    put("注额", amount)
                    put("计数", 1)
                    put("地区", tag)
                    put("标识", identifier)
                    put("原始数据", originalData)
                    put("时间戳", getBatchTimestamp())
                    put("投注类型", "normal")
                }
                db.insert(DatabaseHelper.TABLE_NAME, null, values) != -1L
            }
        } catch (e: Exception) {
            Log.e("DatabaseUtils", "插入普通注额失败", e)
            false
        }
    }
}
