package com.example.myshujuguanli.ui.screens

import android.content.ClipboardManager
import android.content.Context
import android.view.Gravity
import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.BugReport
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Storage
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathMeasure
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myshujuguanli.settings.AnimationSettings
import com.example.myshujuguanli.ui.components.AddIdentifierDialog
import com.example.myshujuguanli.ui.components.DeleteIdentifierDialog
import com.example.myshujuguanli.ui.components.IdentifierCard
import com.example.myshujuguanli.utils.DataParser
import com.example.myshujuguanli.utils.DatabaseUtils
import com.example.myshujuguanli.utils.ParseLearningEngine
import com.example.myshujuguanli.utils.SecurityUtils
import com.example.myshujuguanli.utils.SoundUtils
import com.example.myshujuguanli.utils.SpecialBetResult
import com.example.myshujuguanli.utils.VibrationUtils
import com.example.myshujuguanli.utils.ZodiacUtils
import org.json.JSONArray
import org.json.JSONObject


// 在文件顶部添加常量
private const val PREFS_NAME = "input_state"
private const val KEY_INPUT_TEXT = "input_text"
private const val KEY_OUTPUT_TEXT = "output_text"
private const val KEY_LAST_SAVED_DATA = "last_saved_data"
private const val KEY_LAST_SPECIAL_BETS = "last_special_bets"
private const val KEY_CURRENT_TAG = "current_tag"
private const val KEY_LAST_SAVED_TAG = "last_saved_tag"

// 定义地区状态密封类
sealed class RegionState {
    data object None : RegionState()
    data object HongKong : RegionState()
    data object Macau : RegionState()
    data object Conflict : RegionState()
}

@Composable
fun DataInputScreen(
    isTabletMode: Boolean = false,
    identifiers: List<String>,
    selectedIdentifier: String,
    onUpdateIdentifiers: (List<String>) -> Unit,
    onUpdateSelectedIdentifier: (String) -> Unit,
    onDeleteIdentifier: (String) -> Unit,
    onOpenManagement: () -> Unit,
    onOpenSettings: () -> Unit,
    onCheckStatus: () -> Unit,
    onOpenDebug: () -> Unit,
    isManagementEnabled: Boolean,
    appStatus: SecurityUtils.StatusInfo?,
    onOpenUserGuide: () -> Unit
) {
    val context = LocalContext.current
    val localClipboardManager = LocalClipboardManager.current
    // 添加协程作用域
    val scope = rememberCoroutineScope()
    
    // 在屏幕首次加载时，加载动画设置
    LaunchedEffect(Unit) {
        AnimationSettings.load(context)
    }
    
    // 从 SharedPreferences 读取所有保存的状态
    var inputText by remember { 
        mutableStateOf(
            context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .getString(KEY_INPUT_TEXT, "") ?: ""
        )
    }
    var outputText by remember { 
        mutableStateOf(
            context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .getString(KEY_OUTPUT_TEXT, "") ?: ""
        )
    }
    var lastSavedData by remember {
        mutableStateOf(
            context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .getString(KEY_LAST_SAVED_DATA, null)
                ?.let { json -> jsonToMap(json) }
        )
    }
    var lastSpecialBets by remember {
        mutableStateOf(
            context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .getString(KEY_LAST_SPECIAL_BETS, null)
                ?.let { json -> jsonToSpecialBets(json) } ?: emptyList()
        )
    }
    var currentTag by remember {
        mutableStateOf(
            context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                .getString(KEY_CURRENT_TAG, "澳门") ?: "澳门"
        )
    }

    // 使用 derivedStateOf 来优化状态计算
    val regionState by remember(inputText) {
        derivedStateOf { 
            // 添加日志输出，方便调试
            val state = checkRegion(inputText)
            // println("检测到输入文本: $inputText")
            // println("地区状态: $state")
            state
        }
    }
    
    // 使用 regionState 来控制UI和逻辑
    LaunchedEffect(regionState) {
        // 添加日志输出，方便调试
        // println("regionState 发生变化: $regionState")
        
        when (regionState) {
            RegionState.Conflict -> {
                outputText = "⚠️ 检测到同时存在香港和澳门\n请分开单独输入"
                // 不修改输入文本，只中断解析
            }
            RegionState.HongKong -> {
                currentTag = "香港"
                // println("更新地区标签为: $currentTag")
                context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                    .edit()
                    .putString(KEY_CURRENT_TAG, currentTag)
                    .apply()
            }
            RegionState.Macau -> {
                currentTag = "澳门"
                // println("更新地区标签为: $currentTag")
                context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                    .edit()
                    .putString(KEY_CURRENT_TAG, currentTag)
                    .apply()
            }
            RegionState.None -> {
                currentTag = "澳门" // 默认澳门
                // println("更新地区标签为: $currentTag")
                context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                    .edit()
                    .putString(KEY_CURRENT_TAG, currentTag)
                    .apply()
            }
        }
    }

    // 保存所有状态
    fun saveAllStates() {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putString(KEY_INPUT_TEXT, inputText)
            .putString(KEY_OUTPUT_TEXT, outputText)
            .putString(KEY_LAST_SAVED_DATA, mapToJson(lastSavedData))
            .putString(KEY_LAST_SPECIAL_BETS, specialBetsToJson(lastSpecialBets))
            .putString(KEY_CURRENT_TAG, currentTag)
            .apply()
    }

    // 清空所有状态
    fun clearAllStates() {
        inputText = ""
        outputText = ""
        lastSavedData = null
        lastSpecialBets = emptyList()
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .clear()
            .apply()
    }

    var lastInputText by remember { mutableStateOf("") }
    var dataCount by remember { mutableStateOf(0) }
    var totalAmount by remember { mutableStateOf(0) }
    
    val stats by remember(outputText) {
        derivedStateOf { calculateStats(outputText) }
    }
    
    // 加载总注额
    LaunchedEffect(Unit) {
        totalAmount = DatabaseUtils.getTotalAmount(context)
    }
    
    // 添加 clipboardManager 声明
    val clipboardManager = remember { 
        context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager 
    }
    
    // 创建一个 ColoredNumberTransformation 实例并记住它
    val colorTransformation = remember { ColoredNumberTransformation() }
    
    // 优化颜色映射更新
    val colorMapping by remember(outputText) {
        derivedStateOf { ColorManager.updateColorMapping(outputText) }
    }

    LaunchedEffect(Unit) {
        dataCount = DatabaseUtils.getDataCount(context)
    }

    // 添加状态变量
    var statusInfo by remember { mutableStateOf<SecurityUtils.StatusInfo?>(null) }

    // 在 LaunchedEffect 中检查状态
    LaunchedEffect(Unit) {
        // 获取是否是冷启动
        val isColdStart = context.getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
            .getBoolean("is_cold_start", true)
            
        // 如果不是冷启动，才执行状态检查
        if (!isColdStart) {
            // Log.d("DataInputScreen", "执行状态检查")
            
            val prefs = context.getSharedPreferences(SecurityUtils.PREFS_NAME, Context.MODE_PRIVATE)
            val localStatus = SecurityUtils.getCurrentLocalStatus(context)
            
            // Log.d("DataInputScreen", "本地状态: $localStatus")
            
            // 如果是永久激活，直接使用本地状态
            if (localStatus.status == SecurityUtils.AppStatus.ACTIVATED) {
                // Log.d("DataInputScreen", "永久激活状态，使用本地状态")
                statusInfo = localStatus
                return@LaunchedEffect
            }
            
            // 获取当前服务器时间和到期时间
            val currentServerTime = SecurityUtils.getCurrentServerTime(context)
            val expiryTime = prefs.getLong(SecurityUtils.KEY_EXPIRY_TIME, 0)
            val serverTimeElapsed = prefs.getLong(SecurityUtils.KEY_SERVER_TIME_ELAPSED, 0)
            
            // Log.d("DataInputScreen", """
            //     时间检查:
            //     当前服务器时间: $currentServerTime (${java.util.Date(currentServerTime)})
            //     到期时间: $expiryTime (${java.util.Date(expiryTime)})
            //     elapsed保存时间: $serverTimeElapsed
            //     当前elapsed: ${SystemClock.elapsedRealtime()}
            //     时间差: ${expiryTime - currentServerTime} ms
            //     是否过期: ${expiryTime <= currentServerTime}
            //     是否即将过期: ${(expiryTime - currentServerTime) <= 5 * 60 * 1000}
            // """.trimIndent())
            
            // 如果已过期，则通知MainActivity进行检查
            if (expiryTime <= currentServerTime) {
                // Log.d("DataInputScreen", "检测到过期，通知MainActivity进行状态检查")
                onCheckStatus()
                statusInfo = localStatus
            } else {
                // Log.d("DataInputScreen", "未过期，使用本地状态")
                statusInfo = localStatus
            }
        } else {
            // 冷启动时，重置冷启动标志
            context.getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
                .edit()
                .putBoolean("is_cold_start", false)
                .apply()
        }
    }

    var originalInput by remember { mutableStateOf<String?>(null) }
    var currentInput by remember { mutableStateOf("") }
    
    var showAddIdentifierDialog by remember { mutableStateOf(false) }
    var showDeleteIdentifierDialog by remember { mutableStateOf(false) }
    var identifierToDelete by remember { mutableStateOf("") }

    var editMode by remember { mutableStateOf(false) }

    // 在DataInputScreen顶层添加一个状态
    var needsColorRefresh by remember { mutableStateOf(false) }

    // 在DataInputScreen顶层添加标识统计状态管理
    var showIdentifierStatsDialog by remember { mutableStateOf(false) }
    var identifierStats by remember { mutableStateOf<List<Triple<String, Int, Int>>>(emptyList()) }

    Box(modifier = Modifier
        .fillMaxSize()
        .background(MaterialTheme.colorScheme.background)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    top = WindowInsets.statusBars.asPaddingValues().calculateTopPadding() + 8.dp, // 输入框到顶部的距离
                    start = 16.dp,
                    end = 16.dp,
                    bottom = 16.dp
                )
        ) {
            // 输入区域卡片
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.4f)
                    .animatedBorder(
                        enabled = AnimationSettings.animationEnabledInput,
                        borderWidth = 2.dp,
                        shape = MaterialTheme.shapes.medium,
                        brush = SolidColor(MaterialTheme.colorScheme.primary.copy(alpha = AnimationSettings.animationAlphaInput)),
                        durationMillis = AnimationSettings.durationMillisInput,
                        snakeLengthPercent = AnimationSettings.snakeLengthPercentInput,
                        clockwise = AnimationSettings.animationDirectionInput,
                        animationType = AnimationSettings.animationTypeInput
                    ),
                elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
                shape = MaterialTheme.shapes.medium,
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(5.dp),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 输入区域
                    Column(
                        modifier = Modifier
                            .weight(0.92f)
                            .fillMaxHeight()
                    ) {
                        // 标题和统计信息行
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Card(
                                modifier = Modifier
                                    .width(100.dp),
                                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                                    colors = CardDefaults.cardColors(
                                        containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.95f)
                                    ),
                                    shape = MaterialTheme.shapes.medium  // 确保使用圆角形状
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable(
                                                interactionSource = remember { MutableInteractionSource() },
                                                indication = rememberRipple(bounded = true, color = MaterialTheme.colorScheme.primary),
                                                onClick = { 
                                                    identifierStats = generateIdentifierStats(context)
                                                    showIdentifierStatsDialog = true
                                                }
                                            ),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(horizontal = 6.dp, vertical = 3.dp),
                                            horizontalArrangement = Arrangement.Center,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Text(
                                                text = "已记录 $dataCount 条",
                                                style = MaterialTheme.typography.bodySmall.copy(
                                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                                                ),
                                                color = MaterialTheme.colorScheme.onPrimaryContainer
                                            )
                                        }
                                    }
                                }
                            
                            // 统计信息卡片
                            Card(
                                modifier = Modifier.width(270.dp),
                                elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.95f)
                                )
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = 6.dp, vertical = 3.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    // 标签部分
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(1.dp),
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.width(36.dp)
                                    ) {
                                        // Text(
                                        //     text = "标识:",
                                        //     style = MaterialTheme.typography.bodySmall,
                                        //     color = MaterialTheme.colorScheme.onPrimaryContainer
                                        // )
                                        Text(
                                            text = currentTag,
                                            style = MaterialTheme.typography.bodySmall.copy(
                                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                                            ),
                                            color = MaterialTheme.colorScheme.onPrimaryContainer
                                        )
                                        Text(
                                            text = ":",
                                            style = MaterialTheme.typography.bodySmall.copy(
                                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                                            ),
                                            color = MaterialTheme.colorScheme.onPrimaryContainer
                                        )
                                    }
                                    
                                    // 金额和码数容器
                                    Row(
                                        modifier = Modifier.weight(1f),
                                        horizontalArrangement = Arrangement.End,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        // 金额
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(1.dp),
                                            verticalAlignment = Alignment.CenterVertically,
                                            modifier = Modifier.padding(end = 8.dp)
                                        ) {
                                            Text(
                                                text = "金额:",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSecondaryContainer
                                            )
                                            Text(
                                                text = stats.second.toString(),
                                                style = MaterialTheme.typography.bodySmall.copy(
                                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                                                ),
                                                color = MaterialTheme.colorScheme.onSecondaryContainer
                                            )
                                        }
                                        
                                        // 码数部分
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(1.dp),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Text(
                                                text = "码数:",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSecondaryContainer
                                            )
                                            Text(
                                                text = stats.first.toString(),
                                                style = MaterialTheme.typography.bodySmall.copy(
                                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                                                ),
                                                color = MaterialTheme.colorScheme.onSecondaryContainer
                                            )
                                        }
                                    }
                                }
                            }
                        }

                        // 输入框
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    MaterialTheme.colorScheme.surface,
                                    shape = MaterialTheme.shapes.small
                                )
                                .verticalScroll(rememberScrollState())
                        ) {
                            ColoredTextField(
                                value = inputText,
                                onValueChange = { newText ->
                                    if (editMode) {
                                        inputText = newText
                                        if (newText != lastInputText) {
                                            lastInputText = newText
                                            currentTag = if (newText.contains(Regex("[香港]"))) "香港" else "澳门"
                                        }
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .fillMaxHeight()
                                    .padding(8.dp),
                                readOnly = !editMode,
                                outputText = outputText,
                                needsRefresh = needsColorRefresh,
                                onRefreshComplete = { needsColorRefresh = false }
                            )
                            
                            // 添加调试按钮
                            FloatingActionButton(
                                onClick = onOpenDebug,
                                modifier = Modifier
                                    .align(Alignment.BottomEnd)
                                    .padding(bottom = 46.dp, end = 6.dp)  // 位于编辑按钮上方
                                    .size(32.dp),
                                containerColor = MaterialTheme.colorScheme.surfaceVariant,
                                contentColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                shape = CircleShape
                            ) {
                                Icon(
                                    imageVector = Icons.Default.BugReport,
                                    contentDescription = "调试模式",
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                            
                            // 编辑悬浮按钮
                            FloatingActionButton(
                                onClick = { editMode = !editMode },
                                modifier = Modifier
                                    .align(Alignment.BottomEnd)
                                    .padding(6.dp)
                                    .size(32.dp),
                                containerColor = if (editMode) 
                                    MaterialTheme.colorScheme.primaryContainer
                                else 
                                    MaterialTheme.colorScheme.surfaceVariant,
                                contentColor = if (editMode)
                                    MaterialTheme.colorScheme.onPrimaryContainer
                                else
                                    MaterialTheme.colorScheme.onSurfaceVariant,
                                shape = CircleShape
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = if (editMode) "完成编辑" else "开始编辑",
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }

                    // 标识卡片
                    IdentifierCard(
                        identifiers = identifiers,
                        selectedIdentifier = selectedIdentifier,
                        onIdentifierSelected = onUpdateSelectedIdentifier,
                        onAddIdentifier = { showAddIdentifierDialog = true },
                        onDeleteIdentifier = onDeleteIdentifier,
                        onIdentifierUpdate = { oldName, newName, rebate ->
                            val newIdentifiers = identifiers.map { if (it == oldName) newName else it }
                            // 更新返水值（如果标识名变了要迁移返水值）
                            val prefs = context.getSharedPreferences("identifier_prefs", Context.MODE_PRIVATE)
                            if (oldName != newName) {
                                val oldRebate = prefs.getFloat("identifier_rebates_$oldName", 0f)
                                prefs.edit()
                                    .remove("identifier_rebates_$oldName")
                                    .putFloat("identifier_rebates_$newName", rebate)
                                    .apply()
                            } else {
                                prefs.edit().putFloat("identifier_rebates_$newName", rebate).apply()
                            }
                            onUpdateIdentifiers(newIdentifiers)
                            if (selectedIdentifier == oldName) {
                                onUpdateSelectedIdentifier(newName)
                            }
                        },
                        modifier = Modifier
                            .fillMaxHeight()
                            .padding(end = 0.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 总注额区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.95f),
                        shape = MaterialTheme.shapes.medium
                    )
                    .padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧：总数显示，不再作为数据管理入口
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(5.dp) // 增加间距
                ) {
                    Text(
                        text = "总数",
                        style = MaterialTheme.typography.titleMedium,
                        fontSize = 18.sp,
                        // fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = totalAmount.toString(),
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }

                // 右侧：清空和粘贴按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FilledTonalButton(
                        onClick = { clearAllStates() },
                        modifier = Modifier.width(60.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp),
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
                        )
                    ) {
                        Text("清空", fontWeight = androidx.compose.ui.text.font.FontWeight.Bold)
                        
                    }

                    FilledTonalButton(
                        onClick = {
                            val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            clipboard.primaryClip?.getItemAt(0)?.text?.let { clipboardText ->
                                inputText = clipboardText.toString()
                                
                                // 检查地区冲突
                                if (checkRegion(inputText) == RegionState.Conflict) {
                                    outputText = "⚠️ 检测到同时存在香港和澳门\n请分开单独输入"
                                    return@let
                                }
                                
                                // 记录粘贴的内容为原始输入
                                if (DataParser.getOriginalInput() == null) {
                                    DataParser.recordOriginalInput(clipboardText.toString())
                                }
                                // 自动解析
                                val (normalResults, specialResults) = DataParser.parseInput(inputText)
                                outputText = buildString {
                                    // 添加普通注额
                                    append(normalResults.joinToString("\n"))

                                    // 添加特殊注额
                                    if (specialResults.isNotEmpty()) {
                                        if (normalResults.isNotEmpty()) {
                                            append("\n\n")  // 添加空行分隔
                                        }
                                        specialResults.forEach { (key, result) ->
                                            append("$key:\n")
                                            append("号码: ${result.numbers}\n")
                                            append("注额: ${result.amount}\n")
                                            append("\n")
                                        }
                                    }
                                }
                                
                                // 添加这两行：更新颜色映射并触发刷新
                                ColorManager.updateColorMapping(outputText)
                                needsColorRefresh = true
                                
                                // 状态会通过 LaunchedEffect 自动保存
                            }
                        },
                        modifier = Modifier.width(60.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp),
                        colors = ButtonDefaults.filledTonalButtonColors(
                            containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
                        )
                    ) {
                        Text("粘贴", fontWeight = androidx.compose.ui.text.font.FontWeight.Bold)
                    }
                }
            }

            Spacer(modifier = Modifier.height(5.dp)) //
            
            // 重新解析和保存到数据库按钮 - 移到总数卡片下方
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(3.dp) 
            ) {
                // 重新解析按钮（与数据管理整合）
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    ),
                    shape = ButtonDefaults.shape,
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                    modifier = Modifier
                        .height(40.dp)
                        .weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxHeight()
                    ) {
                        // 重新解析部分
                        Box(
                            modifier = Modifier
                                .weight(1.7f)
                                .fillMaxHeight()
                                .background(
                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.85f),
                                    shape = RoundedCornerShape(20.dp)
                                )
                                .border(
                                    width = 1.dp,
                                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.2f),
                                    shape = RoundedCornerShape(20.dp)
                                )
                                .clip(RoundedCornerShape(20.dp))  // 添加clip确保水波纹效果跟随形状
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = rememberRipple(
                                        bounded = true,
                                        color = Color.White.copy(alpha = 0.3f)  // 调整水波纹颜色和透明度
                                    )
                                ) {
                                    // 检查地区冲突
                                    if (checkRegion(inputText) == RegionState.Conflict) {
                                        outputText = "⚠️ 检测到同时存在香港和澳门\n请分开单独输入"
                                        return@clickable
                                    }
                                    
                                    // 第一次点击重新解析时记录原始输入
                                    if (DataParser.getOriginalInput() == null) {
                                        DataParser.recordOriginalInput(inputText)
                                    }
                                    
                                    // 解析逻辑
                                    val (normalResults, specialResults) = DataParser.parseInput(inputText)
                                    outputText = buildString {
                                        // 添加普通注额
                                        append(normalResults.joinToString("\n"))
    
                                        // 添加特殊注额
                                        if (specialResults.isNotEmpty()) {
                                            if (normalResults.isNotEmpty()) {
                                                append("\n\n")  // 添加空行分隔
                                            }
                                            specialResults.forEach { (key, result) ->
                                                append("$key:\n")
                                                append("号码: ${result.numbers}\n")
                                                append("注额: ${result.amount}\n")
                                                append("\n")
                                            }
                                        }
                                    }
                                    
                                    // 添加这一行：根据解析结果更新颜色映射
                                    ColorManager.updateColorMapping(outputText)
                                    
                                    // 添加这一行：触发颜色刷新
                                    needsColorRefresh = true
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                "重新解析",
                                color = MaterialTheme.colorScheme.onPrimary,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                            )
                        }
                        
                        Spacer(modifier = Modifier.width(3.dp))
                        
                        // 数据管理部分
                        Box(
                            modifier = Modifier
                                .weight(0.43f)
                                .fillMaxHeight()
                                .clip(ConcaveShape(isLeft = false))
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = rememberRipple(
                                        bounded = true,
                                        color = Color.White.copy(alpha = 0.3f)
                                    ),
                                    onClick = onOpenManagement
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            // 添加圆形背景和阴影 - 增强浮起效果
                            Box(
                                modifier = Modifier
                                    .size(32.dp)
                                    .scale(1.05f)
                                    .shadow(
                                        elevation = 12.dp,
                                        shape = CircleShape,
                                        ambientColor = Color.Black,
                                        spotColor = Color.White
                                    )
                                    .background(
                                        brush = Brush.radialGradient(
                                            colors = listOf(
                                                Color.White.copy(alpha = 0.7f),
                                                Color.White.copy(alpha = 0.2f)
                                            ),
                                            center = Offset(8f, 8f),
                                            radius = 25f
                                        ),
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 1.dp,
                                        color = Color.White.copy(alpha = 0.6f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    Icons.Default.Storage,
                                    contentDescription = "数据管理",
                                    tint = Color.White,
                                    modifier = Modifier.size(25.dp)
                                )
                            }
                        }
                    }
                }

                // 设置和保存数据按钮
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    ),
                    shape = ButtonDefaults.shape,
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                    modifier = Modifier
                        .height(40.dp)
                        .weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxHeight()
                    ) {
                        // 设置按钮部分
                        Box(
                            modifier = Modifier
                                .weight(0.43f)
                                .fillMaxHeight()
                                .clip(ConcaveShape(isLeft = true))
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = rememberRipple(
                                        bounded = true,
                                        color = Color.White.copy(alpha = 0.3f)
                                    ),
                                    onClick = onOpenSettings
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            // 添加圆形背景和阴影 - 增强浮起效果
                            Box(
                                modifier = Modifier
                                    .size(32.dp)
                                    .scale(1.05f)
                                    .shadow(
                                        elevation = 12.dp,
                                        shape = CircleShape,
                                        ambientColor = Color.Black,
                                        spotColor = Color.White
                                    )
                                    .background(
                                        brush = Brush.radialGradient(
                                            colors = listOf(
                                                Color.White.copy(alpha = 0.7f),
                                                Color.White.copy(alpha = 0.2f)
                                            ),
                                            center = Offset(8f, 8f),
                                            radius = 25f
                                        ),
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 1.dp,
                                        color = Color.White.copy(alpha = 0.6f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    Icons.Default.Settings,
                                    contentDescription = "设置",
                                    tint = Color.White,
                                    modifier = Modifier.size(25.dp)
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.width(3.dp))
                        
                        // 保存到数据库部分
                        Box(
                            modifier = Modifier
                                .weight(1.7f)
                                .fillMaxHeight()
                                .background(
                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.85f),
                                    shape = RoundedCornerShape(20.dp)
                                )
                                .border(
                                    width = 1.dp,
                                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.2f),
                                    shape = RoundedCornerShape(20.dp)
                                )
                                .clip(RoundedCornerShape(20.dp))  // 添加clip确保水波纹效果跟随形状
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = rememberRipple(
                                        bounded = true,
                                        color = Color.White.copy(alpha = 0.3f)  // 调整水波纹颜色和透明度
                                    )
                                ) {
                                    // 在开始保存数据前重置批次时间戳
                                    DatabaseUtils.resetBatchTimestamp()

                                    val (normalResults, specialBets) = DataParser.parseInput(inputText)

                                    // 处理普通注额
                                    val results = normalResults
                                        .mapNotNull { line ->
                                            val parts = line.split(":")
                                            if (parts.size == 2) {
                                                parts[0].trim() to (parts[1].trim().toIntOrNull() ?: return@mapNotNull null)
                                            } else null
                                        }
                                        .groupBy({ it.first }, { it.second })
                                        .mapValues { it.value.sum() }

                                    // 检查是否与上次保存的数据相同
                                    val specialBetsMap = specialBets.toMap()
                                    val lastSpecialBetsMap = lastSpecialBets.toMap()
                                    
                                    // 获取上次保存的标签和标识
                                    val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                                    val lastTag = prefs.getString(KEY_LAST_SAVED_TAG, "澳门") ?: "澳门"
                                    val lastIdentifier = prefs.getString("last_identifier", "") ?: ""
                                    
                                    // 先检查地区标签和标识是否相同
                                    if (currentTag == lastTag && selectedIdentifier == lastIdentifier) {
                                        // 只有在地区和标识都相同的情况下，才检查内容是否相同
                                        val isSameContent = results == lastSavedData && specialBetsMap == lastSpecialBetsMap
                                        if (isSameContent) {
                                            Toast.makeText(context, "当前数据与上次保存的数据完全相同（内容、标签、标识均相同），请勿重复保存", Toast.LENGTH_SHORT)
                                                .apply { setGravity(Gravity.CENTER, 0, 0) }
                                                .show()
                                            return@clickable
                                        }
                                    }

                                    // 保存当前标识和标签
                                    prefs.edit()
                                        .putString("last_identifier", selectedIdentifier)
                                        .putString(KEY_CURRENT_TAG, currentTag)
                                        .putString(KEY_LAST_SAVED_TAG, currentTag)
                                        .apply()

                                    // 处理特殊注额
                                    specialBets.forEach { (key, result) ->
                                        DatabaseUtils.insertSpecialBet(
                                            context = context,
                                            numbers = result.numbers,
                                            amount = result.amount,
                                            betType = result.type,
                                            tag = currentTag,
                                            identifier = selectedIdentifier,
                                            originalData = inputText
                                        )
                                    }

                                    // 保存通注额
                                    results.forEach { (number, amount) ->
                                        DatabaseUtils.insertData(
                                            context = context,
                                            number = number.toInt(),
                                            amount = amount,
                                            tag = currentTag,
                                            identifier = selectedIdentifier,
                                            originalData = inputText
                                        )
                                    }

                                    // 更新次保存的数据
                                    lastSavedData = results
                                    lastSpecialBets = specialBets

                                    // 更新总注额
                                    totalAmount = DatabaseUtils.getTotalAmount(context)

                                    // 显示成功消息
                                    val totalCount = results.size + specialBets.size
                                    Toast.makeText(context, "成功保存 $totalCount 条数据", Toast.LENGTH_SHORT)
                                        .apply { setGravity(Gravity.CENTER, 0, 0) }
                                        .show()

                                    if (totalCount > 0) {
                                        SoundUtils.playSuccessSound(context)
                                        VibrationUtils.vibrate(context)
                                        DatabaseUtils.incrementDataCount(context)//增加记录数量
                                        
                                        dataCount = DatabaseUtils.getDataCount(context)
                                    }

                                    // 更新并保存状态
                                    saveAllStates()

                                    // 使用 isUserConfirmed = true 调用解析
                                    val parseResult = DataParser.parse(inputText, true)
                                    if (parseResult.first.isNotEmpty() || parseResult.second.isNotEmpty()) {
                                        // 获取原始输入和当前输入进行对比学习
                                        DataParser.getOriginalInput()?.let { original ->
                                            if (original != inputText) {
                                                // 使用当前输入框的文本作为成功文本，而不是格式化后的文本
                                                ParseLearningEngine.tryLearnNewMappings(original, inputText)
                                            }
                                        }
                                        
                                        // 学习完成后清除原始输入
                                        DataParser.reset()
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                "保存数据",
                                color = MaterialTheme.colorScheme.onPrimary,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(5.dp))

            // 输出结果卡片
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.6f)
                    .animatedBorder(
                        enabled = AnimationSettings.animationEnabledOutput,
                        borderWidth = 2.dp,
                        shape = MaterialTheme.shapes.medium,
                        brush = SolidColor(MaterialTheme.colorScheme.primary.copy(alpha = AnimationSettings.animationAlphaOutput)),
                        durationMillis = AnimationSettings.durationMillisOutput,
                        snakeLengthPercent = AnimationSettings.snakeLengthPercentOutput,
                        clockwise = AnimationSettings.animationDirectionOutput,
                        animationType = AnimationSettings.animationTypeOutput
                    ),
                shape = MaterialTheme.shapes.medium,
                elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Column(
                    modifier = Modifier
                        .padding(16.dp) // 添加内边距
                        .fillMaxSize()
                ) {
                    // 生成公式和概览
                    val formula = buildCalculationFormula(outputText)
                    // 提取简短概览 - 如果超过一定长度就截断并加上省略号
                    val shortFormula = if (formula.length > 40) {
                        val parts = formula.split("=")
                        if (parts.size == 2) {
                            // 保留总和部分，但截断前面的计算式
                            val calculation = parts[0]
                            val terms = calculation.split("+")
                            if (terms.size <= 3) {
                                formula // 如果项很少，直接显示完整公式
                            } else {
                                // 只显示前三项，然后加上省略号和总和
                                "${terms.take(3).joinToString("+")}...=${parts[1]}"
                            }
                        } else {
                            formula
                        }
                    } else {
                        formula
                    }
                    
                    // 折叠/展开状态
                    var expanded by remember { mutableStateOf(false) }
                    
                    // 旋转动画用于展开/折叠箭头图标
                    val rotationState by animateFloatAsState(
                        targetValue = if (expanded) 180f else 0f,
                        label = "rotation"
                    )
                    
                    Column(modifier = Modifier.fillMaxWidth()) {
                        // 标题栏 - 带有折叠/展开按钮
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))  // 添加圆角裁剪
                                .clickable { expanded = !expanded },
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 显示标题和简短公式
                            Text(
                                text = "解析结果: $shortFormula",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.clickable {
                                    localClipboardManager.setText(AnnotatedString(formula))
                                    Toast.makeText(context, "公式已复制", Toast.LENGTH_SHORT).show()
                                }
                            )
                            
                            // 只有当公式较长需要折叠时才显示箭头
                            if (formula.length > 40) {
                                Icon(
                                    imageVector = Icons.Default.ArrowDropDown,
                                    contentDescription = if (expanded) "折叠" else "展开",
                                    modifier = Modifier.rotate(rotationState),
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                        
                        // 可折叠的详细公式
                        AnimatedVisibility(
                            visible = expanded && formula.length > 40,
                            enter = expandVertically(),
                            exit = shrinkVertically()
                        ) {
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
                                ),
                                shape = MaterialTheme.shapes.small
                            ) {
                                Text(
                                    text = formula,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                    modifier = Modifier
                                        .padding(8.dp)
                                        .clickable {
                                            localClipboardManager.setText(AnnotatedString(formula))
                                            Toast
                                                .makeText(context, "公式已复制", Toast.LENGTH_SHORT)
                                                .show()
                                        }
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                MaterialTheme.colorScheme.surface,
                                shape = MaterialTheme.shapes.small
                            )
                            .verticalScroll(rememberScrollState())
                    ) {
                        Text(
                            text = formatOutputWithColors(outputText),
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(8.dp)
                        )
                    }
                }
            }
        }

        // 对话框
        if (showAddIdentifierDialog) {
            AddIdentifierDialog(
                identifiers = identifiers,
                onDismiss = { showAddIdentifierDialog = false },
                onConfirm = { newIdentifier, rebate ->
                    val newIdentifiers = identifiers + newIdentifier
                    // 写入返水值
                    val prefs = context.getSharedPreferences("identifier_prefs", Context.MODE_PRIVATE)
                    prefs.edit().putFloat("identifier_rebates_$newIdentifier", rebate).apply()
                    onUpdateIdentifiers(newIdentifiers)
                    onUpdateSelectedIdentifier(newIdentifier)
                    showAddIdentifierDialog = false
                }
            )
        }

        if (identifierToDelete.isNotEmpty()) {
            DeleteIdentifierDialog(
                identifierToDelete = identifierToDelete,
                onDismiss = { identifierToDelete = "" },
                onConfirm = {
                    // 先删除返水值
                    val prefs = context.getSharedPreferences("identifier_prefs", Context.MODE_PRIVATE)
                    prefs.edit().remove("identifier_rebates_$identifierToDelete").apply()
                    // 再删除标识
                    val newIdentifiers = identifiers - identifierToDelete
                    onUpdateIdentifiers(newIdentifiers)
                    if (selectedIdentifier == identifierToDelete) {
                        onUpdateSelectedIdentifier("")
                    }
                }
            )
        }

        // 添加标识统计弹窗
        if (showIdentifierStatsDialog) {
            AlertDialog(
                onDismissRequest = { showIdentifierStatsDialog = false },
                title = { Text("标签统计信息") },
                text = {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .verticalScroll(rememberScrollState()),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = "所有标签数据统计:",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(bottom = 3.dp)
                        )
                        
                        // // 添加表头
                        // Row(
                        //     modifier = Modifier.fillMaxWidth(),
                        //     horizontalArrangement = Arrangement.SpaceBetween
                        // ) {
                        //     Text(
                        //         text = "标签",
                        //         style = MaterialTheme.typography.bodyMedium.copy(
                        //             fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                        //         ),
                        //         modifier = Modifier.weight(1f),
                        //         color = MaterialTheme.colorScheme.onSurfaceVariant
                        //     )
                        //     Text(
                        //         text = "条数",
                        //         style = MaterialTheme.typography.bodyMedium.copy(
                        //             fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                        //         ),
                        //         modifier = Modifier.weight(1f),
                        //         color = MaterialTheme.colorScheme.onSurfaceVariant,
                        //         textAlign = androidx.compose.ui.text.style.TextAlign.Center
                        //     )
                        //     Text(
                        //         text = "金额",
                        //         style = MaterialTheme.typography.bodyMedium.copy(
                        //             fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                        //         ),
                        //         modifier = Modifier.weight(1f),
                        //         color = MaterialTheme.colorScheme.onSurfaceVariant,
                        //         textAlign = androidx.compose.ui.text.style.TextAlign.End
                        //     )
                        // }
                        
                        // Divider(
                        //     modifier = Modifier.padding(vertical = 3.dp),
                        //     color = MaterialTheme.colorScheme.outline
                        // )
                        
                        // 显示标识统计信息
                        identifierStats.forEach { (tag, count, total) ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = tag,
                                    style = MaterialTheme.typography.bodyLarge,
                                    modifier = Modifier.weight(1f),
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = "${count}条",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.weight(1f),
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                                )
                                Text(
                                    text = "$total",
                                    style = MaterialTheme.typography.bodyLarge,
                                    modifier = Modifier.weight(1f),
                                    color = MaterialTheme.colorScheme.primary,
                                    textAlign = androidx.compose.ui.text.style.TextAlign.End
                                )
                            }
                            Divider(
                                modifier = Modifier.padding(vertical = 3.dp),
                                color = MaterialTheme.colorScheme.outlineVariant
                            )
                        }
                        
                        // 计算并显示总金额
                        val totalAmount = identifierStats.sumOf { it.third }
                        val totalCount = identifierStats.sumOf { it.second }
                        
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "总计:",
                                style = MaterialTheme.typography.titleMedium,
                                modifier = Modifier.weight(1f),
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            Text(
                                text = "${totalCount}条",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.weight(1f),
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                textAlign = androidx.compose.ui.text.style.TextAlign.Center
                            )
                            Text(
                                text = "$totalAmount",
                                style = MaterialTheme.typography.titleMedium,
                                modifier = Modifier.weight(1f),
                                color = MaterialTheme.colorScheme.primary,
                                textAlign = androidx.compose.ui.text.style.TextAlign.End
                            )
                        }
                    }
                },
                confirmButton = {
                    TextButton(onClick = { showIdentifierStatsDialog = false }) {
                        Text("关闭")
                    }
                }
            )
        }
    }
}

// 自定义动画边框 Modifier
fun Modifier.animatedBorder(
    enabled: Boolean,
    borderWidth: Dp,
    shape: Shape,
    brush: Brush,
    durationMillis: Int,
    snakeLengthPercent: Float,
    clockwise: Boolean = true,
    animationType: AnimationSettings.AnimationType = AnimationSettings.AnimationType.FLOWING_LIGHT
): Modifier = composed {
    val progress = remember { Animatable(if (clockwise) 1f else 0f) }
    val breathingAlpha = remember { Animatable(0.3f) }

    LaunchedEffect(enabled, durationMillis, clockwise, animationType) {
        if (enabled) {
            when (animationType) {
                AnimationSettings.AnimationType.FLOWING_LIGHT -> {
                    progress.animateTo(
                        targetValue = if (clockwise) 0f else 1f,
                        animationSpec = infiniteRepeatable(
                            animation = tween(durationMillis, easing = LinearEasing),
                            repeatMode = RepeatMode.Restart
                        )
                    )
                }
                AnimationSettings.AnimationType.BREATHING_LIGHT -> {
                    breathingAlpha.animateTo(
                        targetValue = 1f,
                        animationSpec = infiniteRepeatable(
                            animation = tween(durationMillis / 2, easing = LinearEasing),
                            repeatMode = RepeatMode.Reverse
                        )
                    )
                }
            }
        } else {
            progress.stop()
            breathingAlpha.stop()
        }
    }

    val pathMeasure = remember { PathMeasure() }
    val path = remember { Path() }
    val staticBorderColor = MaterialTheme.colorScheme.primaryContainer

    this.drawWithContent {
        drawContent() // 先绘制卡片内容

        val outline = shape.createOutline(size, layoutDirection, Density(density))
        path.reset()
        when (outline) {
            is Outline.Generic -> path.addPath(outline.path)
            is Outline.Rectangle -> path.addRect(outline.rect)
            is Outline.Rounded -> path.addRoundRect(outline.roundRect)
        }
        
        // 总是绘制静态背景边框
        drawPath(
            path = path,
            color = staticBorderColor,
            style = Stroke(width = borderWidth.toPx())
        )
        
        // 如果动画启用，根据动画类型绘制不同效果
        if (enabled) {
            when (animationType) {
                AnimationSettings.AnimationType.FLOWING_LIGHT -> {
                    // 流光动画：绘制移动的"贪吃蛇"
                    pathMeasure.setPath(path, false)
                    val pathLength = pathMeasure.length

                    val snakeLength = pathLength * snakeLengthPercent
                    val start = progress.value * pathLength
                    val end = (start + snakeLength).coerceAtMost(pathLength)

                    val snakePath = Path()
                    pathMeasure.getSegment(start, end, snakePath, true)

                    if (start + snakeLength > pathLength) {
                        val wrapEnd = (start + snakeLength) % pathLength
                        pathMeasure.getSegment(0f, wrapEnd, snakePath, true)
                    }

                    drawPath(
                        path = snakePath,
                        brush = brush,
                        style = Stroke(
                            width = borderWidth.toPx(),
                            cap = StrokeCap.Round // 圆头效果
                        )
                    )
                }
                AnimationSettings.AnimationType.BREATHING_LIGHT -> {
                    // 呼吸灯动画：整个边框透明度变化
                    val breathingBrush = when (brush) {
                        is SolidColor -> SolidColor(brush.value.copy(alpha = breathingAlpha.value))
                        else -> brush
                    }

                    drawPath(
                        path = path,
                        brush = breathingBrush,
                        style = Stroke(
                            width = borderWidth.toPx() * (0.8f + 0.4f * breathingAlpha.value), // 边框宽度也会变化
                            cap = StrokeCap.Round
                        )
                    )
                }
            }
        }
    }
}

// 修改颜色列表，使用更鲜艳的颜色
private val colors = listOf(
    Color(0xFFFF0000), // 鲜红
    Color(0xFF00FF00), // 鲜绿
    Color(0xFF0000FF), // 鲜蓝
    Color(0xFFFF00FF), // 洋红
    Color(0xFF00BFFF), // 深天蓝
    Color(0xFF00FFFF), // 青色
    Color(0xFFFF4500), // 橙红
    Color(0xFF8B00FF), // 紫罗兰
    Color(0xFFFF1493), // 深粉
    Color(0xFF00FF7F), // 春绿
    Color(0xFFFF8C00), // 深橙
    Color(0xFFADFF2F), // 黄绿
    Color(0xFFFF69B4), // 热粉红
    Color(0xFF32CD32)  // 酸橙绿
).map { it.toArgb() }

// 修改 ColorManager 对象，添加从解析结果获取颜色映射的功能
object ColorManager {
    private var currentOutputText = ""
    private val amountColorMap = mutableMapOf<String, Int>()  // 金额到颜色的映射
    private val numberColorMap = mutableMapOf<String, Int>()  // 号码到颜色的映射
    private val zodiacColorMap = mutableMapOf<String, Int>()  // 生肖到颜色的映射
    private var currentColorIndex = 0

    fun updateColorMapping(text: String): Map<String, Int> {
        if (text == currentOutputText) return amountColorMap

        // 重置所有映射
        amountColorMap.clear()
        numberColorMap.clear()
        zodiacColorMap.clear()
        currentColorIndex = 0

        // 收集所有出现的号码
        val appearedNumbers = mutableSetOf<Int>()
        val numberFormats = mutableMapOf<Int, String>() // 保存每个数字的原始格式
        
        // 只处理输出结果中的号码和金额
        val numberAmountPairs = text.split("\n")
            .filter { it.contains(":") }
            .mapNotNull { line ->
                val parts = line.split(":") // 按冒号分割
                if (parts.size == 2) {
                    val numberStr = parts[0].trim()
                    numberStr.toIntOrNull()?.let { num ->
                        appearedNumbers.add(num)
                        numberFormats[num] = numberStr // 保存原始格式
                        numberStr to parts[1].trim()
                    }
                } else null
            }

        // 按金额分组并分配颜色
        numberAmountPairs.groupBy { it.second }.forEach { (amount, pairs) ->
            val color = colors[currentColorIndex % colors.size]
            currentColorIndex++

            pairs.forEach { (number, _) ->
                numberColorMap[number] = color
            }
            amountColorMap[amount] = color
        }

        // 检查每个生肖的所有号码是否都出现了
        ZodiacUtils.getZodiacMappings().forEach { (zodiac, zodiacNumbers) ->
            if (zodiacNumbers.all { it in appearedNumbers }) {
                // 使用第一个号码的原始格式来获取颜色
                val firstNumber = zodiacNumbers.first()
                val originalFormat = numberFormats[firstNumber] ?: firstNumber.toString()
                numberColorMap[originalFormat]?.let { color ->
                    zodiacColorMap[zodiac] = color
                }
            }
        }

        currentOutputText = text
        return amountColorMap
    }

    fun getColorForInput(text: String): Int {
        return when {
            text.all { it.isDigit() } -> {
                if (text.startsWith("各")) {
                    val amount = text.substring(1)
                    amountColorMap[amount] ?: numberColorMap[amount]
                } else {
                    numberColorMap[text]
                }
            }
            text.length == 1 && text[0] in "鼠牛虎兔龙蛇马羊猴鸡狗猪" -> zodiacColorMap[text]
            else -> null
        } ?: Color.Black.toArgb()
    }

    fun getColorForOutput(text: String): Int {
        return when {
            text.all { it.isDigit() } -> numberColorMap[text] ?: amountColorMap[text]
            text.length == 1 && text[0] in "鼠牛虎兔龙蛇马羊猴鸡狗猪" -> zodiacColorMap[text]
            else -> null
        } ?: Color.Black.toArgb()
    }

}

// 修改 ColoredNumberTransformation 类
private class ColoredNumberTransformation : VisualTransformation {
    override fun filter(text: AnnotatedString): TransformedText {
        if (text.isEmpty()) return TransformedText(text, OffsetMapping.Identity)

        return TransformedText(
            buildAnnotatedString {
                val content = text.text
                var lastPos = 0
                
                // 匹配数字、生肖和"各"字后面的数字
                val pattern = "\\d+|[鼠牛虎兔龙蛇马羊猴鸡狗猪]|各\\d+".toRegex()
                pattern.findAll(content).forEach { matchResult ->
                    val match = matchResult.value
                    val startIndex = matchResult.range.first
                    val endIndex = matchResult.range.last + 1
                    
                    // 添加非匹配部分
                    append(content.substring(lastPos, startIndex))
                    
                    // 处理"各"字的特殊情况
                    val colorText = if (match.startsWith("各")) {
                        match.substring(1)
                    } else {
                        match
                    }
                    
                    // 为匹配的文本添加颜色
                    withStyle(SpanStyle(color = Color(ColorManager.getColorForInput(colorText)))) {
                        append(match)
                    }
                    
                    lastPos = endIndex
                }
                
                // 添加剩余文本
                if (lastPos < content.length) {
                    append(content.substring(lastPos))
                }
            },
            OffsetMapping.Identity
        )
    }
}

// 修改格式化输出文本的函数
private fun formatOutputWithColors(text: String): AnnotatedString {
    ColorManager.updateColorMapping(text)
    
    return buildAnnotatedString {
        text.split("\n").forEachIndexed { index, line ->
            if (line.contains(":")) {
                val parts = line.split(":")
                if (parts.size == 2) {
                    val number = parts[0].trim()
                    val amount = parts[1].trim()

                    // 为号码添加颜色
                    withStyle(SpanStyle(color = Color(ColorManager.getColorForOutput(number)))) {
                        append(number)
                        append(":")
                    }
                    // 金额添加颜色
                    withStyle(SpanStyle(color = Color(ColorManager.getColorForOutput(amount)))) {
                        append(amount)
                    }
                } else {
                    append(line)
                }
            } else {
                append(line)
            }
            
            if (index < text.split("\n").size - 1) append("\n")
        }
    }
}

// 计算统计信息
private fun calculateStats(text: String): Pair<Int, Int> {
    if (text.isBlank()) return Pair(0, 0)
    
    var count = 0
    var totalAmount = 0
    

    // 将文本行分割
    text.split("\n").forEach { line ->
        
        // 处理普通注额格式 (如 "46:85")
        if (line.contains(":") && !line.contains("注额:") && !line.contains("号码:")) {
            val parts = line.split(":")
            if (parts.size == 2) {
                // 只有当第一部分是数字时才计数
                if (parts[0].trim().toIntOrNull() != null) {
                    count++  // 只统计数字格式的特码
                    val amount = parts[1].trim().toIntOrNull() ?: 0
                    totalAmount += amount
                }
            }
        }
        // 处理特殊组合注额
        else if (line.contains("注额:")) {
            val amount = line.substringAfter("注额:").trim().toIntOrNull() ?: 0
            totalAmount += amount  // 只加金额，不增加码数
        }
    }
    return Pair(count, totalAmount)
}

// 添加用于 JSON 换的辅助函数
private fun mapToJson(map: Map<String, Int>?): String {
    if (map == null) return ""
    return JSONObject().apply {
        map.forEach { (key, value) ->
            put(key, value)
        }
    }.toString()
}

private fun jsonToMap(json: String): Map<String, Int>? {
    if (json.isEmpty()) return null
    return try {
        val jsonObject = JSONObject(json)
        val map = mutableMapOf<String, Int>()
        jsonObject.keys().forEach { key ->
            map[key] = jsonObject.getInt(key)
        }
        map
    } catch (e: Exception) {
        null
    }
}

private fun specialBetsToJson(specialBets: List<Pair<String, SpecialBetResult>>): String {
    return JSONObject().apply {
        specialBets.forEach { (key, result) ->
            put(key, JSONObject().apply {
                // 将数字列表转换为 JSONArray
                put("numbers", JSONArray(result.numbers))
                put("amount", result.amount)
                put("type", result.type)
            })
        }
    }.toString()
}

private fun jsonToSpecialBets(json: String): List<Pair<String, SpecialBetResult>> {
    return try {
        val jsonObject = JSONObject(json)
        val list = mutableListOf<Pair<String, SpecialBetResult>>()
        jsonObject.keys().forEach { key ->
            val resultObj = jsonObject.getJSONObject(key)
            // 从 JSONArray 换回数字列表
            val numbersArray = resultObj.getJSONArray("numbers")
            val numbersList = mutableListOf<Int>()
            for (i in 0 until numbersArray.length()) {
                numbersList.add(numbersArray.getInt(i))
            }
            list.add(Pair(key, SpecialBetResult(
                numbers = numbersList,
                amount = resultObj.getInt("amount"),
                type = resultObj.getString("type")
            )))
        }
        list
    } catch (e: Exception) {
        emptyList()
    }
}

// 添加这个函数到 DataInputScreen 顶层
@Composable
private fun ColoredTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    readOnly: Boolean = false,
    outputText: String = "",
    needsRefresh: Boolean = false,
    onRefreshComplete: () -> Unit = {}
) {
    // 使用自定义实现取代 BasicTextField
    val colors = MaterialTheme.colorScheme
    
    // 添加强制刷新状态
    var refreshTrigger by remember { mutableStateOf(0) }
    
    // 强制更新颜色映射
    LaunchedEffect(value, outputText, needsRefresh) {
        if (outputText.isNotEmpty() || needsRefresh) {
            ColorManager.updateColorMapping(outputText)
            refreshTrigger++
            if (needsRefresh) {
                onRefreshComplete()
            }
        }
    }
    
    Box(
        modifier = modifier
            .background(colors.surface)
            .padding(8.dp)
    ) {
        Text(
            text = buildAnnotatedString {
                var lastPos = 0
                
                // 匹配数字、生肖和"各"字后面的数字
                val pattern = "\\d+|[鼠牛虎兔龙蛇马羊猴鸡狗猪]|各\\d+".toRegex()
                pattern.findAll(value).forEach { matchResult ->
                    val match = matchResult.value
                    val startIndex = matchResult.range.first
                    val endIndex = matchResult.range.last + 1
                    
                    // 添加非匹配部分
                    append(value.substring(lastPos, startIndex))
                    
                    // 处理"各"字的特殊情况
                    val colorText = if (match.startsWith("各")) {
                        match.substring(1)
                    } else {
                        match
                    }
                    
                    // 为匹配的文本添加颜色
                    withStyle(SpanStyle(color = Color(ColorManager.getColorForInput(colorText)))) {
                        append(match)
                    }
                    
                    lastPos = endIndex
                }
                
                // 添加剩余文本
                if (lastPos < value.length) {
                    append(value.substring(lastPos))
                }
            },
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.fillMaxWidth()
        )
        
        // 如果不是只读，添加透明的文本输入框
        if (!readOnly) {
            BasicTextField(
                value = value,
                onValueChange = { 
                    onValueChange(it)
                },
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Transparent),
                textStyle = MaterialTheme.typography.bodyMedium.copy(
                    color = Color.Transparent
                ),
                cursorBrush = SolidColor(colors.primary)
            )
        }
    }
}

// 添加生成标识统计信息的函数
private fun generateIdentifierStats(context: Context): List<Triple<String, Int, Int>> {
    val originalDataList = DatabaseUtils.getOriginalDataList(context)
    return originalDataList
        .groupBy { it.identifier.ifEmpty { "无标识" } }
        .map { (tag, dataList) ->
            val total = dataList.sumOf { data ->
                val normalAmount = data.numberDetails.sumOf { it.amount }
                val specialAmount = data.specialAmount
                normalAmount + specialAmount
            }
            Triple(tag, dataList.size, total)  // 三元组：标识、条数、总金额
        }
        .sortedByDescending { it.third }  // 按金额降序排序
}

// 添加自定义形状类
private class ConcaveShape(private val isLeft: Boolean) : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val path = Path().apply {
            if (isLeft) {
                // 左侧凹陷形状
                moveTo(0f, 0f)
                lineTo(size.width - size.height/2, 0f)
                arcTo(
                    rect = Rect(
                        offset = Offset(size.width - size.height, 0f),
                        size = Size(size.height, size.height)
                    ),
                    startAngleDegrees = -90f,
                    sweepAngleDegrees = 180f,
                    forceMoveTo = false
                )
                lineTo(0f, size.height)
                close()
            } else {
                // 右侧凹陷形状
                moveTo(size.height/2, 0f)
                lineTo(size.width, 0f)
                lineTo(size.width, size.height)
                lineTo(size.height/2, size.height)
                arcTo(
                    rect = Rect(
                        offset = Offset(0f, 0f),
                        size = Size(size.height, size.height)
                    ),
                    startAngleDegrees = 90f,
                    sweepAngleDegrees = 180f,
                    forceMoveTo = false
                )
                close()
            }
        }
        return Outline.Generic(path)
    }
}

// 添加生成计算公式的函数
private fun buildCalculationFormula(outputText: String): String {
    // println("号码输出: $outputText")
    if (outputText.isBlank()) return ""
    
    // 用于识别不同数据组的标记
    val groupedAmounts = mutableListOf<Pair<Int, Int>>()
    var currentGroup = ""
    var currentAmount = 0
    var currentCount = 0
    
    // 解析输出文本中的金额部分，保持原始分组结构
    val lines = outputText.split("\n")
    
    for (i in lines.indices) {
        val line = lines[i]
        
        // 检查是否有空行，空行表示分组的边界
        if (line.isBlank()) {
            // 如果当前有一个活跃的分组，将其添加到结果中
            if (currentCount > 0) {
                groupedAmounts.add(Pair(currentAmount, currentCount))
                currentCount = 0
            }
            // 重置分组标记
            currentGroup = ""
            continue
        }
        
        if (line.contains(":") && !line.contains("注额:") && !line.contains("号码:")) {
            val parts = line.split(":")
            if (parts.size == 2) {
                val amount = parts[1].trim().toIntOrNull() ?: continue
                
                // 判断是否需要开始一个新的分组
                if (currentGroup.isEmpty()) {
                    // 这是一个新分组的开始
                    currentGroup = "normal"
                    currentAmount = amount
                    currentCount = 1
                } else if (amount == currentAmount) {
                    // 相同金额且在同一分组内，增加计数
                    currentCount++
                } else {
                    // 金额变化，保存当前分组并开始新分组
                    groupedAmounts.add(Pair(currentAmount, currentCount))
                    currentAmount = amount
                    currentCount = 1
                }
            }
        } else if (line.contains("注额:")) {
            // 如果当前有一个活跃的分组，将其添加到结果中
            if (currentCount > 0) {
                groupedAmounts.add(Pair(currentAmount, currentCount))
                currentCount = 0
            }
            
            // 特殊注额单独处理为一个分组
            val amount = line.substringAfter("注额:").trim().toIntOrNull() ?: continue
            currentGroup = "special"
            groupedAmounts.add(Pair(amount, 1))
            currentCount = 0  // 重置计数
            currentGroup = "" // 重置分组标记
        }
        
        // 检查下一行是否是分组的边界
        val isLastLine = i == lines.size - 1
        val isNextLineEmpty = !isLastLine && (i + 1 < lines.size) && lines[i + 1].isBlank()
        val isNextLineSpecial = !isLastLine && (i + 1 < lines.size) && 
            (lines[i + 1].contains("号码:") || lines[i + 1].contains("注额:"))
        
        if (isLastLine || isNextLineEmpty || isNextLineSpecial) {
            // 如果当前有一个活跃的分组，将其添加到结果中
            if (currentCount > 0) {
                groupedAmounts.add(Pair(currentAmount, currentCount))
                currentCount = 0
            }
            // 重置分组标记
            currentGroup = ""
        }
    }
    
    // 如果最后还有未添加的分组
    if (currentCount > 0) {
        groupedAmounts.add(Pair(currentAmount, currentCount))
    }
    
    // 如果没有分组，直接返回空字符串
    if (groupedAmounts.isEmpty()) return ""
    
    // 检查所有金额是否相同
    val allAmounts = groupedAmounts.map { it.first }
    val isAllSame = allAmounts.all { it == allAmounts.first() }
    
    // 计算总和
    val total = groupedAmounts.sumOf { (amount, count) -> amount * count }
    
    // 如果所有金额都相同，使用乘法公式
    if (isAllSame && groupedAmounts.isNotEmpty()) {
        val amount = groupedAmounts.first().first
        val totalCount = groupedAmounts.sumOf { it.second }
        return "${amount}x${totalCount}=${total}"
    }

    // 构建公式字符串
    val formula = groupedAmounts.joinToString("+") { (amount, count) ->
        if (count == 1) {
            amount.toString()
        } else {
            "${count}x${amount}"
        }
    }

    return "$formula=$total"
}

// 添加地区检查函数
private fun checkRegion(text: String): RegionState {
    // 使用更完整的检测逻辑
    val hasHongKong = text.contains("香港") || text.contains("港") || text.contains("香")
    val hasMacau = text.contains("澳门") || text.contains("澳")
    
    return when {
        hasHongKong && hasMacau -> RegionState.Conflict
        hasHongKong -> RegionState.HongKong
        hasMacau -> RegionState.Macau
        else -> RegionState.None
    }
}
