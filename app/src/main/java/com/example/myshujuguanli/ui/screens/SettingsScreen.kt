package com.example.myshujuguanli.ui.screens

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.FileDownload
import androidx.compose.material.icons.filled.FileUpload
import androidx.compose.material.icons.filled.Help
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.example.myshujuguanli.MainActivity
import com.example.myshujuguanli.settings.AnimationSettings
import com.example.myshujuguanli.utils.BettingOdds
import com.example.myshujuguanli.utils.ParseLearningEngine
import com.example.myshujuguanli.utils.SecurityUtils
import com.example.myshujuguanli.utils.ZodiacUtils
import com.example.myshujuguanli.theme.ThemeManager
import com.example.myshujuguanli.viewmodels.ThemeViewModel
import kotlinx.coroutines.Job
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    isTabletMode: Boolean = false,
    onBack: () -> Unit,
    onNavigateToOddsSettings: () -> Unit,
    onNavigateToUserGuide: () -> Unit,
    themeViewModel: ThemeViewModel,  // 直接接收 ViewModel 实例
    isDarkTheme: Boolean
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current
    
    // 获取本地缓存的状态信息
    val prefs = remember { context.getSharedPreferences(SecurityUtils.PREFS_NAME, Context.MODE_PRIVATE) }
    val storedServerTime = remember { prefs.getLong(SecurityUtils.KEY_SERVER_TIME, System.currentTimeMillis()) }
    val storedExpiryTime = remember { prefs.getLong(SecurityUtils.KEY_EXPIRY_TIME, 0L) }
    val deviceStatus = remember { prefs.getString(SecurityUtils.KEY_DEVICE_STATUS, null) }
    val isActivated = remember { prefs.getBoolean(SecurityUtils.KEY_IS_ACTIVATED, false) }

    // 在组件初始化时加载保存的赔率设置
    LaunchedEffect(Unit) {
        BettingOdds.loadSavedSettings(context)
    }

    // 计算状态和显示文本
    val (status, displayText) = remember(storedServerTime, storedExpiryTime, deviceStatus, isActivated) {
        when {
            isActivated -> Pair(
                SecurityUtils.AppStatus.ACTIVATED,
                "永久版"
            )
            deviceStatus == SecurityUtils.AppStatus.TRIAL.name -> Pair(
                SecurityUtils.AppStatus.TRIAL,
                "试用期"
            )
            storedExpiryTime > storedServerTime -> {
                val remainingTime = storedExpiryTime - storedServerTime
                val days = remainingTime / (24 * 60 * 60 * 1000)
                val hours = (remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
                // 格式化显示，大于1万天时用万为单位
                val displayDays = if (days > 10000) {
                    String.format("%.1f万天", days / 10000.0)
                } else {
                    "${days}天"
                }
                Pair(
                    SecurityUtils.AppStatus.NORMAL,
                    "$displayDays${hours}小时"
                )
            }
            else -> Pair(
                SecurityUtils.AppStatus.EXPIRED,
                "已过期"
            )
        }
    }
    
    // 自动纠正开关状态
    var autoCorrectEnabled by remember { 
        mutableStateOf(
            context.getSharedPreferences("settings", Context.MODE_PRIVATE)
                .getBoolean("auto_correct_enabled", true)
        ) 
    }
    var isAutoCorrectExpanded by remember { mutableStateOf(false) }

    // 在组件初始化时加载保存的映射
    LaunchedEffect(Unit) {
        ZodiacUtils.loadSavedMappings(context)
    }
    
    var zodiacMappings by remember { mutableStateOf(ZodiacUtils.getZodiacMappings()) }
    var showYearDialog by remember { mutableStateOf(false) }
    var currentYear by remember { mutableStateOf(ZodiacUtils.getCurrentBaseYear(context)) }
    var isZodiacExpanded by remember { mutableStateOf(false) }
    var isOddsExpanded by remember { mutableStateOf(false) }

    // UI 状态变量
    var showAddRuleDialog by remember { mutableStateOf(false) }
    var showRuleOptionsDialog by remember { mutableStateOf(false) }
    var showEditRuleDialog by remember { mutableStateOf(false) }
    var selectedRule by remember { mutableStateOf<Pair<String, String>?>(null) }
    var showImportDialog by remember { mutableStateOf(false) }
    var showExportDialog by remember { mutableStateOf(false) }

    // 添加主题色选择状态
    var showThemeColorDialog by remember { mutableStateOf(false) }
    var isThemeColorExpanded by remember { mutableStateOf(false) }
    
    // 从ThemeManager获取主题选项
    val themeColorOptions = ThemeManager.themes

    // 获取当前主题信息
    val themeInfo = themeViewModel.themeInfo.value

    // 返水设置相关状态
    var isRebateExpanded by remember { mutableStateOf(false) }
    var rebateEnabled by remember { 
        mutableStateOf(
            context.getSharedPreferences("settings", Context.MODE_PRIVATE)
                .getBoolean("rebate_enabled", false)
        ) 
    }
    var rebateValue by remember { 
        mutableStateOf(
            context.getSharedPreferences("settings", Context.MODE_PRIVATE)
                .getFloat("rebate_value", 0.0f).toString()
        ) 
    }

    // 自动粘贴设置相关状态
    var isAutoPasteExpanded by remember { mutableStateOf(false) }

    // 添加搜索相关状态
    var showSearch by remember { mutableStateOf(false) }
    var searchQuery by remember { mutableStateOf("") }

    // 在 SettingsScreen 的开始处修改 LaunchedEffect
    LaunchedEffect(Unit) {
        // 加载动画设置
        AnimationSettings.load(context)
        // 只加载主题信息，不重新生成随机主题
        themeViewModel.updateThemeInfoOnly(context)
    }

    // 在文件顶部添加状态
    var showDeviceId by remember { mutableStateOf(false) }
    var deviceIdTimer by remember { mutableStateOf<Job?>(null) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Start
                    ) {
                        Text("设置界面") 
                        // 帮助图标紧跟标题
                        IconButton(
                            onClick = onNavigateToUserGuide,
                            modifier = Modifier.size(40.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Help,
                                contentDescription = "使用说明",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 重新设计状态卡片，改为浅色背景圆角卡片
                    Card(
                        modifier = Modifier
                            .padding(end = 8.dp)
                            .padding(vertical = 4.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.0f)
                        ),
                        shape = MaterialTheme.shapes.medium
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(horizontal = 12.dp, vertical = 8.dp)
                                .clip(RoundedCornerShape(4.dp))
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = rememberRipple(bounded = true)
                                ) {
                                    // 获取设备ID
                                    val deviceId = SecurityUtils.getDeviceId(context)
                                    // 复制到剪贴板
                                    val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                    val clip = ClipData.newPlainText("设备ID", deviceId)
                                    clipboardManager.setPrimaryClip(clip)
                                    // 显示提示
                                    Toast.makeText(context, "设备ID已复制：$deviceId", Toast.LENGTH_LONG).show()
                                },
                            horizontalAlignment = Alignment.End
                        ) {
                            // 显示剩余使用时间
                            Text(
                                text = displayText,
                                style = MaterialTheme.typography.labelLarge,
                                color = when (status) {
                                    SecurityUtils.AppStatus.EXPIRED -> MaterialTheme.colorScheme.error
                                    SecurityUtils.AppStatus.ACTIVATED -> MaterialTheme.colorScheme.primary
                                    SecurityUtils.AppStatus.TRIAL -> MaterialTheme.colorScheme.secondary
                                    SecurityUtils.AppStatus.NORMAL -> MaterialTheme.colorScheme.primary
                                    else -> MaterialTheme.colorScheme.onSurface
                                }
                            )
                            // 如果是订阅状态,显示到期时间
                            if (status == SecurityUtils.AppStatus.NORMAL) {
                                Text(
                                    text = "${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(storedExpiryTime))} 到期",
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontWeight = FontWeight.Bold
                                    ),
                                    color = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.padding(top = 2.dp)
                                )
                            }
                        }
                    }
                }
            )
        },
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)  // 使用 Scaffold 提供的 padding
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)  // 内容的水平边距
            ) {
                // 自动纠正设置卡片
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp
                    ),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // 标题栏和开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(55.dp)
                                .clickable { isAutoCorrectExpanded = !isAutoCorrectExpanded }
                                .padding(horizontal = 16.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Start,
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    text = "自动纠正输入",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Icon(
                                    imageVector = if (isAutoCorrectExpanded) 
                                        Icons.Default.KeyboardArrowUp 
                                    else 
                                        Icons.Default.KeyboardArrowDown,
                                    contentDescription = if (isAutoCorrectExpanded) "收起" else "展开"
                                )
                            }
                            Switch(
                                checked = autoCorrectEnabled,
                                onCheckedChange = { enabled ->
                                    autoCorrectEnabled = enabled
                                    context.getSharedPreferences("settings", Context.MODE_PRIVATE)
                                        .edit()
                                        .putBoolean("auto_correct_enabled", enabled)
                                        .apply()
                                },
                                modifier = Modifier.scale(0.7f)
                            )
                        }

                        // 展开的内容
                        AnimatedVisibility(
                            visible = isAutoCorrectExpanded,
                            enter = fadeIn() + expandVertically(),
                            exit = fadeOut() + shrinkVertically()
                        ) {
                            Column(
                                modifier = Modifier.padding(
                                    start = 16.dp,
                                    end = 16.dp,
                                    bottom = 16.dp
                                )
                            ) {
                                Text(
                                    text = if (autoCorrectEnabled) 
                                        "输入时将自动应用学习到的规则" 
                                    else 
                                        "保持原始输入格式",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                
                                if (autoCorrectEnabled) {
                                    Divider(modifier = Modifier.padding(vertical = 8.dp))
                                    
                                    // 规则标题和添加按钮
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "规则管理: ${ParseLearningEngine.getAllMappings().size}条",
                                            style = MaterialTheme.typography.titleMedium
                                        )
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            // 搜索按钮
                                            IconButton(
                                                onClick = { 
                                                    showSearch = !showSearch
                                                    if (!showSearch) searchQuery = "" // 关闭搜索时清空查询
                                                }
                                            ) {
                                                Icon(
                                                    imageVector = if (showSearch) Icons.Default.Close else Icons.Default.Search,
                                                    contentDescription = if (showSearch) "关闭搜索" else "搜索规则",
                                                    tint = if (showSearch) MaterialTheme.colorScheme.error 
                                                          else MaterialTheme.colorScheme.primary
                                                )
                                            }
                                            // 导入按钮
                                            IconButton(
                                                onClick = { showImportDialog = true }
                                            ) {
                                                Icon(
                                                    imageVector = Icons.Default.FileDownload,
                                                    contentDescription = "导入规则",
                                                    tint = MaterialTheme.colorScheme.primary
                                                )
                                            }
                                            // 导出按钮
                                            IconButton(
                                                onClick = { showExportDialog = true }
                                            ) {
                                                Icon(
                                                    imageVector = Icons.Default.FileUpload,
                                                    contentDescription = "导出规则",
                                                    tint = MaterialTheme.colorScheme.primary
                                                )
                                            }
                                            // 添加规则按钮
                                            IconButton(
                                                onClick = { showAddRuleDialog = true }
                                            ) {
                                                Icon(
                                                    imageVector = Icons.Default.Add,
                                                    contentDescription = "添加规则",
                                                    tint = MaterialTheme.colorScheme.primary
                                                )
                                            }
                                        }
                                    }

                                    // 搜索框（带动画）
                                    AnimatedVisibility(
                                        visible = showSearch,
                                        enter = expandVertically() + fadeIn(),
                                        exit = shrinkVertically() + fadeOut()
                                    ) {
                                        OutlinedTextField(
                                            value = searchQuery,
                                            onValueChange = { searchQuery = it },
                                            placeholder = { Text("搜索规则") },
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = 8.dp),
                                            singleLine = true,
                                            trailingIcon = if (searchQuery.isNotEmpty()) {
                                                {
                                                    IconButton(onClick = { searchQuery = "" }) {
                                                        Icon(Icons.Default.Clear, contentDescription = "清除")
                                                    }
                                                }
                                            } else null
                                        )
                                    }

                                    // 修改现有的规则列表，添加过滤功能
                                    val mappings = ParseLearningEngine.getAllMappings()
                                    val filteredMappings = mappings.filter { (from, to) ->
                                        searchQuery.isEmpty() || from.contains(searchQuery) || to.contains(searchQuery)
                                    }

                                    if (filteredMappings.isEmpty()) {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(32.dp),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                text = if (searchQuery.isNotEmpty()) "没有找到匹配的规则" else "暂无规则",
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    } else {
                                        LazyVerticalGrid(
                                            columns = GridCells.Adaptive(minSize = 100.dp),
                                            contentPadding = PaddingValues(8.dp),
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            items(filteredMappings.toList()) { (from, to) ->
                                                val isNewRule = from == ParseLearningEngine.getLastLearnedRule()  // 检查是否是最新规则
                                                
                                                Card(
                                                    modifier = Modifier
                                                        .fillMaxWidth()
                                                        .clickable { 
                                                            selectedRule = from to to
                                                            showRuleOptionsDialog = true 
                                                        },
                                                    colors = CardDefaults.cardColors(
                                                        containerColor = if (isNewRule) {
                                                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                                                        } else {
                                                            MaterialTheme.colorScheme.surfaceVariant
                                                        }
                                                    ),
                                                    border = if (isNewRule) {
                                                        BorderStroke(
                                                            width = 1.dp,
                                                            color = MaterialTheme.colorScheme.primary
                                                        )
                                                    } else null
                                                ) {
                                                    Row(
                                                        modifier = Modifier
                                                            .padding(8.dp)
                                                            .fillMaxWidth(),
                                                        verticalAlignment = Alignment.CenterVertically,
                                                        horizontalArrangement = Arrangement.Center
                                                    ) {
                                                        Text(
                                                            text = "'$from' → '$to'",
                                                            style = if (isNewRule) {
                                                                MaterialTheme.typography.bodyLarge.copy(
                                                                    fontWeight = FontWeight.Bold
                                                                )
                                                            } else {
                                                                MaterialTheme.typography.bodyMedium
                                                            },
                                                            maxLines = 1,
                                                            overflow = TextOverflow.Ellipsis
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 生肖设置卡片
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp
                    ),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        // 标题和年份选择按钮
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { isZodiacExpanded = !isZodiacExpanded }
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Start,
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    text = "生肖号码设置",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Icon(
                                    imageVector = if (isZodiacExpanded) 
                                        Icons.Default.KeyboardArrowUp 
                                    else 
                                        Icons.Default.KeyboardArrowDown,
                                    contentDescription = if (isZodiacExpanded) "收起" else "展开"
                                )
                            }
                            Text(
                                text = "${currentYear}年 ${ZodiacUtils.getZodiacForYear(currentYear)}年",
                                modifier = Modifier.clickable { showYearDialog = true },
                                color = MaterialTheme.colorScheme.primary
                            )
                        }

                        // 生肖映射列表（带动画效果）
                        AnimatedVisibility(
                            visible = isZodiacExpanded,
                            enter = expandVertically() + fadeIn(),
                            exit = shrinkVertically() + fadeOut()
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 16.dp, vertical = 8.dp)
                            ) {
                                LazyColumn(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 8.dp),
                                    verticalArrangement = Arrangement.spacedBy(4.dp)
                                ) {
                                    items(zodiacMappings.toList()) { (zodiac, numbers) ->
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = 4.dp),
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Text(
                                                text = zodiac,
                                                style = MaterialTheme.typography.bodyMedium
                                            )
                                            Text(
                                                text = numbers.sorted().joinToString(", "),
                                                style = MaterialTheme.typography.bodyMedium
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 主题色设置卡片
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp
                    ),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        // 标题栏
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { isThemeColorExpanded = !isThemeColorExpanded }
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "主题颜色设置",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Icon(
                                    imageVector = if (isThemeColorExpanded) 
                                        Icons.Default.KeyboardArrowUp 
                                    else 
                                        Icons.Default.KeyboardArrowDown,
                                    contentDescription = if (isThemeColorExpanded) "收起" else "展开"
                                )
                            }
                            // 当前主题色预览
                            Box(
                                modifier = Modifier
                                    .size(24.dp)
                                    .background(
                                        MaterialTheme.colorScheme.primary,
                                        shape = MaterialTheme.shapes.small
                                    )
                                    .clickable { showThemeColorDialog = true }
                            )
                        }

                        // 主题色内容
                        AnimatedVisibility(
                            visible = isThemeColorExpanded,
                            enter = expandVertically() + fadeIn(),
                            exit = shrinkVertically() + fadeOut()
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 16.dp, vertical = 8.dp)
                            ) {
                                Text(
                                    text = "点击色块切换主题色",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 主题色网格
                                LazyVerticalGrid(
                                    columns = GridCells.Fixed(6),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    verticalArrangement = Arrangement.spacedBy(8.dp),
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    items(themeColorOptions.size) { index ->
                                        val theme = themeColorOptions[index]
                                        // 始终使用亮色主题
                                        val colorScheme = theme.lightScheme
                                        Box(
                                            modifier = Modifier
                                                .aspectRatio(1f)
                                                .background(
                                                    color = colorScheme.primary,
                                                    shape = MaterialTheme.shapes.small
                                                )
                                                .clickable {
                                                    // 使用false表示亮色模式
                                                    themeViewModel.updateTheme(context, index, false)
                                                }
                                        )
                                    }
                                    // 随机主题按钮
                                    item {
                                        Box(
                                            modifier = Modifier
                                                .aspectRatio(1f)
                                                .background(
                                                    color = MaterialTheme.colorScheme.surfaceVariant,
                                                    shape = MaterialTheme.shapes.small
                                                )
                                                .clickable {
                                                    // 使用false表示亮色模式
                                                    themeViewModel.enableRandomTheme(context, false)
                                                    
                                                    // 直接在主Activity中请求随机主题，完全避开使用ColorScheme类型
                                                    (context as? MainActivity)?.let {
                                                        it.applyRandomTheme()
                                                    }
                                                }
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Refresh,
                                                contentDescription = "随机主题",
                                                modifier = Modifier
                                                    .align(Alignment.Center)
                                                    .size(30.dp),
                                                tint = if (themeViewModel.isRandomTheme)
                                                    MaterialTheme.colorScheme.primary
                                                else
                                                    MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 统一的流光动画设置卡片
                var isAnimationSettingsExpanded by remember { mutableStateOf(false) }
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
                ) {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        // 标题栏
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(55.dp)
                                .clickable { isAnimationSettingsExpanded = !isAnimationSettingsExpanded }
                                .padding(horizontal = 16.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Start,
                                modifier = Modifier.weight(1f)
                            ) {
                                Text("流光动画设置", style = MaterialTheme.typography.titleMedium)
                                Spacer(modifier = Modifier.width(8.dp))
                                Icon(
                                    imageVector = if (isAnimationSettingsExpanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                                    contentDescription = if (isAnimationSettingsExpanded) "收起" else "展开"
                                )
                            }
                        }

                        // 可展开内容
                        AnimatedVisibility(
                            visible = isAnimationSettingsExpanded,
                            enter = fadeIn() + expandVertically(),
                            exit = fadeOut() + shrinkVertically()
                        ) {
                            var selectedBox by remember { mutableStateOf("输入框") }

                            Column(
                                modifier = Modifier.padding(start = 16.dp, end = 16.dp, bottom = 16.dp),
                                verticalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                // 输入框/输出框切换器
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.Center,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    listOf("输入框", "输出框").forEach { boxName ->
                                        TextButton(
                                            onClick = { selectedBox = boxName },
                                            shape = RoundedCornerShape(50),
                                            colors = ButtonDefaults.textButtonColors(
                                                containerColor = if (selectedBox == boxName) MaterialTheme.colorScheme.primaryContainer else Color.Transparent,
                                                contentColor = if (selectedBox == boxName) MaterialTheme.colorScheme.onPrimaryContainer else MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        ) {
                                            Text(boxName, fontWeight = if (selectedBox == boxName) FontWeight.Bold else FontWeight.Normal)
                                        }
                                    }
                                }

                                Divider()

                                // 根据选择显示不同的设置
                                val isInputBox = selectedBox == "输入框"

                                // --- 开关 ---
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text("启用动画", style = MaterialTheme.typography.bodyLarge)
                                    Switch(
                                        checked = if (isInputBox) AnimationSettings.animationEnabledInput else AnimationSettings.animationEnabledOutput,
                                        onCheckedChange = {
                                            if (isInputBox) AnimationSettings.animationEnabledInput = it else AnimationSettings.animationEnabledOutput = it
                                            AnimationSettings.save(context)
                                        },
                                        modifier = Modifier.scale(0.7f)
                                    )
                                }

                                // --- 动画类型 ---
                                Column(
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text("动画类型", style = MaterialTheme.typography.bodyLarge)
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        val animationTypes = listOf("流光" to AnimationSettings.AnimationType.FLOWING_LIGHT, "呼吸灯" to AnimationSettings.AnimationType.BREATHING_LIGHT)
                                        animationTypes.forEach { (typeName, type) ->
                                            FilterChip(
                                                onClick = {
                                                    if (isInputBox) AnimationSettings.animationTypeInput = type else AnimationSettings.animationTypeOutput = type
                                                    AnimationSettings.save(context)
                                                },
                                                label = { Text(typeName) },
                                                selected = if (isInputBox) AnimationSettings.animationTypeInput == type else AnimationSettings.animationTypeOutput == type,
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                }

                                // --- 方向 (仅流光动画显示) ---
                                if ((if (isInputBox) AnimationSettings.animationTypeInput else AnimationSettings.animationTypeOutput) == AnimationSettings.AnimationType.FLOWING_LIGHT) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text("顺时针旋转", style = MaterialTheme.typography.bodyLarge)
                                        Switch(
                                            checked = if (isInputBox) AnimationSettings.animationDirectionInput else AnimationSettings.animationDirectionOutput,
                                            onCheckedChange = {
                                                if (isInputBox) AnimationSettings.animationDirectionInput = it else AnimationSettings.animationDirectionOutput = it
                                                AnimationSettings.save(context)
                                            },
                                            modifier = Modifier.scale(0.7f)
                                        )
                                    }
                                }

                                // --- 速度 ---
                                val currentAnimationType = if (isInputBox) AnimationSettings.animationTypeInput else AnimationSettings.animationTypeOutput
                                var speedText by remember(selectedBox) { mutableStateOf(if (isInputBox) AnimationSettings.durationMillisInput.toString() else AnimationSettings.durationMillisOutput.toString()) }
                                OutlinedTextField(
                                    value = speedText,
                                    onValueChange = {
                                        speedText = it.filter { char -> char.isDigit() }
                                        speedText.toIntOrNull()?.let { value ->
                                            if (value > 0) {
                                                if (isInputBox) AnimationSettings.durationMillisInput = value else AnimationSettings.durationMillisOutput = value
                                                AnimationSettings.save(context)
                                            }
                                        }
                                    },
                                    label = {
                                        Text(
                                            when (currentAnimationType) {
                                                AnimationSettings.AnimationType.FLOWING_LIGHT -> "流光速度 (时间, ms)"
                                                AnimationSettings.AnimationType.BREATHING_LIGHT -> "呼吸速度 (时间, ms)"
                                            }
                                        )
                                    },
                                    placeholder = { Text("越小越快") },
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    singleLine = true,
                                    modifier = Modifier.fillMaxWidth()
                                )

                                // --- 长度 (仅流光动画显示) ---
                                if (currentAnimationType == AnimationSettings.AnimationType.FLOWING_LIGHT) {
                                    var lengthText by remember(selectedBox) { mutableStateOf(((if (isInputBox) AnimationSettings.snakeLengthPercentInput else AnimationSettings.snakeLengthPercentOutput) * 100).toInt().toString()) }
                                    OutlinedTextField(
                                        value = lengthText,
                                        onValueChange = {
                                            lengthText = it.filter { char -> char.isDigit() }
                                            lengthText.toIntOrNull()?.let { value ->
                                                if (value in 1..100) {
                                                    val percent = value / 100f
                                                    if (isInputBox) AnimationSettings.snakeLengthPercentInput = percent else AnimationSettings.snakeLengthPercentOutput = percent
                                                    AnimationSettings.save(context)
                                                }
                                            }
                                        },
                                        label = { Text("流光长度 (%)") },
                                        placeholder = { Text("1-100") },
                                        trailingIcon = { Text("%") },
                                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                        singleLine = true,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }

                                // --- 颜色深浅 ---
                                Column {
                                    val alpha = if (isInputBox) AnimationSettings.animationAlphaInput else AnimationSettings.animationAlphaOutput
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text("颜色深浅", style = MaterialTheme.typography.bodyMedium)
                                        Text("${(alpha * 100).toInt()}%", style = MaterialTheme.typography.bodyMedium)
                                    }
                                    Slider(
                                        value = alpha,
                                        onValueChange = {
                                            if (isInputBox) AnimationSettings.animationAlphaInput = it else AnimationSettings.animationAlphaOutput = it
                                            AnimationSettings.save(context)
                                        },
                                        valueRange = 0f..1f,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }
                            }
                        }
                    }
                }

                // 赔率设置卡片
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp
                    ),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        // 标题栏
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { isOddsExpanded = !isOddsExpanded }
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "平特赔率设置",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Icon(
                                    imageVector = if (isOddsExpanded) 
                                        Icons.Default.KeyboardArrowUp 
                                    else 
                                        Icons.Default.KeyboardArrowDown,
                                    contentDescription = if (isOddsExpanded) "收起" else "展开"
                                )
                            }
                        }

                        // 赔率内容
                        AnimatedVisibility(
                            visible = isOddsExpanded,
                            enter = expandVertically() + fadeIn(),
                            exit = shrinkVertically() + fadeOut()
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 16.dp, vertical = 8.dp)
                            ) {
                                // 添加赔率编辑状态
                                var showOddsEditDialog by remember { mutableStateOf(false) }
                                var selectedOddsType by remember { mutableStateOf<String?>(null) }
                                var selectedOddsValue by remember { mutableStateOf<String?>(null) }
                                var isCurrentYearOdds by remember { mutableStateOf(false) }
                                // 添加刷新状态变量
                                var oddsRefreshTrigger by remember { mutableStateOf(0) }
                                
                                // 用状态变量包裹赔率数据，确保重置后刷新
                                var oddsState by remember { mutableStateOf(BettingOdds.getCurrentSettings()) }
                                val normalOdds = oddsState.normalOdds
                                val currentYearOdds = oddsState.currentYearOdds
                                val allTypes = normalOdds.keys.union(currentYearOdds.keys).toList()
                                val currentZodiac = ZodiacUtils.getZodiacForYear(currentYear)
                                
                                // 固定表头
                                Card(
                                    modifier = Modifier.fillMaxWidth(),
                                    colors = CardDefaults.cardColors(
                                        containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.6f)
                                    ),
                                    elevation = CardDefaults.cardElevation(0.dp)
                                ) {
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 4.dp, horizontal = 8.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Box(
                                            modifier = Modifier.weight(1.2f).fillMaxWidth(),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                "赔率类型",
                                                style = MaterialTheme.typography.bodyMedium,
                                                textAlign = TextAlign.Center
                                            )
                                        }
                                        Box(
                                            modifier = Modifier.weight(1f).fillMaxWidth(),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                "通用赔率",
                                                style = MaterialTheme.typography.bodyMedium,
                                                textAlign = TextAlign.Center
                                            )
                                        }
                                        Box(
                                            modifier = Modifier.weight(1f).fillMaxWidth(),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                "有($currentZodiac)赔率",
                                                style = MaterialTheme.typography.bodyMedium,
                                                textAlign = TextAlign.Center
                                            )
                                        }
                                    }
                                }
                                // 内容可滚动
                                LazyColumn(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(if (isTabletMode) 600.dp else 360.dp),
                                    verticalArrangement = Arrangement.spacedBy(1.dp)
                                ) {
                                    items(allTypes) { betType ->
                                        val normal = normalOdds[betType]
                                        val current = currentYearOdds[betType]
                                        Card(
                                            modifier = Modifier.fillMaxWidth(),
                                            colors = CardDefaults.cardColors(
                                                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                                            )
                                        ) {
                                            Row(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .padding(vertical = 4.dp, horizontal = 8.dp),
                                                horizontalArrangement = Arrangement.SpaceBetween,
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                Box(
                                                    modifier = Modifier.weight(1.2f).fillMaxWidth(),
                                                    contentAlignment = Alignment.Center
                                                ) {
                                                    Text(
                                                        betType,
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        textAlign = TextAlign.Center
                                                    )
                                                }
                                                // 默认赔率单元格
                                                Box(
                                                    modifier = Modifier
                                                        .weight(1f)
                                                        .fillMaxWidth()
                                                        .clickable(enabled = normal != null) {
                                                            if (normal != null) {
                                                                selectedOddsType = betType
                                                                selectedOddsValue = normal.toString()
                                                                isCurrentYearOdds = false
                                                                showOddsEditDialog = true
                                                            }
                                                        },
                                                    contentAlignment = Alignment.Center
                                                ) {
                                                    Text(
                                                        normal?.toString() ?: "-",
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        color = MaterialTheme.colorScheme.primary,
                                                        textAlign = TextAlign.Center
                                                    )
                                                }
                                                // 有本命肖赔率单元格
                                                Box(
                                                    modifier = Modifier
                                                        .weight(1f)
                                                        .fillMaxWidth()
                                                        .clickable(enabled = current != null) {
                                                            if (current != null) {
                                                                selectedOddsType = betType
                                                                selectedOddsValue = current.toString()
                                                                isCurrentYearOdds = true
                                                                showOddsEditDialog = true
                                                            }
                                                        },
                                                    contentAlignment = Alignment.Center
                                                ) {
                                                    Text(
                                                        current?.toString() ?: "-",
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        color = MaterialTheme.colorScheme.primary,
                                                        textAlign = TextAlign.Center
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(1.dp))
                                
                                // 使用提示和重置按钮同一行，紧凑布局
                                var showResetConfirm by remember { mutableStateOf(false) }
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 2.dp, bottom = 0.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = "使用提示：点击数字可编辑",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        maxLines = 1
                                    )
                                    TextButton(
                                        onClick = { showResetConfirm = true },
                                        colors = ButtonDefaults.textButtonColors(
                                            contentColor = MaterialTheme.colorScheme.error
                                        )
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Refresh,
                                            contentDescription = "重置",
                                            modifier = Modifier.size(16.dp)
                                        )
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text("重置赔率")
                                    }
                                }
                                if (showResetConfirm) {
                                    AlertDialog(
                                        onDismissRequest = { showResetConfirm = false },
                                        title = { Text("确认重置") },
                                        text = { Text("确定要将所有赔率重置为默认值吗？此操作不可撤销。") },
                                        confirmButton = {
                                            TextButton(
                                                onClick = {
                                                    BettingOdds.resetToDefaultSettings(context)
                                                    oddsRefreshTrigger++
                                                    oddsState = BettingOdds.getCurrentSettings() // 强制刷新
                                                    Toast.makeText(context, "已重置为默认赔率", Toast.LENGTH_SHORT).show()
                                                    showResetConfirm = false
                                                }
                                            ) {
                                                Text("确定")
                                            }
                                        },
                                        dismissButton = {
                                            TextButton(onClick = { showResetConfirm = false }) {
                                                Text("取消")
                                            }
                                        }
                                    )
                                }
                                
                                // 赔率编辑对话框
                                if (showOddsEditDialog && selectedOddsType != null) {
                                    var oddsValue by remember { mutableStateOf(selectedOddsValue ?: "") }
                                    var errorText by remember { mutableStateOf<String?>(null) }
                                    
                                    AlertDialog(
                                        onDismissRequest = { showOddsEditDialog = false },
                                        title = { Text("设置赔率") },
                                        text = {
                                            Column {
                                                Text(
                                                    text = "赔率类型: $selectedOddsType",
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    modifier = Modifier.padding(bottom = 12.dp)
                                                )
                                                
                                                OutlinedTextField(
                                                    value = oddsValue,
                                                    onValueChange = { newValue ->
                                                        // 仅允许输入数字和小数点
                                                        if (newValue.isEmpty() || newValue.matches(Regex("^\\d*\\.?\\d*$"))) {
                                                            oddsValue = newValue
                                                            errorText = null
                                                        }
                                                    },
                                                    label = { Text("赔率输入框") },
                                                    keyboardOptions = KeyboardOptions(
                                                        keyboardType = KeyboardType.Decimal,
                                                        imeAction = ImeAction.Done
                                                    ),
                                                    keyboardActions = KeyboardActions(
                                                        onDone = { focusManager.clearFocus() }
                                                    ),
                                                    isError = errorText != null,
                                                    singleLine = true,
                                                    modifier = Modifier.fillMaxWidth()
                                                )
                                                
                                                if (errorText != null) {
                                                    Text(
                                                        text = errorText!!,
                                                        color = MaterialTheme.colorScheme.error,
                                                        style = MaterialTheme.typography.bodySmall,
                                                        modifier = Modifier.padding(top = 4.dp)
                                                    )
                                                }
                                            }
                                        },
                                        confirmButton = {
                                            TextButton(
                                                onClick = {
                                                    try {
                                                        val odds = oddsValue.toDouble()
                                                        if (odds <= 0) {
                                                            errorText = "赔率必须大于0"
                                                        } else {
                                                            selectedOddsType?.let { betType ->
                                                                if (isCurrentYearOdds) {
                                                                    BettingOdds.updateCurrentYearOdds(betType, odds, context)
                                                                } else {
                                                                    BettingOdds.updateNormalOdds(betType, odds, context)
                                                                }
                                                                oddsRefreshTrigger++
                                                                oddsState = BettingOdds.getCurrentSettings() // 强制刷新
                                                                Toast.makeText(context, "赔率更新成功", Toast.LENGTH_SHORT).show()
                                                                showOddsEditDialog = false
                                                            }
                                                        }
                                                    } catch (e: Exception) {
                                                        errorText = "请输入有效的赔率"
                                                    }
                                                }
                                            ) {
                                                Text("确定")
                                            }
                                        },
                                        dismissButton = {
                                            TextButton(onClick = { showOddsEditDialog = false }) {
                                                Text("取消")
                                            }
                                        }
                                    )
                                }
                            }
                        }
                    }
                }

                // 返水设置卡片
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp
                    ),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // 标题栏和开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(55.dp)
                                .clickable { isRebateExpanded = !isRebateExpanded }
                                .padding(horizontal = 16.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Start,
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    text = "返水设置开关",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Icon(
                                    imageVector = if (isRebateExpanded) 
                                        Icons.Default.KeyboardArrowUp 
                                    else 
                                        Icons.Default.KeyboardArrowDown,
                                    contentDescription = if (isRebateExpanded) "收起" else "展开"
                                )
                            }
                            Switch(
                                checked = rebateEnabled,
                                onCheckedChange = { enabled ->
                                    rebateEnabled = enabled
                                    context.getSharedPreferences("settings", Context.MODE_PRIVATE)
                                        .edit()
                                        .putBoolean("rebate_enabled", enabled)
                                        .apply()
                                    
                                    // 显示状态变更提示
                                    Toast.makeText(
                                        context, 
                                        if (enabled) "返水功能已开启" else "返水功能已关闭", 
                                        Toast.LENGTH_SHORT
                                    ).show()
                                },
                                modifier = Modifier.scale(0.7f)
                            )
                        }

                        // 展开的内容
                        AnimatedVisibility(
                            visible = isRebateExpanded,
                            enter = fadeIn() + expandVertically(),
                            exit = fadeOut() + shrinkVertically()
                        ) {
                            Column(
                                modifier = Modifier.padding(
                                    start = 16.dp,
                                    end = 16.dp,
                                    bottom = 16.dp
                                )
                            ) {
                                Text(
                                    text = if (rebateEnabled) 
                                        "返水计算功能(已开启)"
                                    else 
                                        "返水计算功能(已关闭)",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 返水值输入框
                                OutlinedTextField(
                                    value = rebateValue,
                                    onValueChange = { newValue ->
                                        // 仅允许输入数字和小数点
                                        if (newValue.isEmpty() || newValue.matches(Regex("^\\d*\\.?\\d*$"))) {
                                            rebateValue = newValue
                                            
                                            // 保存返水值
                                            try {
                                                val floatValue = newValue.toFloatOrNull() ?: 0f
                                                context.getSharedPreferences("settings", Context.MODE_PRIVATE)
                                                    .edit()
                                                    .putFloat("rebate_value", floatValue)
                                                    .apply()
                                            } catch (e: Exception) {
                                                // 处理转换异常
                                            }
                                        }
                                    },
                                    label = { Text("返水值 (%)") },
                                    enabled = rebateEnabled,
                                    keyboardOptions = KeyboardOptions(
                                        keyboardType = KeyboardType.Decimal,
                                        imeAction = ImeAction.Done
                                    ),
                                    keyboardActions = KeyboardActions(
                                        onDone = { focusManager.clearFocus() }
                                    ),
                                    trailingIcon = {
                                        Text("%", style = MaterialTheme.typography.bodyMedium)
                                    },
                                    singleLine = true,
                                    modifier = Modifier.fillMaxWidth()
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 返水说明文本
                                Text(
                                    text = buildAnnotatedString {
                                        withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                                            append("说明：")
                                        }
                                        append("返水值表示返还的百分比。例如，返水值设为4.0，表示4%，总数1万元，将返还400元。")
                                    },
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }

                // 年份选择对话框
                if (showYearDialog) {
                    var yearText by remember { mutableStateOf(currentYear.toString()) }
                    var error by remember { mutableStateOf<String?>(null) }
                    var previewMappings by remember { mutableStateOf(zodiacMappings) }

                    AlertDialog(
                        onDismissRequest = { showYearDialog = false },
                        title = { Text("选择年份") },
                        text = {
                            Column {
                                OutlinedTextField(
                                    value = yearText,
                                    onValueChange = { 
                                        yearText = it
                                        // 实时预览新的映射
                                        yearText.toIntOrNull()?.let { year ->
                                            if (year in 1992..3992) {
                                                previewMappings = ZodiacUtils.generateZodiacMapping(year)
                                                // 实时更新主界面的映射
                                                zodiacMappings = previewMappings
                                            }
                                        }
                                    },
                                    label = { Text("输入年份") },
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    isError = error != null,
                                    modifier = Modifier.fillMaxWidth()
                                )
                                if (error != null) {
                                    Text(
                                        text = error!!,
                                        color = MaterialTheme.colorScheme.error,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }

                                LaunchedEffect(yearText) {
                                    error = when {
                                        yearText.toIntOrNull() == null -> "请输入有效年份"
                                        yearText.toInt() !in 1992..3992 -> "年份必须在1992-3992之间"
                                        else -> null
                                    }
                                }
                            }
                        },
                        confirmButton = {
                            TextButton(
                                onClick = {
                                    val year = yearText.toIntOrNull() ?: return@TextButton
                                    if (year in 1992..3992) {
                                        currentYear = year
                                        ZodiacUtils.updateBaseYear(context, year)
                                        zodiacMappings = ZodiacUtils.getZodiacMappings()
                                        showYearDialog = false
                                    }
                                }
                            ) {
                                Text("确定")
                            }
                        },
                        dismissButton = {
                            TextButton(onClick = { showYearDialog = false }) {
                                Text("取消")
                            }
                        }
                    )
                }

                // 添加规则对话框
                if (showAddRuleDialog) {
                    var fromText by remember { mutableStateOf("") }
                    var toText by remember { mutableStateOf("") }
                    
                    AlertDialog(
                        onDismissRequest = { showAddRuleDialog = false },
                        title = { Text("添加新规则") },
                        text = {
                            Column {
                                OutlinedTextField(
                                    value = fromText,
                                    onValueChange = { fromText = it },
                                    label = { Text("原始符号") },
                                    singleLine = true,
                                    modifier = Modifier.fillMaxWidth()
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                OutlinedTextField(
                                    value = toText,
                                    onValueChange = { toText = it },
                                    label = { Text("替换为") },
                                    singleLine = true,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }
                        },
                        confirmButton = {
                            TextButton(
                                onClick = {
                                    if (fromText.isNotEmpty()) {
                                        ParseLearningEngine.addNewRule(fromText, toText)
                                        showAddRuleDialog = false
                                    }
                                }
                            ) {
                                Text("添加")
                            }
                        },
                        dismissButton = {
                            TextButton(onClick = { showAddRuleDialog = false }) {
                                Text("取消")
                            }
                        }
                    )
                }

                // 规则选项对话框
                if (showRuleOptionsDialog) {
                    AlertDialog(
                        onDismissRequest = { showRuleOptionsDialog = false },
                        title = { Text("规则操作") },
                        text = {
                            Column {
                                TextButton(
                                    onClick = {
                                        showRuleOptionsDialog = false
                                        showEditRuleDialog = true
                                    },
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text("编辑")
                                }
                                TextButton(
                                    onClick = {
                                        selectedRule?.let { (from, _) ->
                                            ParseLearningEngine.deleteRule(from)
                                        }
                                        showRuleOptionsDialog = false
                                    },
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text("删除")
                                }
                            }
                        },
                        confirmButton = {},
                        dismissButton = {
                            TextButton(onClick = { showRuleOptionsDialog = false }) {
                                Text("取消")
                            }
                        }
                    )
                }

                // 编辑规则对话框
                if (showEditRuleDialog) {
                    var fromText by remember { mutableStateOf(selectedRule?.first ?: "") }
                    var toText by remember { mutableStateOf(selectedRule?.second ?: "") }
                    
                    AlertDialog(
                        onDismissRequest = { showEditRuleDialog = false },
                        title = { Text("编辑规则") },
                        text = {
                            Column {
                                OutlinedTextField(
                                    value = fromText,
                                    onValueChange = { fromText = it },
                                    label = { Text("原始符号") },
                                    singleLine = true,
                                    modifier = Modifier.fillMaxWidth()
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                OutlinedTextField(
                                    value = toText,
                                    onValueChange = { toText = it },
                                    label = { Text("替换为") },
                                    singleLine = true,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }
                        },
                        confirmButton = {
                            TextButton(
                                onClick = {
                                    if (fromText.isNotEmpty()) {
                                        selectedRule?.let { (oldFrom, _) ->
                                            ParseLearningEngine.updateRule(oldFrom, fromText, toText)
                                        }
                                        showEditRuleDialog = false
                                    }
                                }
                            ) {
                                Text("保存")
                            }
                        },
                        dismissButton = {
                            TextButton(onClick = { showEditRuleDialog = false }) {
                                Text("取消")
                            }
                        }
                    )
                }

                // 添加导入对话框
                if (showImportDialog) {
                    var importText by remember { mutableStateOf("") }
                    var previewRules by remember { mutableStateOf<List<Pair<String, String>>>(emptyList()) }
                    
                    AlertDialog(
                        onDismissRequest = { showImportDialog = false },
                        title = { Text("导入规则") },
                        text = {
                            Column {
                                OutlinedTextField(
                                    value = importText,
                                    onValueChange = { text ->
                                        importText = text
                                        // 实时解析预览
                                        try {
                                            previewRules = ParseLearningEngine.parseRulesFromString(text)
                                        } catch (e: Exception) {
                                            previewRules = emptyList()
                                        }
                                    },
                                    label = { Text("粘贴规则文本") },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(120.dp),
                                    maxLines = 5
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "预览:",
                                    style = MaterialTheme.typography.titleSmall
                                )
                                
                                if (previewRules.isEmpty()) {
                                    Text(
                                        text = "无有效规则",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.error
                                    )
                                } else {
                                    LazyVerticalGrid(
                                        columns = GridCells.Fixed(2),
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        verticalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(120.dp)
                                    ) {
                                        items(previewRules) { (from, to) ->
                                            Card(
                                                colors = CardDefaults.cardColors(
                                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                                                )
                                            ) {
                                                Row(
                                                    modifier = Modifier
                                                        .padding(8.dp)
                                                        .fillMaxWidth(),
                                                    horizontalArrangement = Arrangement.Center
                                                ) {
                                                    Text(
                                                        text = "'$from' → '$to'",
                                                        style = MaterialTheme.typography.bodySmall,
                                                        maxLines = 1,
                                                        overflow = TextOverflow.Ellipsis
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        confirmButton = {
                            TextButton(
                                onClick = {
                                    if (previewRules.isNotEmpty()) {
                                        ParseLearningEngine.importRules(previewRules)
                                        showImportDialog = false
                                    }
                                },
                                enabled = previewRules.isNotEmpty()
                            ) {
                                Text("导入")
                            }
                        },
                        dismissButton = {
                            TextButton(onClick = { showImportDialog = false }) {
                                Text("取消")
                            }
                        }
                    )
                }

                // 添加导出对话框
                if (showExportDialog) {
                    AlertDialog(
                        onDismissRequest = { showExportDialog = false },
                        title = { Text("导出规则") },
                        text = {
                            val rulesText = ParseLearningEngine.exportRulesToString()
                            Column {
                                Text(
                                    text = "规则文本:",
                                    style = MaterialTheme.typography.titleSmall
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = rulesText,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(8.dp)
                                )
                            }
                        },
                        confirmButton = {
                            TextButton(
                                onClick = {
                                    val intent = Intent(Intent.ACTION_SEND).apply {
                                        type = "text/plain"
                                        putExtra(Intent.EXTRA_TEXT, ParseLearningEngine.exportRulesToString())
                                    }
                                    context.startActivity(Intent.createChooser(intent, "分享规则"))
                                    showExportDialog = false
                                }
                            ) {
                                Text("分享")
                            }
                        },
                        dismissButton = {
                            TextButton(onClick = { showExportDialog = false }) {
                                Text("取消")
                            }
                        }
                    )
                }
            }
        }
    }
} 