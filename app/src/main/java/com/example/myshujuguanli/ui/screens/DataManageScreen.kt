package com.example.myshujuguanli.ui.screens

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.filled.Flag
import androidx.compose.material.icons.filled.OutlinedFlag
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.Divider
import androidx.compose.material3.ElevatedFilterChip
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import com.example.myshujuguanli.utils.DatabaseUtils
import com.example.myshujuguanli.utils.ExportUtils
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

// 这个文件中的标识管理不会影响到IdentifierCard.kt和IdentifierDialogs.kt中的标识管理
// 因为这里只是用于显示和搜索标识,不涉及标识的添加、编辑和删除操作
// IdentifierCard.kt和IdentifierDialogs.kt负责标识的CRUD操作
// 这里只是读取标识进行展示和搜索

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DataManageScreen(
    onBack: () -> Unit
) {
    val context = LocalContext.current
    var originalDataList by remember { mutableStateOf<List<DatabaseUtils.OriginalDataInfo>>(emptyList()) }
    var showDeleteConfirm by remember { mutableStateOf<String?>(null) }
    
    // 将 markedItems 移动到 DataManageScreen 的顶层作用域
    var markedItems by remember { mutableStateOf(setOf<String>()) }
    
    // 添加批量删除确认对话框状态
    var showBatchDeleteConfirm by remember { mutableStateOf(false) }
    
    // 添加批注状态管理，并从 SharedPreferences 加载
    var notesMap by remember { 
        val prefs = context.getSharedPreferences("data_notes", Context.MODE_PRIVATE)
        val savedNotes = prefs.all.mapValues { it.value as String }.toMutableMap()
        mutableStateOf(savedNotes) 
    }

    // 添加保存批注的函数
    fun saveNote(timestamp: String, note: String) {
        context.getSharedPreferences("data_notes", Context.MODE_PRIVATE)
            .edit()
            .putString(timestamp, note)
            .apply()
        
        notesMap = notesMap.toMutableMap().apply {
            put(timestamp, note)
        }
    }

    // 添加删除批注的函数
    fun deleteNote(timestamp: String) {
        context.getSharedPreferences("data_notes", Context.MODE_PRIVATE)
            .edit()
            .remove(timestamp)
            .apply()
        
        notesMap = notesMap.toMutableMap().apply {
            remove(timestamp)
        }
    }

    // 搜索条件
    var searchText by remember { mutableStateOf("") }
    var selectedFilters by remember { mutableStateOf(setOf<String>()) }
    var selectedArea by remember { mutableStateOf<String?>(null) }  // null 表示显示全部数据
    var showDatePicker by remember { mutableStateOf(false) }
    var startDate by remember { mutableStateOf<Date?>(null) }
    var endDate by remember { mutableStateOf<Date?>(null) }
    
    // 在 DataManageScreen 函数中添加导出选项对话框状态
    var showExportOptionsDialog by remember { mutableStateOf(false) }

    // 在 DataManageScreen 函数顶部添加状态
    var showRangeSelectDialog by remember { mutableStateOf(false) }
    var rangeStartText by remember { mutableStateOf("") }
    var rangeEndText by remember { mutableStateOf("") }

    // 在 DataManageScreen 函数顶部添加错误状态
    var rangeError by remember { mutableStateOf<String?>(null) }

    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    // 添加时间比较辅助函数
    fun isDateInRange(date: Date, start: Date?, end: Date?): Boolean {
        if (start == null || end == null) return true
        return date.time >= start.time && date.time <= end.time
    }

    // 修改过滤逻辑
    val filteredList = remember(originalDataList, searchText, selectedFilters, selectedArea, startDate, endDate) {
        originalDataList.filter { data ->
            var matches = true

            // 地区筛选
            if (selectedArea != null) {
                matches = matches && data.area == selectedArea
            }

            // 搜索文本筛选
            if (searchText.isNotEmpty()) {
                // 检查是否同时选择了"原始数据"和"标识"
                val isMultiSearch = selectedFilters.contains("原始数据") && selectedFilters.contains("标识")
                
                matches = matches && when {
                    // 复合搜索情况：空格分隔的搜索词，前面匹配标识，后面匹配原始数据
                    isMultiSearch && searchText.contains(" ") -> {
                        val parts = searchText.split(" ", limit = 2)
                        val identifierPart = parts[0].trim()
                        val dataPart = parts[1].trim()
                        
                        val identifierMatches = if (identifierPart.isNotEmpty()) {
                            // 特殊处理"无标识"的情况
                            if (identifierPart == "无标识") {
                                data.identifier.isEmpty()
                            } else {
                                data.identifier.contains(identifierPart) 
                            }
                        } else true
                        
                        val dataMatches = if (dataPart.isNotEmpty()) 
                            data.originalData.contains(dataPart) else true
                        
                        identifierMatches && dataMatches
                    }
                    // 只选择了原始数据
                    selectedFilters.contains("原始数据") && !selectedFilters.contains("标识") -> 
                        data.originalData.contains(searchText)
                    // 只选择了标识
                    selectedFilters.contains("标识") && !selectedFilters.contains("原始数据") -> {
                        // 特殊处理"无标识"的情况
                        if (searchText.trim() == "无标识") {
                            data.identifier.isEmpty()
                        } else {
                            data.identifier.contains(searchText)
                        }
                    }
                    // 同时选择但没有使用空格分隔
                    isMultiSearch -> {
                        // 特殊处理"无标识"的情况
                        if (searchText.trim() == "无标识") {
                            data.identifier.isEmpty() || data.originalData.contains(searchText)
                        } else {
                            data.identifier.contains(searchText) || data.originalData.contains(searchText)
                        }
                    }
                    // 没有选择搜索类型但输入了搜索文本
                    else -> {
                        // 特殊处理"无标识"的情况
                        if (searchText.trim() == "无标识") {
                            data.identifier.isEmpty() || data.originalData.contains(searchText)
                        } else {
                            data.identifier.contains(searchText) || data.originalData.contains(searchText)
                        }
                    }
                }
            }

            // 时间范围筛选
            if (startDate != null && endDate != null) {
                try {
                    val dataTime = dateFormat.parse(data.timestamp)
                    matches = matches && dataTime?.let {
                        isDateInRange(it, startDate, endDate)
                    } ?: false
                } catch (e: Exception) {
                    // Log.e("DataManageScreen", "时间解析错误: ${e.message}")
                    matches = false
                }
            }

            matches
        }
    }

    // 初始加载数据
    LaunchedEffect(Unit) {
        originalDataList = DatabaseUtils.getOriginalDataList(context)
    }

    val listState = rememberLazyListState()
    val scope = rememberCoroutineScope()
    
    // 使用完全不同的方法来实现应用商店风格的滚动隐藏效果
    // 跟踪滚动方向和状态
    var isScrollingUp by remember { mutableStateOf(false) }
    var previousScrollOffset by remember { mutableStateOf(0) }
    var previousFirstVisibleItem by remember { mutableStateOf(0) }
    
    // 监听滚动状态，更准确判断滚动方向
    LaunchedEffect(listState) {
        snapshotFlow { listState.firstVisibleItemIndex to listState.firstVisibleItemScrollOffset }
            .collect { (firstVisibleItem, scrollOffset) ->
                // 判断滚动方向
                isScrollingUp = if (firstVisibleItem > previousFirstVisibleItem) {
                    // 如果可见的第一个项目索引增加，肯定是向上滚动
                    true
                } else if (firstVisibleItem < previousFirstVisibleItem) {
                    // 如果可见的第一个项目索引减少，肯定是向下滚动
                    false
                } else {
                    // 如果索引相同，则比较滚动偏移量
                    scrollOffset > previousScrollOffset
                }
                
                // 保存状态以便下次比较
                previousScrollOffset = scrollOffset
                previousFirstVisibleItem = firstVisibleItem
            }
    }
    
    // 使用动画值控制筛选区域的高度
    val filterHeightFactor by animateFloatAsState(
        targetValue = if (isScrollingUp) 0f else 1f,
        animationSpec = tween(durationMillis = 300),
        label = "filterHeightFactor"
    )
    
    // 计算筛选区域的实际高度
    val filterHeight = (40 * filterHeightFactor).dp
    
    // 先计算 luminance 值，避免在 SideEffect 中直接使用 MaterialTheme
    val isLightBackground = MaterialTheme.colorScheme.background.luminance() > 0.5f 
    
    // 状态栏颜色设置
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            // 设置状态栏颜色为透明
            window.statusBarColor = Color.Transparent.toArgb()
            // 设置状态栏为沉浸式，确保内容绘制到状态栏下方
            WindowCompat.setDecorFitsSystemWindows(window, false)
            // 根据应用背景色判断状态栏图标颜色
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = isLightBackground
        }
    }

    Surface(
        color = MaterialTheme.colorScheme.background,
        modifier = Modifier.fillMaxSize()
    ) {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            topBar = {
                Column(modifier = Modifier.statusBarsPadding()) {
                    TopAppBar(
                        title = {
                            Column(
                                verticalArrangement = Arrangement.spacedBy(3.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Text("对账管理")
                                    Text(
                                        text = "(总${if (markedItems.isEmpty()) filteredList.size else markedItems.size}条单)",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                    )
                                    
                                    // 计算并显示总金额（根据标记状态显示不同的总额）
                                    val totalAmount = if (markedItems.isEmpty()) {
                                        // 显示所有数据的总额
                                        filteredList.sumOf { data ->
                                            val normalAmount = data.numberDetails.sumOf { it.amount }
                                            val specialAmount = data.specialAmount
                                            normalAmount + specialAmount
                                        }
                                    } else {
                                        // 只显示标记项的总额
                                        filteredList
                                            .filter { data -> markedItems.contains(data.timestamp) }
                                            .sumOf { data ->
                                                val normalAmount = data.numberDetails.sumOf { it.amount }
                                                val specialAmount = data.specialAmount
                                                normalAmount + specialAmount
                                            }
                                    }
                                    
                                    Text(
                                        text = "总数: $totalAmount",
                                        style = MaterialTheme.typography.bodyMedium
                                        // color = MaterialTheme.colorScheme.primary
                                    )
                                }
                                
                                // 添加标识金额统计行
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .horizontalScroll(rememberScrollState()),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    // 按标识分组计算总金额 - 使用原始数据列表计算，而不是筛选后的列表
                                    val tagTotals = originalDataList
                                        .groupBy { it.identifier.ifEmpty { "无标识" } }
                                        .map { (tag, dataList) ->
                                            val total = dataList.sumOf { data ->
                                                val normalAmount = data.numberDetails.sumOf { it.amount }
                                                val specialAmount = data.specialAmount
                                                normalAmount + specialAmount
                                            }
                                            Triple(tag, dataList.size, total)  // 三元组：标识、条数、总金额
                                        }
                                        .sortedByDescending { it.third }  // 按金额降序排序
                                    
                                    // 显示标识-条数-总金额格式
                                    tagTotals.forEach { (tag, count, total) ->
                                        val isSelected = (searchText == tag || searchText == "$tag ") && 
                                            selectedFilters.contains("标识")
                                        Text(
                                            text = "$tag-$count-$total",
                                            style = MaterialTheme.typography.bodySmall,
                                            // 选中的标识显示不同颜色
                                            color = if (isSelected) 
                                                MaterialTheme.colorScheme.tertiary 
                                            else 
                                                MaterialTheme.colorScheme.primary,
                                            modifier = Modifier
                                                .clip(RoundedCornerShape(4.dp))
                                                // 选中的标识有背景色
                                                .then(
                                                    if (isSelected) {
                                                        Modifier.background(
                                                            MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.3f)
                                                        )
                                                    } else {
                                                        Modifier
                                                    }
                                                )
                                                .clickable {
                                                    // 判断是否是已选中的标识
                                                    if (isSelected) {
                                                        // 如果已选中，则取消选中，清空搜索条件
                                                        searchText = ""
                                                        selectedFilters = emptySet()
                                                    } else {
                                                        // 如果未选中，则设置搜索条件
                                                        // 添加空格以适配原始数据搜索功能
                                                        // 特殊处理无标识的情况
                                                        searchText = if (tag == "无标识") "无标识 " else "$tag "
                                                        // 同时选中"标识"和"原始数据"
                                                        selectedFilters = setOf("标识", "原始数据")
                                                        selectedArea = null // 重置区域筛选
                                                        startDate = null // 清除日期范围
                                                        endDate = null
                                                    }
                                                }
                                                .padding(horizontal = 4.dp, vertical = 2.dp)
                                        )
                                    }
                                }
                            }
                        },
                        navigationIcon = {
                            IconButton(onClick = onBack) {
                                Icon(Icons.Default.ArrowBack, "返回")
                            }
                        },
                        actions = {
                            IconButton(onClick = { showExportOptionsDialog = true }) {
                                Icon(Icons.Default.Share, contentDescription = if (markedItems.isNotEmpty()) "分享标记数据" else "分享全部数据")
                            }
                        },
                        colors = TopAppBarDefaults.topAppBarColors(
                            containerColor = MaterialTheme.colorScheme.background,
                            scrolledContainerColor = MaterialTheme.colorScheme.background
                        )
                    )
                    
                    // 筛选区域 - 现在带有高度动画
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(filterHeight)  // 应用动画高度
                            .horizontalScroll(rememberScrollState()),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 地区选择
                        listOf("澳门", "香港").forEach { area ->
                            ElevatedFilterChip(
                                selected = selectedArea == area,
                                onClick = { selectedArea = if (selectedArea == area) null else area },
                                label = { Text(area) },
                                modifier = Modifier.padding(horizontal = 2.dp)
                            )
                        }

                        // 其他筛选条件
                        listOf("原始数据", "时间", "标识").forEach { filter ->
                            ElevatedFilterChip(
                                selected = selectedFilters.contains(filter),
                                onClick = {
                                    selectedFilters = if (selectedFilters.contains(filter)) {
                                        selectedFilters - filter
                                    } else {
                                        selectedFilters + filter
                                    }
                                    if (filter == "时间") {
                                        showDatePicker = true
                                    }
                                },
                                label = { Text(filter) },
                                modifier = Modifier.padding(horizontal = 2.dp)
                            )
                        }
                    }
                }
            }
        ) { innerPadding ->
            Box(modifier = Modifier.fillMaxSize()) {  // 添加 Box 作为容器
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(innerPadding)
                ) {
                    // 搜索输入框
                    if (selectedFilters.any { it != "时间" }) {
                        OutlinedTextField(
                            value = searchText,
                            onValueChange = { searchText = it },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 8.dp, vertical = 4.dp),
                            placeholder = { Text("请输入搜索内容") },
                            trailingIcon = { Icon(Icons.Default.Search, "搜索") },
                            singleLine = true
                        )
                    }

                    // 显示已选择的时间范围
                    if (startDate != null && endDate != null) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 8.dp, vertical = 4.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "时间范围: ${dateFormat.format(startDate).substring(5)} ~ " +
                                        dateFormat.format(endDate).substring(5),
                                style = MaterialTheme.typography.bodyMedium
                            )

                            // 添加清除按钮
                            IconButton(
                                onClick = {
                                    startDate = null
                                    endDate = null
                                    selectedFilters = selectedFilters - "时间"  // 同时取消时间筛选条件的选中状态
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "清除时间范围",
                                    tint = MaterialTheme.colorScheme.error
                                )
                            }
                        }
                    }

                    // 数据列表
                    LazyColumn(
                        state = listState,
                        modifier = Modifier.fillMaxWidth(),
                        contentPadding = PaddingValues(8.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        itemsIndexed(filteredList) { index, data ->
                            DataItem(
                                data = data,
                                onDelete = { 
                                    showDeleteConfirm = data.timestamp
                                    deleteNote(data.timestamp)
                                },
                                index = index,
                                totalCount = filteredList.size,
                                isMarked = markedItems.contains(data.timestamp),
                                onToggleMark = {
                                    val newIsMarked = !markedItems.contains(data.timestamp)
                                    markedItems = if (newIsMarked) {
                                        markedItems + data.timestamp
                                    } else {
                                        if (notesMap[data.timestamp].isNullOrEmpty()) {
                                            deleteNote(data.timestamp)
                                        }
                                        markedItems - data.timestamp
                                    }
                                },
                                note = notesMap[data.timestamp],
                                onNoteChange = { newNote ->
                                    if (newNote.isNotEmpty()) {
                                        saveNote(data.timestamp, newNote)
                                    } else {
                                        deleteNote(data.timestamp)
                                    }
                                },
                                onNoteSave = { timestamp, note -> saveNote(timestamp, note) },
                                onNoteDelete = { timestamp -> deleteNote(timestamp) }
                            )
                        }
                    }
                }
                
                // FAB 按钮组
                if (markedItems.isNotEmpty()) {
                    Column(
                        modifier = Modifier
                            .align(Alignment.CenterEnd)  // 改为右侧居中对齐
                            .padding(end = 16.dp),  // 添加右侧边距
                        verticalArrangement = Arrangement.spacedBy(8.dp)  // 垂直间距
                    ) {
                        // 删除按钮（带标记数量指示器）
                        Box(contentAlignment = Alignment.TopEnd) {
                            FloatingActionButton(
                                onClick = { showBatchDeleteConfirm = true },
                                containerColor = MaterialTheme.colorScheme.errorContainer
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = "批量删除",
                                    tint = MaterialTheme.colorScheme.onErrorContainer
                                )
                            }
                            
                            // 标记数量指示器
                            Box(
                                modifier = Modifier
                                    .padding(top = 2.dp, end = 2.dp)
                                    .background(
                                        color = MaterialTheme.colorScheme.error,
                                        shape = CircleShape
                                    )
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = "${markedItems.size}",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onError
                                )
                            }
                        }

                        // 全选按钮 - 仅在未全选时显示
                        if (markedItems.size < filteredList.size) {
                            // 全选按钮
                            FloatingActionButton(
                                onClick = { 
                                    markedItems = filteredList.map { it.timestamp }.toSet() 
                                },
                                containerColor = MaterialTheme.colorScheme.primaryContainer
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Done,
                                    contentDescription = "全选",
                                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                                )
                            }
                            
                            // 范围选择按钮
                            FloatingActionButton(
                                onClick = { showRangeSelectDialog = true },
                                containerColor = MaterialTheme.colorScheme.tertiaryContainer
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Search,  // 或其他合适的图标
                                    contentDescription = "序号范围选择",
                                    tint = MaterialTheme.colorScheme.onTertiaryContainer
                                )
                            }
                        }

                        // 取消按钮 - 当标记数量大于1时显示
                        if (markedItems.size > 1) {
                            FloatingActionButton(
                                onClick = {
                                    markedItems = emptySet()
                                },
                                containerColor = MaterialTheme.colorScheme.secondaryContainer
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "取消选择",
                                    tint = MaterialTheme.colorScheme.onSecondaryContainer
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // 删除确认对话框
    if (showDeleteConfirm != null) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirm = null },
            title = { Text("确认删除") },
            text = {
                // 找到对应的数据显示更详细的信息
                val dataToDelete = originalDataList.find { it.timestamp == showDeleteConfirm }
                Text(
                    text = buildString {
                        append("确定要删除这条数据吗？\n")
                        append("时间：${SimpleDateFormat("MM-dd HH:mm:ss").format(
                            SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dataToDelete?.timestamp ?: "")
                        )}\n")
                        append("内容：${dataToDelete?.originalData ?: ""}")
                    }
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        showDeleteConfirm?.let { timestamp ->
                            // 使用时间戳删除数据
                            DatabaseUtils.deleteOriginalDataByTimestamp(context, timestamp)
                            originalDataList = DatabaseUtils.getOriginalDataList(context)
                            // 减少记录数量
                            DatabaseUtils.decrementDataCount(context)
                        }
                        // 关闭删除确认对话框
                        showDeleteConfirm = null
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("删除")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirm = null }) {
                    Text("取消")
                }
            }
        )
    }

    // 时间选择对话框
    if (showDatePicker) {
        DateRangePickerDialog(
            onDismiss = { showDatePicker = false },
            onConfirm = { start, end ->
                // 确保结束时间不早于开始时间
                if (start.time <= end.time) {
                    startDate = start
                    endDate = end
                    showDatePicker = false
                }
            }
        )
    }

    // 添加导出选项对话框
    if (showExportOptionsDialog) {
        AlertDialog(
            onDismissRequest = { showExportOptionsDialog = false },
            title = { Text("选择导出格式") },
            text = { 
                Text(
                    if (markedItems.isNotEmpty()) 
                        "请选择要导出标记数据的格式" 
                    else 
                        "请选择要导出数据的格式"
                ) 
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showExportOptionsDialog = false
                        // 根据是否有标记项决定导出内容
                        val dataToExport = if (markedItems.isNotEmpty()) {
                            // 只导出被标记的数据
                            filteredList.filter { data -> 
                                markedItems.contains(data.timestamp)
                            }
                        } else {
                            // 导出所有过滤后的数据
                            filteredList
                        }
                        
                        // 导出完整数据
                        ExportUtils.exportFilteredDataAsText(
                            context = context,
                            dataList = dataToExport,
                            onComplete = { uri ->
                                val shareIntent = Intent().apply {
                                    action = Intent.ACTION_SEND
                                    putExtra(Intent.EXTRA_STREAM, uri)
                                    type = "text/plain"
                                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                }
                                context.startActivity(Intent.createChooser(
                                    shareIntent, 
                                    if (markedItems.isNotEmpty()) "分享标记数据" else "分享全部数据"
                                ))
                            }
                        )
                    }
                ) {
                    Text("完整数据")
                }
            },
            dismissButton = {
                Column {
                    TextButton(
                        onClick = {
                            showExportOptionsDialog = false
                            // 根据是否有标记项决定导出内容
                            val dataToExport = if (markedItems.isNotEmpty()) {
                                // 只导出被标记的数据
                                filteredList.filter { data -> 
                                    markedItems.contains(data.timestamp)
                                }
                            } else {
                                // 导出所有过滤后的数据
                                filteredList
                            }
                            
                            // 导出简化数据为txt文件
                            ExportUtils.exportSimplifiedDataAsTxt(
                                context = context,
                                dataList = dataToExport,
                                onComplete = { uri ->
                                    val shareIntent = Intent().apply {
                                        action = Intent.ACTION_SEND
                                        putExtra(Intent.EXTRA_STREAM, uri)
                                        type = "text/plain"
                                        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                    }
                                    context.startActivity(Intent.createChooser(
                                        shareIntent, 
                                        if (markedItems.isNotEmpty()) "分享标记数据" else "分享数据概览"
                                    ))
                                }
                            )
                        }
                    ) {
                        Text("简化数据(txt)")
                    }
                    
                    Column {
                        TextButton(
                            onClick = {
                                showExportOptionsDialog = false
                                // 根据是否有标记项决定导出内容
                                val dataToExport = if (markedItems.isNotEmpty()) {
                                    // 只导出被标记的数据
                                    filteredList.filter { data -> 
                                        markedItems.contains(data.timestamp)
                                    }
                                } else {
                                    // 导出所有过滤后的数据
                                    filteredList
                                }
                                
                                // 导出简化数据为图片
                                ExportUtils.exportSimplifiedDataAsImage(context, dataToExport, filteredList)
                            }
                        ) {
                            Text("简化数据(图片)")
                        }
                        Text(
                            text = "建议25条数据量以下使用",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(start = 16.dp)
                        )
                    }
                }
            }
        )
    }

    // 添加批量删除确认对话框
    if (showBatchDeleteConfirm) {
        AlertDialog(
            onDismissRequest = { showBatchDeleteConfirm = false },
            title = { Text("批量删除") },
            text = { 
                Text("确定要删除所有标记的 ${markedItems.size} 条数据吗？此操作不可撤销。") 
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 批量删除所有标记的数据
                        markedItems.forEach { timestamp ->
                            DatabaseUtils.deleteOriginalDataByTimestamp(context, timestamp)
                            deleteNote(timestamp)
                            // 减少记录数量
                            DatabaseUtils.decrementDataCount(context)
                        }
                        // 重新加载数据
                        originalDataList = DatabaseUtils.getOriginalDataList(context)
                        // 清空标记
                        markedItems = emptySet()
                        showBatchDeleteConfirm = false
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("删除")
                }
            },
            dismissButton = {
                TextButton(onClick = { showBatchDeleteConfirm = false }) {
                    Text("取消")
                }
            }
        )
    }

    // 修改范围选择对话框，添加跳转功能
    if (showRangeSelectDialog) {
        AlertDialog(
            onDismissRequest = { 
                showRangeSelectDialog = false
                rangeStartText = ""
                rangeEndText = ""
                rangeError = null
            },
            title = { Text("序号操作") },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text("请输入序号（总共 ${filteredList.size} 条）")
                    
                    OutlinedTextField(
                        value = rangeStartText,
                        onValueChange = { rangeStartText = it },
                        label = { Text("起始序号") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    OutlinedTextField(
                        value = rangeEndText,
                        onValueChange = { rangeEndText = it },
                        label = { Text("结束序号 (可选)") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )

                    // 在输入字段下方添加错误提示显示
                    if (rangeError != null) {
                        Text(
                            text = rangeError!!,
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 添加跳转按钮
                        Button(
                            onClick = {
                                try {
                                    val number = rangeStartText.toInt()
                                    
                                    if (number in 1..filteredList.size) {
                                        // 界面序号是倒序显示的，需要转换
                                        val actualIndex = filteredList.size - number
                                        
                                        // 使用协程滚动到指定位置
                                        scope.launch {
                                            listState.animateScrollToItem(actualIndex)
                                            // 高亮显示该项
                                            markedItems = setOf(filteredList[actualIndex].timestamp)
                                        }
                                        
                                        showRangeSelectDialog = false
                                        rangeStartText = ""
                                        rangeEndText = ""
                                        rangeError = null
                                    } else {
                                        // 添加超出范围的错误提示
                                        rangeError = "序号 $number 超出范围 (1-${filteredList.size})"
                                    }
                                } catch (e: NumberFormatException) {
                                    rangeError = "请输入有效的序号"
                                }
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.tertiary
                            )
                        ) {
                            Text("跳转")
                        }
                        
                        // 原有的选择范围功能
                        Button(
                            onClick = {
                                try {
                                    if (rangeEndText.isEmpty()) {
                                        // 如果结束序号为空，则默认选中单条数据
                                        val start = rangeStartText.toInt()
                                        
                                        if (start in 1..filteredList.size) {
                                            val actualIndex = filteredList.size - start
                                            markedItems = markedItems + setOf(filteredList[actualIndex].timestamp)
                                            showRangeSelectDialog = false
                                            rangeStartText = ""
                                            rangeEndText = ""
                                            rangeError = null
                                        } else {
                                            rangeError = "序号 $start 超出范围 (1-${filteredList.size})"
                                        }
                                    } else {
                                        // 原有的范围选择逻辑
                                        val start = rangeStartText.toInt()
                                        val end = rangeEndText.toInt()
                                        
                                        if (start > 0 && end > 0 && start <= end && 
                                            end <= filteredList.size) {
                                            // 注意：界面显示的序号是倒序的，所以需要转换
                                            val actualStart = filteredList.size - end
                                            val actualEnd = filteredList.size - start
                                            
                                            // 选择指定范围的数据
                                            val selectedItems = filteredList
                                                .slice(actualStart..actualEnd)
                                                .map { it.timestamp }
                                            
                                            markedItems = markedItems + selectedItems
                                            
                                            showRangeSelectDialog = false
                                            rangeStartText = ""
                                            rangeEndText = ""
                                            rangeError = null
                                        } else {
                                            rangeError = "序号范围 $start-$end 超出有效范围 (1-${filteredList.size})"
                                        }
                                    }
                                } catch (e: NumberFormatException) {
                                    rangeError = "请输入有效的序号"
                                }
                            }
                        ) {
                            Text("选择")
                        }
                    }
                }
            },
            confirmButton = { /* 将按钮移到 text 区域内，这里留空 */ },
            dismissButton = {
                TextButton(
                    onClick = { 
                        showRangeSelectDialog = false
                        rangeStartText = ""
                        rangeEndText = ""
                        rangeError = null
                    }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun DataItem(
    data: DatabaseUtils.OriginalDataInfo,
    onDelete: () -> Unit,
    index: Int,
    totalCount: Int,
    isMarked: Boolean,
    onToggleMark: () -> Unit,
    note: String?,
    onNoteChange: (String) -> Unit,
    onNoteSave: (String, String) -> Unit,
    onNoteDelete: (String) -> Unit
) {
    val context = LocalContext.current
    
    // 复制到剪贴板的辅助函数
    fun copyToClipboard(text: String, label: String) {
        val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText(label, text)
        clipboardManager.setPrimaryClip(clip)
        Toast.makeText(context, "已复制$label", Toast.LENGTH_SHORT).show()
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .then(
                if (isMarked) {
                    Modifier.border(
                        2.dp,
                        MaterialTheme.colorScheme.error,
                        shape = MaterialTheme.shapes.medium
                    )
                } else Modifier
            )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 添加序号显示
                Text(
                    text = "#${totalCount - index}",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(end = 8.dp)
                )
                
                // 原有的时间和地区显示
                Text(
                    text = "${data.timestamp} ${data.area}",
                    style = MaterialTheme.typography.titleMedium
                )
                
                // 修改标记按钮
                Row {
                    IconButton(onClick = onToggleMark) {
                        Icon(
                            imageVector = if (isMarked) {
                                Icons.Default.Flag
                            } else {
                                Icons.Default.OutlinedFlag
                            },
                            contentDescription = if (isMarked) "取消标记" else "标记为待修改",
                            tint = if (isMarked) {
                                MaterialTheme.colorScheme.error
                            } else {
                                MaterialTheme.colorScheme.onSurface
                            }
                        )
                    }
                    IconButton(onClick = onDelete) {
                        Icon(Icons.Default.Delete, "删除")
                    }
                }
            }
            
            // 标识（如果有）
            if (data.identifier.isNotEmpty()) {
                Text(
                    text = "标识: ${data.identifier}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 原始数据
            Text(
                text = data.originalData,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(8.dp))  // 添加圆角裁剪
                    .clickable { 
                        copyToClipboard(data.originalData, "原始数据") 
                    }
                    .padding(4.dp),  // 内边距使点击区域更明显
                softWrap = true
            )

            // 添加分隔符和箭头
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Divider(
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 8.dp),
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
                )
                Text(
                    text = "👇",
                    style = MaterialTheme.typography.bodyLarge
                )
                Divider(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp),
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
                )
            }

            // 号码明细（解析结果）
            if (data.numberDetails.isNotEmpty()) {
                val allNumberDetails = data.numberDetails.joinToString("\n") { "${it.number}:${it.amount}" }
                
                FlowRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))  // 添加圆角裁剪
                        .clickable { 
                            copyToClipboard(allNumberDetails, "号码明细") 
                        }
                        .padding(4.dp),  // 内边距使点击区域更明显
                    horizontalArrangement = Arrangement.Start
                ) {
                    data.numberDetails.forEach { detail ->
                        Text(
                            text = "${detail.number}:${detail.amount}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier.padding(end = 8.dp, bottom = 4.dp)
                        )
                    }
                }
            }

            // 特殊组合数据（如果有）
            if (data.specialBets.isNotEmpty()) {
                val allSpecialBets = data.specialBets.joinToString("\n") { "${it.type}:${it.amount}" }
                
                FlowRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))  // 添加圆角裁剪
                        .clickable { 
                            copyToClipboard(allSpecialBets, "特殊组合") 
                        }
                        .padding(4.dp),  // 内边距使点击区域更明显
                    horizontalArrangement = Arrangement.Start
                ) {
                    data.specialBets.forEach { specialBet ->
                        Text(
                            text = "${specialBet.type}:${specialBet.amount}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(end = 8.dp, bottom = 4.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 总计信息
            Text(
                text = buildString {
                    val normalAmount = if (data.numberDetails.isNotEmpty()) {
                        data.numberDetails.sumOf { it.amount }
                    } else 0
                    
                    val specialAmount = data.specialAmount
                    val total = normalAmount + specialAmount
                    
                    if (data.numberDetails.isNotEmpty()) {
                        append("特码类:$normalAmount(${data.numberDetails.size}注)\n")
                    }
                    
                    if (specialAmount > 0) {
                        val specialBets = data.originalData.split("\n").filter { line ->
                            line.contains("平特") || line.contains("特肖") || 
                            line.contains("连肖") || line.contains("合肖")
                        }
                        append("平特类:$specialAmount(${specialBets.size}注)\n")
                    }
                    append("总计:$total")
                },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.fillMaxWidth()
            )

            // 如果被标记，显示标记提示
            if (isMarked) {
                Text(
                    text = "⚠️ 已标记",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }

            // 在标记项中添加批注输入框，并处理取消标记时删除批注
            if (isMarked) {
                OutlinedTextField(
                    value = note ?: "",
                    onValueChange = { newNote ->
                        onNoteChange(newNote)
                        if (newNote.isNotEmpty()) {
                            onNoteSave(data.timestamp, newNote)
                        } else {
                            onNoteDelete(data.timestamp)
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    label = { Text("添加批注") },
                    placeholder = { Text("记录需要修改的内容...") },
                    maxLines = 3
                )
            } else if (!note.isNullOrEmpty()) {
                Text(
                    text = "批注: $note",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

@Composable
private fun DateRangePickerDialog(
    onDismiss: () -> Unit,
    onConfirm: (Date, Date) -> Unit
) {
    // 获取当前时间并格式化
    val currentTime = Calendar.getInstance()
    val dateFormat = SimpleDateFormat("MM-dd HH:mm:ss", Locale.getDefault())
    
    // 设置默认的开始时间为当前时间前5分钟
    val defaultStartTime = Calendar.getInstance().apply {
        add(Calendar.MINUTE, -5)  // 减去5分钟
    }
    
    // 初始化文本框的值为当前时间
    var startDateText by remember { 
        mutableStateOf(dateFormat.format(defaultStartTime.time).substring(0)) 
    }
    var endDateText by remember { 
        mutableStateOf(dateFormat.format(currentTime.time).substring(0)) 
    }
    var showError by remember { mutableStateOf(false) }
    val fullDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择时间范围") },
        text = {
            Column {
                OutlinedTextField(
                    value = startDateText,
                    onValueChange = { 
                        startDateText = it
                        showError = false 
                    },
                    label = { Text("开始时间") },
                    placeholder = { Text("MM-dd HH:mm:ss") },
                    isError = showError
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = endDateText,
                    onValueChange = { 
                        endDateText = it
                        showError = false 
                    },
                    label = { Text("结束时间") },
                    placeholder = { Text("MM-dd HH:mm:ss") },
                    isError = showError
                )
                
                if (showError) {
                    Text(
                        text = "请输入有效的时间格式",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    try {
                        val currentYear = Calendar.getInstance().get(Calendar.YEAR)
                        val start = fullDateFormat.parse("$currentYear-$startDateText")
                        val end = fullDateFormat.parse("$currentYear-$endDateText")
                        
                        if (start != null && end != null) {
                            onConfirm(start, end)
                        } else {
                            showError = true
                        }
                    } catch (e: Exception) {
                        // Log.e("DataManageScreen", "时间解析错误: ${e.message}")
                        showError = true
                    }
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

// 修改简化数据导出函数，添加 filteredList 参数
private fun exportSimplifiedData(
    context: Context,
    dataList: List<DatabaseUtils.OriginalDataInfo>,
    filteredList: List<DatabaseUtils.OriginalDataInfo>
) {
    val content = buildString {
        // 按标签分组统计记录数量
        val tagGroups = dataList.groupBy { it.identifier.ifEmpty { "无标签" } }
        
        appendLine("=== 数据概览 ===")
        // 添加总记录以及标签分布信息
        appendLine("总记录: ${dataList.size} (${tagGroups.map { "${it.key}:${it.value.size}" }.joinToString(", ")})")
        appendLine("------------------------")
        
        // 首先按时间戳排序，确保最旧的在前面
        val sortedDataList = dataList.sortedBy { it.timestamp }
        
        // 然后按顺序生成序号
        sortedDataList.forEachIndexed { index, data ->
            // 计算总金额
            val normalTotal = data.numberDetails.sumOf { it.amount }
            val specialTotal = data.specialBets.sumOf { it.amount }
            val total = normalTotal + specialTotal
            
            // 序号从1开始，最旧的为#1
            val displayIndex = index + 1
            
            // 添加序号、标签和总金额
            val tagDisplay = if (data.identifier.isNotEmpty()) "[${data.identifier}]" else ""
            appendLine("${data.area} $tagDisplay - #$displayIndex - $total A")
        }
        
        // 添加总计
        val grandTotal = dataList.sumOf { data -> 
            data.numberDetails.sumOf { it.amount } + data.specialBets.sumOf { it.amount }
        }
        appendLine("\n=== 总计 ===")
        appendLine("    $grandTotal A")
    }
    
    // 创建分享 Intent
    val shareIntent = Intent().apply {
        action = Intent.ACTION_SEND
        type = "text/plain"
        putExtra(Intent.EXTRA_TEXT, content)
    }
    
    // 启动分享选择器
    context.startActivity(Intent.createChooser(
        shareIntent, 
        if (dataList.size < filteredList.size) "分享标记数据概览" else "分享数据概览"
    ))
} 