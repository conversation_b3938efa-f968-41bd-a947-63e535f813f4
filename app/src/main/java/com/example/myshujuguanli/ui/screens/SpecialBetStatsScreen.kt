package com.example.myshujuguanli.ui.screens

import android.content.Context
import android.content.Intent
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myshujuguanli.utils.BettingOdds
import com.example.myshujuguanli.utils.DatabaseUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.NumberFormat
import java.util.Locale


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SpecialBetStatsScreen(
    onNavigateBack: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    
    // 格式化器，用于格式化数字
    val numberFormat = remember { NumberFormat.getNumberInstance(Locale.getDefault()) }
    
    // 选择模式状态
    var isSelectionMode by remember { mutableStateOf(false) }
    var selectedItems by remember { mutableStateOf(emptySet<String>()) }
    
    // 过滤设置
    var showMacau by remember { mutableStateOf(true) }
    var showHongKong by remember { mutableStateOf(true) }
    
    // 保存查询结果的状态
    var statsResults by remember { mutableStateOf<List<SpecialBetStat>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var totalAmount by remember { mutableIntStateOf(0) }
    var macauTotalAmount by remember { mutableIntStateOf(0) }
    var hongKongTotalAmount by remember { mutableIntStateOf(0) }
    
    // 当界面首次组合时或过滤条件变化时加载数据
    LaunchedEffect(showMacau, showHongKong) {
        isLoading = true
        // 重置选择状态
        isSelectionMode = false
        selectedItems = emptySet()
        loadSpecialBetStats(context, coroutineScope, showMacau, showHongKong) { results, totals ->
            statsResults = results
            totalAmount = totals.total
            macauTotalAmount = totals.macau
            hongKongTotalAmount = totals.hongKong
            isLoading = false
        }
    }
    
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部栏
            TopAppBar(
                title = {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = if (isSelectionMode) "选择分享 (${selectedItems.size})" else "平特类统计",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black
                        )
                    }
                },
                navigationIcon = {
                    IconButton(onClick = {
                        if (isSelectionMode) {
                            // 退出选择模式
                            isSelectionMode = false
                            selectedItems = emptySet()
                        } else {
                            onNavigateBack()
                        }
                    }) {
                        Icon(
                            imageVector = if (isSelectionMode) Icons.Default.Close else Icons.Default.ArrowBack,
                            contentDescription = if (isSelectionMode) "关闭选择" else "返回",
                            tint = Color.Black
                        )
                    }
                },
                actions = {
                    if (isSelectionMode) {
                        // 全选/取消全选按钮
                        TextButton(onClick = {
                            selectedItems = if (selectedItems.size == statsResults.size) {
                                emptySet()
                            } else {
                                statsResults.map { it.betType }.toSet()
                            }
                        }) {
                            Text(if (selectedItems.size == statsResults.size) "全不选" else "全选")
                        }

                        // 确认分享按钮
                        IconButton(
                            onClick = {
                                val itemsToShare = statsResults.filter { it.betType in selectedItems }
                                shareSpecialBetStats(context, itemsToShare, totalAmount, macauTotalAmount, hongKongTotalAmount, showMacau, showHongKong)
                                isSelectionMode = false
                                selectedItems = emptySet()
                            },
                            enabled = selectedItems.isNotEmpty()
                        ) {
                            Icon(
                                imageVector = Icons.Default.Share,
                                contentDescription = "分享",
                                tint = if (selectedItems.isNotEmpty()) Color.Black else Color.Gray
                            )
                        }
                    } else {
                        // 进入选择模式的分享按钮
                        IconButton(onClick = { isSelectionMode = true }) {
                            Icon(
                                imageVector = Icons.Default.Share,
                                contentDescription = "分享统计",
                                tint = Color.Black
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 1f)
                )
            )
            
            // 汇总统计区域 - 整合筛选按钮
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 4.dp
                ),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // 金额标题行和筛选器
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.Top // 调整为顶部对齐
                    ) {
                        // 左侧金额信息
                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            // 总数 - 直接显示文本
                            Text(
                                text = "总数: ${numberFormat.format(totalAmount)}",
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                            
                            // 减少间距
                            Spacer(modifier = Modifier.height(6.dp))
                            
                            // 澳门 - 直接显示文本
                            Text(
                                text = "澳门: ${numberFormat.format(macauTotalAmount)}",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            
                            // 减少间距
                            Spacer(modifier = Modifier.height(6.dp))
                            
                            // 香港 - 直接显示文本
                            Text(
                                text = "香港: ${numberFormat.format(hongKongTotalAmount)}",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        }
                        
                        // 右侧筛选区 - 调整垂直间距
                        Column(
                            horizontalAlignment = Alignment.End,
                            verticalArrangement = Arrangement.spacedBy(18.dp) // 增加垂直间距
                        ) {
                            FilterChip(
                                showMacau = showMacau,
                                label = "澳门",
                                onToggle = { showMacau = it },
                                smallSize = true
                            )
                            
                            FilterChip(
                                showMacau = showHongKong,
                                label = "香港",
                                onToggle = { showHongKong = it },
                                smallSize = true
                            )
                        }
                    }
                }
            }
            
            // 加载指示器
            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            // 将表头和数据列表合并到一个卡片中，确保完全一致的宽度
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .weight(1f),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 2.dp
                )
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 表头部分
                    Surface(
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "类型",
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.weight(2.5f),
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.primary
                            )
                            Text(
                                text = "注额",
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.weight(1f),
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.primary
                            )
                            Text(
                                text = "赔率",
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.weight(1f),
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                    
                    // 数据列表部分
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        items(statsResults, key = { it.betType }) { stat ->
                            BetListItem(
                                betType = stat.betType,
                                amount = stat.totalAmount,
                                numberFormat = numberFormat,
                                isSelectionMode = isSelectionMode,
                                isSelected = stat.betType in selectedItems,
                                onItemClick = {
                                    if (isSelectionMode) {
                                        selectedItems = if (stat.betType in selectedItems) {
                                            selectedItems - stat.betType
                                        } else {
                                            selectedItems + stat.betType
                                        }
                                    }
                                }
                            )
                            
                            if (statsResults.indexOf(stat) < statsResults.size - 1) {
                                Divider(
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.1f),
                                    modifier = Modifier.padding(horizontal = 16.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun FilterChip(
    showMacau: Boolean,
    label: String,
    onToggle: (Boolean) -> Unit,
    smallSize: Boolean = false
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (showMacau) 
            MaterialTheme.colorScheme.primary.copy(alpha = 0.1f) 
        else 
            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.1f),
        animationSpec = spring(stiffness = Spring.StiffnessLow),
        label = "bgColor"
    )
    
    val borderColor by animateColorAsState(
        targetValue = if (showMacau) 
            MaterialTheme.colorScheme.primary
        else 
            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f),
        animationSpec = spring(stiffness = Spring.StiffnessLow),
        label = "borderColor"
    )
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(24.dp))
            .background(backgroundColor)
            .border(1.dp, borderColor, RoundedCornerShape(24.dp))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = rememberRipple(bounded = true, color = MaterialTheme.colorScheme.primary),
                onClick = { onToggle(!showMacau) }
            )
            .padding(
                horizontal = if (smallSize) 8.dp else 12.dp, 
                vertical = if (smallSize) 6.dp else 8.dp
            ),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (showMacau) {
                Box(
                    modifier = Modifier
                        .size(if (smallSize) 16.dp else 20.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.size(if (smallSize) 12.dp else 16.dp)
                    )
                }
                Spacer(modifier = Modifier.width(4.dp))
            }
            
            Text(
                text = label,
                color = if (showMacau) 
                    MaterialTheme.colorScheme.onSurface 
                else 
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                fontWeight = if (showMacau) FontWeight.Bold else FontWeight.Normal,
                fontSize = if (smallSize) 12.sp else 14.sp
            )
        }
    }
}

@Composable
fun BetListItem(
    betType: String,
    amount: Int,
    numberFormat: NumberFormat,
    isSelectionMode: Boolean,
    isSelected: Boolean,
    onItemClick: () -> Unit
) {
    val isLianXiao = betType.contains("连肖")
    val itemElevation by animateDpAsState(
        targetValue = if (isLianXiao) 4.dp else 0.dp,
        animationSpec = spring(stiffness = Spring.StiffnessLow),
        label = "elevation"
    )
    
    // 获取赔率
    val odds = getOddsForType(betType)
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                enabled = isSelectionMode,
                onClick = onItemClick,
                interactionSource = remember { MutableInteractionSource() },
                indication = rememberRipple()
            )
            .padding(vertical = 2.dp),
        tonalElevation = itemElevation,
        color = if (isLianXiao) MaterialTheme.colorScheme.surface.copy(alpha = 0.7f) else MaterialTheme.colorScheme.surface
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (isSelectionMode) {
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = { onItemClick() },
                    modifier = Modifier.size(24.dp)
                )
                Spacer(Modifier.width(16.dp))
            }
            // 类型名称
            Text(
                text = betType,
                modifier = Modifier.weight(2.5f),
                fontWeight = if (isLianXiao) FontWeight.Bold else FontWeight.Normal,
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center
            )
            
            // 总金额
            Text(
                text = numberFormat.format(amount),
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            // 赔率
            Text(
                text = odds.toString(),
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

// 辅助数据类
data class SpecialBetStat(
    val betType: String,
    val totalAmount: Int,
    val odds: Int,
    val betCount: Int,
    val area: String // 地区：澳门/香港
)

// 总计数据类
data class TotalStats(
    val total: Int,
    val macau: Int,
    val hongKong: Int
)

// 获取主类型对应的赔率
private fun getOddsForType(fullType: String): Double {
    val mainType = when {
        fullType.startsWith("平特") -> "平特"
        fullType.startsWith("特肖") -> "特肖"
        fullType.startsWith("2连") -> "2连肖"
        fullType.startsWith("3连") -> "3连肖"
        fullType.startsWith("4连") -> "4连肖"
        fullType.startsWith("5连") -> "5连肖"
        fullType.startsWith("二中二") -> "二中二"
        fullType.startsWith("三中二") -> "三中二"
        fullType.startsWith("三中三") -> "三中三"
        fullType.startsWith("平特尾") -> "平特尾"
        fullType.startsWith("五不中") -> "五不中"
        fullType.startsWith("六不中") -> "六不中"
        fullType.startsWith("七不中") -> "七不中"
        fullType.startsWith("八不中") -> "八不中"
        fullType.startsWith("九不中") -> "九不中"
        fullType.startsWith("十不中") -> "十不中"
        fullType.startsWith("十一不中") -> "十一不中"
        fullType.startsWith("包红") || fullType.startsWith("包绿") || fullType.startsWith("包蓝") -> "包波色"
        fullType.contains("包红单数") || fullType.contains("包绿单数") || fullType.contains("包蓝单数") ||
        fullType.contains("包红双数") || fullType.contains("包绿双数") || fullType.contains("包蓝双数") -> "包波色双单"
        fullType.startsWith("包单") || fullType.startsWith("包双") ||
        fullType.startsWith("包合单") || fullType.startsWith("包合双") -> "包双单合双单"
        else -> fullType
    }
    
    return BettingOdds.getCurrentSettings().normalOdds[mainType] ?: 0.0
}

// 加载特殊投注统计数据
private fun loadSpecialBetStats(
    context: Context,
    coroutineScope: CoroutineScope,
    showMacau: Boolean,
    showHongKong: Boolean,
    onResultsLoaded: (List<SpecialBetStat>, TotalStats) -> Unit
) {
    coroutineScope.launch {
        val results = withContext(Dispatchers.IO) {
            // 获取所有原始数据
            val originalDataList = DatabaseUtils.getOriginalDataList(context)
            
            // 从原始数据中提取特殊投注信息，保留完整类型
            val specialBetsMap = mutableMapOf<String, MutableList<Triple<String, Int, String>>>()
            var totalMacau = 0
            var totalHongKong = 0
            
            // 遍历原始数据收集特殊投注
            originalDataList.forEach { dataInfo ->
                // 根据筛选条件过滤
                if ((dataInfo.area == "澳门" && !showMacau) || 
                    (dataInfo.area == "香港" && !showHongKong)) {
                    return@forEach
                }
                
                dataInfo.specialBets.forEach { specialBet ->
                    val betType = specialBet.type // 使用完整类型
                    val amount = specialBet.amount
                    val area = dataInfo.area
                    
                    // 更新总计
                    if (area == "澳门") {
                        totalMacau += amount
                    } else if (area == "香港") {
                        totalHongKong += amount
                    }
                    
                    // 生成键值：类型+地区
                    val key = betType
                    
                    if (!specialBetsMap.containsKey(key)) {
                        specialBetsMap[key] = mutableListOf()
                    }
                    
                    specialBetsMap[key]?.add(Triple(betType, amount, area))
                }
            }
            
            // 总金额统计
            val totalAmount = totalMacau + totalHongKong
            val totalStats = TotalStats(
                total = totalAmount,
                macau = totalMacau,
                hongKong = totalHongKong
            )
            
            // 存储特殊类型的映射，用于查找平特对应的赔率和金额
            specialBetsMap.filter { it.key.startsWith("平特_") }
                .mapValues { it.value.sumOf { triple -> triple.second } }
            
            // 生成最终统计结果
            val statsList = specialBetsMap.map { (betType, bets) ->
                val betAmount = bets.sumOf { it.second }
                val odds = getOddsForType(betType)
                
                SpecialBetStat(
                    betType = betType,
                    totalAmount = betAmount,
                    odds = odds.toInt(),
                    betCount = bets.size,
                    area = bets.map { it.third }.distinct().joinToString("/")
                )
            }.sortedByDescending { it.totalAmount } // 按总金额降序排序
            
            Pair(statsList, totalStats)
        }
        
        onResultsLoaded(results.first, results.second)
    }
}

private fun shareSpecialBetStats(
    context: Context,
    stats: List<SpecialBetStat>,
    total: Int,
    macauTotal: Int,
    hongKongTotal: Int,
    showMacau: Boolean,
    showHongKong: Boolean
) {
    val numberFormat = NumberFormat.getNumberInstance(Locale.getDefault())

    val shareText = buildString {
        // 根据筛选动态生成标题
        val title = when {
            showMacau && showHongKong -> "全部"
            showMacau -> "澳门"
            showHongKong -> "香港"
            else -> "无"
        }
        append("$title \n")
        append("--------------------\n")

        // 拼接投注详情 - 修改格式：去掉下划线，冒号改为"各"
        var actualTotal = 0
        stats.forEach { stat ->
            val formattedBetType = stat.betType.replace("_", "")
            append("${formattedBetType}各${numberFormat.format(stat.totalAmount)}\n")
            actualTotal += stat.totalAmount
        }

        append("--------------------\n")
        // 根据实际导出的项目计算总计
        append("总计: ${numberFormat.format(actualTotal)}\n")
    }

    val sendIntent: Intent = Intent().apply {
        action = Intent.ACTION_SEND
        putExtra(Intent.EXTRA_TEXT, shareText.trim())
        type = "text/plain"
    }

    val shareIntent = Intent.createChooser(sendIntent, "分享统计数据")
    context.startActivity(shareIntent)
} 