package com.example.myshujuguanli.ui.screens

import android.content.Context
import android.util.Log
import android.view.Gravity
import android.widget.Toast
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myshujuguanli.MainActivity
import com.example.myshujuguanli.utils.BettingOdds
import com.example.myshujuguanli.utils.DataParser
import com.example.myshujuguanli.utils.DatabaseHelper
import com.example.myshujuguanli.utils.DatabaseUtils
import com.example.myshujuguanli.utils.ExportUtils
import com.example.myshujuguanli.utils.NumberData
import com.example.myshujuguanli.utils.ProfitStats
import com.example.myshujuguanli.utils.SecurityUtils
import com.example.myshujuguanli.utils.SoundUtils
import com.example.myshujuguanli.utils.VibrationUtils
import com.example.myshujuguanli.utils.ZodiacUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONArray
import java.net.URL
import kotlin.math.abs
import kotlin.math.roundToInt

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ManagementScreen(
    isTabletMode: Boolean = false, //是否为平板模式
    mainActivity: MainActivity, //主活动
    onNavigateToDataManage: () -> Unit, //导航到数据管理    
    modifier: Modifier = Modifier //修饰符
) {
    var showActivationDialog by remember { mutableStateOf(false) } //是否显示激活对话框
    var activationError by remember { mutableStateOf<String?>(null) } //激活错误
    var currentStatus by remember { mutableStateOf<SecurityUtils.StatusInfo?>(null) } //当前状态

    val context = LocalContext.current //当前上下文
    
    // 获取屏幕尺寸
    val displayMetrics = context.resources.displayMetrics
    val screenWidth = displayMetrics.widthPixels.toFloat()
    val screenHeight = displayMetrics.heightPixels.toFloat()
    
    // 在初始化时就加载保存的按钮位置
    val prefs = context.getSharedPreferences("button_prefs", Context.MODE_PRIVATE)
    
    // 保存按钮相对位置的百分比，而不是绝对像素值
    val savedXPercent = prefs.getFloat("button_x_percent", 0.05f) // 默认在左上角，x为屏幕宽度的5%
    val savedYPercent = prefs.getFloat("button_y_percent", 0.1f)  // y为屏幕高度的10%
    
    // 根据当前屏幕尺寸和保存的百分比计算实际位置
    val initialX = savedXPercent * screenWidth
    val initialY = savedYPercent * screenHeight
    
    // 确保位置在屏幕范围内
    val boundedX = initialX.coerceIn(0f, screenWidth - 30f)
    val boundedY = initialY.coerceIn(0f, screenHeight - 30f)
    
    // 添加悬浮按钮位置状态 - 使用加载的位置初始化
    var buttonPosition by remember { mutableStateOf(Offset(boundedX, boundedY)) }
    var isDragging by remember { mutableStateOf(false) } // 是否正在拖动
    
    // 添加位置保存函数 - 现在保存相对位置百分比
    fun saveButtonPosition(position: Offset) {
        val prefs = context.getSharedPreferences("button_prefs", Context.MODE_PRIVATE)
        // 计算相对于屏幕尺寸的百分比
        val xPercent = position.x / screenWidth
        val yPercent = position.y / screenHeight
        
        prefs.edit()
            .putFloat("button_x_percent", xPercent)
            .putFloat("button_y_percent", yPercent)
            .apply()
    }
    
    // 检查特殊输入的函数 - 移到使用前定义
    fun checkSpecialInput(input: String): List<Int> {
        // 检查是否为单独的关键字
        if (input.isEmpty()) return emptyList()
        
        // 检查是否是"飞"字开头的输入
        if (input.startsWith("飞")) {
            val numbers = input.substring(1).trim().split(Regex("\\s+"))
                .mapNotNull { numStr ->
                    when {
                        numStr.startsWith("0") && numStr.length == 2 -> numStr.substring(1).toIntOrNull()
                        else -> numStr.toIntOrNull()
                    }
                }
                .filter { it in 1..49 }
                .distinct()
            
            if (numbers.isNotEmpty()) {
                return numbers
            }
        }
        
        // 检查是否是生肖
        val zodiacMap = ZodiacUtils.getZodiacMappings()
        
        // 先检查是否是完整的单个关键字
        if (input in zodiacMap) {
            return zodiacMap[input] ?: emptyList()
        }
        
        // 检查是否是颜色
        when (input) {
            "红", "红波" -> return DataParser.RED_NUMBERS.toList().sorted()
            "蓝", "蓝波" -> return DataParser.BLUE_NUMBERS.toList().sorted()
            "绿", "绿波" -> return DataParser.GREEN_NUMBERS.toList().sorted()
        }
        
        // 检查是否是单双大小
        val numberTypes = mapOf(
            "单" to (1..49).filter { it % 2 == 1 },
            "双" to (1..49).filter { it % 2 == 0 },
            "大" to (25..49).toList(),
            "小" to (1..24).toList(),
            "大单" to (25..49).filter { it % 2 == 1 },
            "大双" to (25..49).filter { it % 2 == 0 },
            "小单" to (1..24).filter { it % 2 == 1 },
            "小双" to (1..24).filter { it % 2 == 0 },
            "合单" to (1..49).filter { (it / 10 + it % 10) % 2 == 1 },
            "合双" to (1..49).filter { (it / 10 + it % 10) % 2 == 0 }
        )
        
        if (input in numberTypes) {
            return numberTypes[input] ?: emptyList()
        }
        
        // 智能检查波色与单双大小的组合
        val colorMap = mapOf(
            "红" to DataParser.RED_NUMBERS.toList(),
            "蓝" to DataParser.BLUE_NUMBERS.toList(),
            "绿" to DataParser.GREEN_NUMBERS.toList()
        )
        
        val propertyFilters = mapOf(
            "单" to { n: Int -> n % 2 == 1 },
            "双" to { n: Int -> n % 2 == 0 },
            "大" to { n: Int -> n >= 25 },
            "小" to { n: Int -> n < 25 }
        )
        
        // 检查是否是波色+属性组合，如"红单"、"蓝小"等
        for ((color, numbers) in colorMap) {
            // 检查单一属性组合，如"红单"、"蓝大"
            for ((property, filter) in propertyFilters) {
                val combo = "$color$property"
                if (input == combo) {
                    return numbers.filter(filter).sorted()
                }
            }
            
            // 检查两个属性组合，如"红大单"、"蓝小双"
            for ((prop1, filter1) in propertyFilters) {
                for ((prop2, filter2) in propertyFilters) {
                    // 跳过无意义的组合，如"单双"、"大小"
                    if ((prop1 == "单" && prop2 == "双") || 
                        (prop1 == "双" && prop2 == "单") ||
                        (prop1 == "大" && prop2 == "小") || 
                        (prop1 == "小" && prop2 == "大")) {
                        continue
                    }
                    
                    val combo = "$color$prop1$prop2"
                    if (input == combo) {
                        return numbers.filter { filter1(it) && filter2(it) }.sorted()
                    }
                }
            }
        }
        
        // 检查是否是多个生肖的组合
        val recognizedZodiacs = mutableListOf<String>()
        var tempInput = input
        
        // 限制最多解析12个生肖
        val maxZodiacs = 12
        
        // 遍历输入，尝试识别生肖
        while (tempInput.isNotEmpty() && recognizedZodiacs.size < maxZodiacs) {
            var recognized = false
            
            // 尝试匹配单个字符的生肖
            val firstChar = tempInput.substring(0, 1)
            if (firstChar in zodiacMap) {
                recognizedZodiacs.add(firstChar)
                tempInput = tempInput.substring(1)
                recognized = true
            }
            
            // 如果没有识别到生肖，跳过当前字符
            if (!recognized) {
                tempInput = tempInput.substring(1)
            }
        }
        
        // 如果识别到了多个生肖，返回所有对应的号码
        if (recognizedZodiacs.isNotEmpty()) {
            val result = mutableListOf<Int>()
            recognizedZodiacs.forEach { zodiac ->
                zodiacMap[zodiac]?.let { numbers ->
                    result.addAll(numbers)
                }
            }
            return result.distinct().sorted()
        }
        
        return emptyList()
    }
    
    var maxLoss by remember { mutableStateOf("") } //最大亏损
    var lotteryNumber by remember { mutableStateOf("") } //开奖号码
    var showMacau by remember { mutableStateOf(true) } //澳门显示
    var showHongKong by remember { mutableStateOf(true) } //香港显示
    var numberData by remember { mutableStateOf<Map<Int, NumberData>>(emptyMap()) } //号码数据
    var profits by remember { mutableStateOf<Map<Int, Int>>(emptyMap()) } //盈利
    var stats by remember { mutableStateOf<ProfitStats?>(null) } //统计信息
    var selectedNumbers by remember { mutableStateOf<Set<Int>>(emptySet()) } //选中的号码
    var showNumberSelection by remember { mutableStateOf(false) } //显示号码选择
    var specialBetsTotal by remember { mutableStateOf(0) } //总注额
    var macauSpecialTotal by remember { mutableStateOf(0) }//澳门平特类
    var hkSpecialTotal by remember { mutableStateOf(0) }//香港平特类
    var showConfirmDialog by remember { mutableStateOf(false) } //显示确认对话框
    var calculationStatus by remember { mutableStateOf("") } //计算状态
    var isStatsCardExpanded by remember { mutableStateOf(true) } //统计卡片是否展开
    var showLotteryResult by remember { mutableStateOf(false) } //显示开奖结果
    var lotteryResult by remember { mutableStateOf("") } //开奖结果
    val scope = rememberCoroutineScope() //协程作用域
    val scrollState = rememberScrollState()
    
    // 添加标识相关状态
    var availableIdentifiers by remember { mutableStateOf<List<String>>(emptyList()) } // 可用的所有标识
    var selectedIdentifiers by remember { mutableStateOf<List<String>>(emptyList()) } // 已选择的标识
    var showIdentifierSelection by remember { mutableStateOf(false) } // 是否显示标识选择对话框
    
    // 添加特殊号码选择状态
    val selectedSpecialNumbers = remember { mutableStateListOf<Int>() }
    
    // 其他状态变量
    var isDialogOpen by remember { mutableStateOf(false) }
    var betAmount by remember { mutableStateOf("100") }
    
    // 确保正确定义betAmounts
    val betAmounts = remember { mutableStateMapOf<String, Int>() }

    // 获取设置中的特码赔率，在顶层获取以避免重复计算
    val specialNumberOdds = BettingOdds.getCurrentSettings().normalOdds["特码"]?.toDouble() ?: 47.0

    // 添加焦点管理
    val focusManager = LocalFocusManager.current
    var showCursor by remember { mutableStateOf(false) }
    
    // 添加返水设置相关状态
    var showRebateDialog by remember { mutableStateOf(false) }
    
    // 从 SharedPreferences 中读取返水设置
    val sharedPrefs = context.getSharedPreferences("settings", Context.MODE_PRIVATE)
    var rebateEnabled by remember { 
        mutableStateOf(sharedPrefs.getBoolean("rebate_enabled", false)) 
    }
    var rebateValue by remember { 
        mutableStateOf(sharedPrefs.getFloat("rebate_value", 0f).toString()) 
    }

    // 返水计算函数
    fun calculateRebate(amount: Int): Int {
        val rebateRateValue = rebateValue.toFloatOrNull() ?: 0f
        return if (rebateEnabled) (amount * (rebateRateValue / 100)).toInt() else 0
    }

    // 定义重置函数
    fun resetAllStates() {
        numberData = emptyMap()
        profits = emptyMap()
        stats = null
        lotteryNumber = ""
        maxLoss = ""
        selectedNumbers = emptySet()
        specialBetsTotal = 0
        macauSpecialTotal = 0
        hkSpecialTotal = 0
    }

    // 在类的顶部添加格式化函数
    fun formatNumberDisplay(number: Int): String {
        return if (number < 10) "0$number" else number.toString()
    }

    // 提取常量
    val textFieldModifier = Modifier
        .width(180.dp)
        .height(60.dp)

    // 提取计算盈利的函数
    fun calculateProfit(number: Int, numberData: NumberData): Int {
        return when {
            showMacau && showHongKong -> DatabaseUtils.calculateNumberProfit(context, number, maxLoss.toIntOrNull() ?: 0)
            showMacau -> {
                val macauAmount = numberData.macauAmount
                val totalMacauAmount = stats?.macauAmount ?: 0
                totalMacauAmount - (macauAmount * specialNumberOdds).toInt()
            }
            showHongKong -> {
                val hongKongAmount = numberData.hongKongAmount
                val totalHongKongAmount = stats?.hongKongAmount ?: 0
                totalHongKongAmount - (hongKongAmount * specialNumberOdds).toInt()
            }
            else -> 0
        }
    }

    // 添加获取可用标识的函数
    fun loadAvailableIdentifiers() {
        scope.launch {
            withContext(Dispatchers.IO) {
                try {
                    val dbHelper = DatabaseHelper(context)
                    val db = dbHelper.readableDatabase
                    
                    // 先查询具有明确标签的数据
                    val cursor = db.rawQuery(
                        "SELECT DISTINCT 标识 FROM ${DatabaseHelper.TABLE_NAME} WHERE 标识 IS NOT NULL AND 标识 != ''",
                        null
                    )
                    
                    val identifiers = mutableListOf<String>()
                    cursor.use {
                        while (it.moveToNext()) {
                            val identifier = it.getString(0)
                            identifiers.add(identifier)
                        }
                    }
                    
                    // 检查是否存在空标签数据
                    val emptyTagCursor = db.rawQuery(
                        "SELECT COUNT(*) FROM ${DatabaseHelper.TABLE_NAME} WHERE 标识 IS NULL OR 标识 = ''",
                        null
                    )
                    
                    if (emptyTagCursor.moveToFirst() && emptyTagCursor.getInt(0) > 0) {
                        // 添加一个特殊的"空标签"选项
                        identifiers.add("空标签")
                    }
                    emptyTagCursor.close()
                    
                    availableIdentifiers = identifiers
                    
                    // 如果没有选择任何标识，当前默认为空列表，即获取全部数据
                    // 不再自动选择所有标识
                    
                    db.close()
                } catch (e: Exception) {
                    Log.e("ManagementScreen", "获取标签失败", e)
                }
            }
        }
    }
    
    // 在组件首次加载时获取可用标识
    LaunchedEffect(Unit) {
        loadAvailableIdentifiers()
    }
    
    LaunchedEffect(showMacau, showHongKong, selectedIdentifiers) {
        // 确保生肖数据已加载
        ZodiacUtils.loadSavedMappings(context)
        
        // 处理标签选择逻辑
        val finalSelectedIdentifiers = if (selectedIdentifiers.isEmpty()) {
            // 选择为空，不应用过滤
            null
        } else if (selectedIdentifiers.contains("空标签")) {
            // 包含空标签
            val nonEmptyTags = selectedIdentifiers.filter { it != "空标签" }
            if (nonEmptyTags.isEmpty()) {
                // 只选择了空标签
                emptyList()  // 传递空列表表示只查询空标签
            } else {
                // 选择了空标签和其他标签
                nonEmptyTags.toMutableList().apply { add("") }  // 添加一个空字符串表示空标签
            }
        } else {
            selectedIdentifiers // 正常标签列表
        }
        
        // 获取每个号码的数据
        numberData = (1..49).associateWith { 
            DatabaseUtils.getNumberStats(
                context, 
                it, 
                showMacau, 
                showHongKong,
                finalSelectedIdentifiers // 传递处理后的标签列表
            ) 
        }
        
        // 获取特殊注额数据 - 根据地区切换获取
        specialBetsTotal = 0
        macauSpecialTotal = 0
        hkSpecialTotal = 0
        
        if (showMacau && showHongKong) {
            // 显示全部地区
            specialBetsTotal = DatabaseUtils.getSpecialBetsTotal(context, finalSelectedIdentifiers)
            macauSpecialTotal = DatabaseUtils.getMacauSpecialBetsTotal(context, finalSelectedIdentifiers)
            hkSpecialTotal = DatabaseUtils.getHongKongSpecialBetsTotal(context, finalSelectedIdentifiers)
        } else if (showMacau) {
            // 只显示澳门
            macauSpecialTotal = DatabaseUtils.getMacauSpecialBetsTotal(context, finalSelectedIdentifiers)
            specialBetsTotal = macauSpecialTotal
        } else if (showHongKong) {
            // 只显示香港
            hkSpecialTotal = DatabaseUtils.getHongKongSpecialBetsTotal(context, finalSelectedIdentifiers)
            specialBetsTotal = hkSpecialTotal
        }

        // 计算统计信息 - 需要传入当前显示的标签状态
        // 由于calculateProfitStats已经基于相同的getNumberStats查询构建了数据
        // 所以这里的数据应该是一致的
        stats = DatabaseUtils.calculateProfitStats(
            context, 
            0, 
            showMacau, 
            showHongKong, 
            finalSelectedIdentifiers
        ).also { currentStats ->
            // 计算号码的盈利，确保与calculateProfitStats中使用相同的计算逻辑
            profits = numberData.mapValues { (number, data) ->
                when {
                    showMacau && showHongKong -> {
                        val totalAmount = currentStats.totalAmount
                        val zongshu = totalAmount + macauSpecialTotal + hkSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        val profit = if (data.totalAmount > 0) {
                            (totalAmount - rebateAmount - (data.totalAmount * specialNumberOdds)).toInt()
                        } else totalAmount - rebateAmount
                        profit
                    }
                    showMacau -> {
                        val macauTotal = currentStats.macauAmount
                        val zongshu = macauTotal + macauSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        val profit = if (data.macauAmount > 0) {
                            (macauTotal - rebateAmount - (data.macauAmount * specialNumberOdds)).toInt()
                        } else macauTotal - rebateAmount
                        profit
                    }
                    showHongKong -> {
                        val hongKongTotal = currentStats.hongKongAmount
                        val zongshu = hongKongTotal + hkSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        val profit = if (data.hongKongAmount > 0) {
                            (hongKongTotal - rebateAmount - (data.hongKongAmount * specialNumberOdds)).toInt()
                        } else hongKongTotal - rebateAmount
                        profit
                    }
                    else -> 0
                }
            }
        }

        // 如果已经有最大亏损值和调整建议则自动重新计算
        if (maxLoss.isNotEmpty() && stats?.adjustments != null) {
            val maxLossValue = maxLoss.toIntOrNull() ?: return@LaunchedEffect
            
            // 计算调整建议
            val adjustments = profits.mapValues { (_, profit) ->
                if (profit < -maxLossValue) {
                    ((abs(profit.toDouble()) - maxLossValue) / specialNumberOdds).toInt()
                } else 0
            }

            // 更新统计信息
            stats = stats?.copy(
                adjustments = adjustments,
                totalReduction = adjustments.values.sum(),
                adjustedTotal = when {
                    showMacau && showHongKong -> (stats?.totalAmount ?: 0) - adjustments.values.sum()
                    showMacau -> (stats?.macauAmount ?: 0) - adjustments.values.sum()
                    showHongKong -> (stats?.hongKongAmount ?: 0) - adjustments.values.sum()
                    else -> 0
                }
            )
        }
    }

    // 添加特殊输入的支持变量
    var isEatingMode by remember { mutableStateOf(false) } // 是否为"吃"模式
    var eatingLimit by remember { mutableStateOf(0) } // "吃"模式的限制值
    
    // 修改辅助函数的可见性和位置（移到 calculateProfits 函数之前）
    fun updateStats(adjustments: Map<Int, Int>, adjustedProfits: Map<Int, Int>) {
        // 计算调整后的最大盈利和赢率
        val adjustedMaxProfit = when {
            selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                // 在确定风险号码模式下重新计算调整后的盈利
                val newAvailableTotal = when {
                    showMacau && showHongKong -> (stats?.totalAmount ?: 0) - adjustments.values.sum()
                    showMacau -> (stats?.macauAmount ?: 0) - adjustments.values.sum()
                    showHongKong -> (stats?.hongKongAmount ?: 0) - adjustments.values.sum()
                    else -> 0
                }

                // 计算每个号码的调整后盈利
                val profits = numberData.map { (number, data) ->
                    val adjustment = adjustments[number] ?: 0
                    val currentAmount = when {
                        showMacau && showHongKong -> data.totalAmount
                        showMacau -> data.macauAmount
                        showHongKong -> data.hongKongAmount
                        else -> 0
                    }

                    val zongshu = when {
                        showMacau && showHongKong -> newAvailableTotal + macauSpecialTotal + hkSpecialTotal
                        showMacau -> newAvailableTotal + macauSpecialTotal
                        showHongKong -> newAvailableTotal + hkSpecialTotal
                        else -> 0
                    }
                    val rebateAmount = calculateRebate(zongshu)
                    
                    newAvailableTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)
                }
                
                profits.maxOrNull()?.toInt() ?: 0
            }
            else -> adjustedProfits.values.maxOrNull() ?: 0
        }

        val adjustedWinCount = when {
            selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                // 在确定风险号码模式下重新计算调整后的赢率
                val newAvailableTotal = when {
                    showMacau && showHongKong -> (stats?.totalAmount ?: 0) - adjustments.values.sum()
                    showMacau -> (stats?.macauAmount ?: 0) - adjustments.values.sum()
                    showHongKong -> (stats?.hongKongAmount ?: 0) - adjustments.values.sum()
                    else -> 0
                }

                numberData.count { (number, data) ->
                    val adjustment = adjustments[number] ?: 0
                    val currentAmount = when {
                        showMacau && showHongKong -> data.totalAmount
                        showMacau -> data.macauAmount
                        showHongKong -> data.hongKongAmount
                        else -> 0
                    }

                    val zongshu = when {
                        showMacau && showHongKong -> newAvailableTotal + macauSpecialTotal + hkSpecialTotal
                        showMacau -> newAvailableTotal + macauSpecialTotal
                        showHongKong -> newAvailableTotal + hkSpecialTotal
                        else -> 0
                    }
                    val rebateAmount = calculateRebate(zongshu)
                    
                    val profit = newAvailableTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)
                    profit >= 0
                }
            }
            else -> adjustedProfits.count { it.value >= 0 }
        }

        val adjustedWinRate = (adjustedWinCount * 100f) / 49

        // 更新统计信息
        stats = stats?.copy(
            adjustments = adjustments,
            totalReduction = adjustments.values.sum(),
            adjustedTotal = when {
                showMacau && showHongKong -> (stats?.totalAmount ?: 0) - adjustments.values.sum()
                showMacau -> (stats?.macauAmount ?: 0) - adjustments.values.sum()
                showHongKong -> (stats?.hongKongAmount ?: 0) - adjustments.values.sum()
                else -> 0
            },
            maxProfit = profits.values.maxOrNull() ?: 0,
            winRate = (profits.count { it.value >= 0 } * 100f) / 49,
            adjustedMaxProfit = adjustedMaxProfit,
            adjustedWinRate = adjustedWinRate
        )
    }

    // 修改 calculateProfits 函数
    fun calculateProfits() {
        if (isEatingMode) {
            // "吃"模式的计算逻辑保持不变
            val limit = eatingLimit
            if (limit <= 0) return
            
            // 计算调整建议 - 对超过限制的号码进行减注
            val adjustments = numberData.mapValues { (_, data) ->
                val currentAmount = when {
                    showMacau && showHongKong -> data.totalAmount
                    showMacau -> data.macauAmount
                    showHongKong -> data.hongKongAmount
                    else -> 0
                }
                
                if (currentAmount > limit) {
                    currentAmount - limit // 超出部分需要减掉
                } else {
                    0 // 没超出限制，不需要减注
                }
            }

            // 计算调整后的每个号码的盈利
            val adjustedProfits = profits.mapValues { (number, profit) ->
                val adjustment = adjustments[number] ?: 0 // 调整值
                val totalReduction = adjustments.values.sum() // 总减注
                
                when {
                    showMacau && showHongKong -> {
                        val totalAmount = stats?.totalAmount ?: 0 // 总注额
                        val adjustedTotal = totalAmount - totalReduction // 调整后总注额
                        val currentAmount = numberData[number]?.totalAmount ?: 0 // 总注额
                        val zongshu = adjustedTotal + macauSpecialTotal + hkSpecialTotal
                        val rebateAmount = calculateRebate(zongshu) // 返水金额
                        
                        if (adjustment > 0) {
                            (adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)).toInt()
                        } else {
                            (adjustedTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                    }
                    showMacau -> {
                        val macauTotal = stats?.macauAmount ?: 0 // 澳门总注额
                        val adjustedTotal = macauTotal - totalReduction // 调整后总注额
                        val currentAmount = numberData[number]?.macauAmount ?: 0 // 澳门注额
                        val zongshu = adjustedTotal + macauSpecialTotal
                        val rebateAmount = calculateRebate(zongshu) // 返水金额
                        
                        if (adjustment > 0) {
                            (adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)).toInt()
                        } else {
                            (adjustedTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                    }
                    showHongKong -> {
                        val hkTotal = stats?.hongKongAmount ?: 0 // 香港总注额
                        val adjustedTotal = hkTotal - totalReduction // 调整后总注额
                        val currentAmount = numberData[number]?.hongKongAmount ?: 0 // 香港注额
                        val zongshu = adjustedTotal + hkSpecialTotal
                        val rebateAmount = calculateRebate(zongshu) // 返水金额
                        
                        if (adjustment > 0) {
                            (adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)).toInt()
                        } else {
                            (adjustedTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                    }
                    else -> profit
                }
            }

            // 更新统计信息
            updateStats(adjustments, adjustedProfits.mapValues { it.value.toInt() })
            return
        }

        // 获取最大亏损值
        val maxLossValue = maxLoss.toIntOrNull() ?: return

        // 新的分段减注逻辑
        if (selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty()) {
            // 计算确定风险号码的总金额
            val confirmedRiskTotal = selectedSpecialNumbers.sumOf { number ->
                when {
                    showMacau && showHongKong -> numberData[number]?.totalAmount ?: 0
                    showMacau -> numberData[number]?.macauAmount ?: 0
                    showHongKong -> numberData[number]?.hongKongAmount ?: 0
                    else -> 0
                }
            }

            // 计算新的可用总资金
            val newAvailableTotal = when {
                showMacau && showHongKong -> (stats?.totalAmount ?: 0) - confirmedRiskTotal
                showMacau -> (stats?.macauAmount ?: 0) - confirmedRiskTotal
                showHongKong -> (stats?.hongKongAmount ?: 0) - confirmedRiskTotal
                else -> 0
            }

            // 计算调整建议
            val adjustments = numberData.mapValues { (number, data) ->
                val currentAmount = when {
                    showMacau && showHongKong -> data.totalAmount
                    showMacau -> data.macauAmount
                    showHongKong -> data.hongKongAmount
                    else -> 0
                }

                if (number in selectedSpecialNumbers) {
                    // 确定风险号码全额减注
                    currentAmount
                } else {
                    // 其他号码基于新的可用资金计算建议减注额
                    val currentProfit = when {
                        showMacau && showHongKong -> {
                            val zongshu = newAvailableTotal + macauSpecialTotal + hkSpecialTotal
                            val rebateAmount = calculateRebate(zongshu)
                            (newAvailableTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                        showMacau -> {
                            val zongshu = newAvailableTotal + macauSpecialTotal
                            val rebateAmount = calculateRebate(zongshu)
                            (newAvailableTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                        showHongKong -> {
                            val zongshu = newAvailableTotal + hkSpecialTotal
                            val rebateAmount = calculateRebate(zongshu)
                            (newAvailableTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                        else -> 0
                    }

                    if (currentProfit < -maxLossValue) {
                        ((kotlin.math.abs(currentProfit.toDouble()) - maxLossValue) / specialNumberOdds).toInt()
                    } else 0
                }
            }

            // 计算调整后的每个号码的盈利
            val adjustedProfits = profits.mapValues { (number, _) ->
                val adjustment = adjustments[number] ?: 0
                val totalReduction = adjustments.values.sum()
                val currentAmount = when {
                    showMacau && showHongKong -> numberData[number]?.totalAmount ?: 0
                    showMacau -> numberData[number]?.macauAmount ?: 0
                    showHongKong -> numberData[number]?.hongKongAmount ?: 0
                    else -> 0
                }

                when {
                    showMacau && showHongKong -> {
                        val adjustedTotal = newAvailableTotal - totalReduction
                        val zongshu = adjustedTotal + macauSpecialTotal + hkSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)
                    }
                    showMacau -> {
                        val adjustedTotal = newAvailableTotal - totalReduction
                        val zongshu = adjustedTotal + macauSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)
                    }
                    showHongKong -> {
                        val adjustedTotal = newAvailableTotal - totalReduction
                        val zongshu = adjustedTotal + hkSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)
                    }
                    else -> 0
                }.toInt()
            }

            // 更新统计信息
            updateStats(adjustments, adjustedProfits)
        } else {
            // 原有的最大亏损计算逻辑
            val adjustments = profits.mapValues { (_, profit) ->
                if (profit < -maxLossValue) {
                    ((kotlin.math.abs(profit.toDouble()) - maxLossValue) / specialNumberOdds).toInt()
                } else 0
            }

            // 计算调整后的每个号码的盈利
            val adjustedProfits = profits.mapValues { (number, _) ->
                val adjustment = adjustments[number] ?: 0
                val totalReduction = adjustments.values.sum()
                
                when {
                    showMacau && showHongKong -> {
                        val totalAmount = stats?.totalAmount ?: 0
                        val adjustedTotal = totalAmount - totalReduction
                        val currentAmount = numberData[number]?.totalAmount ?: 0
                        val zongshu = adjustedTotal + macauSpecialTotal + hkSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        if (adjustment > 0) {
                            (adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)).toInt()
                        } else {
                            (adjustedTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                    }
                    showMacau -> {
                        val macauTotal = stats?.macauAmount ?: 0
                        val adjustedTotal = macauTotal - totalReduction
                        val currentAmount = numberData[number]?.macauAmount ?: 0
                        val zongshu = adjustedTotal + macauSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        if (adjustment > 0) {
                            (adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)).toInt()
                        } else {
                            (adjustedTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                    }
                    showHongKong -> {
                        val hkTotal = stats?.hongKongAmount ?: 0
                        val adjustedTotal = hkTotal - totalReduction
                        val currentAmount = numberData[number]?.hongKongAmount ?: 0
                        val zongshu = adjustedTotal + hkSpecialTotal
                        val rebateAmount = calculateRebate(zongshu)
                        if (adjustment > 0) {
                            (adjustedTotal - rebateAmount - ((currentAmount - adjustment) * specialNumberOdds)).toInt()
                        } else {
                            (adjustedTotal - rebateAmount - (currentAmount * specialNumberOdds)).toInt()
                        }
                    }
                    else -> 0
                }
            }

            // 更新统计信息
            updateStats(adjustments, adjustedProfits.mapValues { it.value.toInt() })
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(10.dp) // 设置内边距
    ) {
        // 统计信息面板
        Card(
            modifier = Modifier.fillMaxWidth(),// 设置宽度为父容器宽度
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)// 设置卡片阴影
        ) {
            if (isTabletMode) {
                // 平板模式：使用可折叠卡片布局
                Column(
                    modifier = Modifier
                        .padding(10.dp)// 设置内边距
                        .clip(RoundedCornerShape(8.dp)) // 添加圆角裁剪
                        .clickable { isStatsCardExpanded = !isStatsCardExpanded }// 添加点击事件
                        .animateContentSize()// 添加动画
                ) {
                    // 第一行：总注额、标签开关和折叠图标
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()// 设置宽度为父容器宽度
                            .height(26.dp),// 设置固定高度
                        horizontalArrangement = Arrangement.SpaceBetween,// 设置水平对齐方式
                        verticalAlignment = Alignment.CenterVertically// 设置垂直对齐方式
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(bottom = 4.dp) // 设置垂直内边距
                        ) {
                            Text(
                                text = "总数: ",
                                style = MaterialTheme.typography.titleMedium
                                    .copy(fontWeight = FontWeight.Bold)
                            )
                            Text(
                                text = "${(stats?.totalAmount ?: 0) + specialBetsTotal}",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                            
                            // 返水文本按钮 - 使用主题色并优化点击反馈
                            Box(
                                modifier = Modifier
                                    .clip(MaterialTheme.shapes.medium) // 添加圆角裁剪
                                    .clickable(
                                        interactionSource = remember { MutableInteractionSource() },
                                        indication = rememberRipple(bounded = true), // 使用有界的涟漪效果
                                        onClick = { 
                                            // 点击时打开返水设置对话框
                                            rebateValue = sharedPrefs.getFloat("rebate_value", 0f).toString()
                                            rebateEnabled = sharedPrefs.getBoolean("rebate_enabled", false)
                                            showRebateDialog = true
                                        }
                                    )
                                    .padding(horizontal = 8.dp, vertical = 4.dp) // 增加点击区域
                            ) {
                                Text(
                                    text = if (rebateEnabled) {
                                        val rebateAmount = calculateRebate((stats?.totalAmount ?: 0) + specialBetsTotal)
                                        "返水(${rebateValue}%)≈$rebateAmount"
                                    } else {
                                        "返水设置"
                                    },
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Bold
                                    ),
                                    color = MaterialTheme.colorScheme.primary // 使用主题主色
                                )
                            }
                        }
                        
                        Row(
                            verticalAlignment = Alignment.CenterVertically,// 设置垂直对齐方式
                            horizontalArrangement = Arrangement.End,// 设置水平对齐方式
                            modifier = Modifier.padding(vertical = 6.dp)  // 调整垂直间距
                        ) {
                            // 澳门开关组
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(horizontal = 1.dp)//设置水平内边距
                            ) {
                                Text(
                                    text = "澳门",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = if (showMacau) 
                                        MaterialTheme.colorScheme.primary 
                                    else 
                                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                                Switch(
                                    checked = showMacau,
                                    onCheckedChange = { showMacau = it },
                                    modifier = Modifier
                                        .scale(0.6f)
                                        .offset(x = (-18).dp)  //
                                )
                            }
                            
                            Spacer(modifier = Modifier.width(1.dp))  // 调整间距
                            
                            // 香港开关组
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(horizontal = 1.dp)
                            ) {
                                Text(
                                    text = "香港",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = if (showHongKong) 
                                        MaterialTheme.colorScheme.primary 
                                    else 
                                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                                Switch(
                                    checked = showHongKong,
                                    onCheckedChange = { showHongKong = it },
                                    modifier = Modifier
                                        .scale(0.6f)
                                        .offset(x = (-18).dp)  // 微调开关位置
                                )
                            }
                            
                            Icon(
                                imageVector = if (isStatsCardExpanded) // 如果卡片展开
                                    Icons.Default.KeyboardArrowUp // 设置图标
                                else 
                                    Icons.Default.KeyboardArrowDown,// 设置图标
                                contentDescription = if (isStatsCardExpanded) "收起" else "展开",// 设置内容描述
                                modifier = Modifier
                                    .padding(start = 18.dp)// 设置水平内边距
                                    .size(18.dp)// 设置图标大小
                            )
                        }
                    }

                    if (isStatsCardExpanded) {
                        
                        // 展开时显示的内容
                        if (showMacau) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(bottom = 2.dp)
                            ) {
                                Text("澳门: ")// 设置文本内容
                                Text(
                                    text = "${(stats?.macauAmount ?: 0)} 平特类: $macauSpecialTotal",// 设置文本内容
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary// 设置文本颜色
                                )
                            }
                        }
                        
                        if (showHongKong) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,// 设置垂直对齐方式
                                modifier = Modifier.padding(bottom = 2.dp)// 设置垂直内边距
                            ) {
                                Text("香港: ")// 设置文本内容
                                Text(
                                    text = "${(stats?.hongKongAmount ?: 0)} 平特类: $hkSpecialTotal",// 设置文本内容
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary// 设置文本颜色
                                )
                                
                            }
                        }
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("最大盈利: ")// 设置文本内容
                            Text(
                                text = "${profits.values.maxOrNull() ?: 0}",// 设置文本内容
                                style = MaterialTheme.typography.titleMedium,
                                color = if ((profits.values.maxOrNull() ?: 0) >= 0) // 如果最大盈利大于等于0
                                    MaterialTheme.colorScheme.primary
                                else 
                                    MaterialTheme.colorScheme.error// 设置文本颜色
                            )
                            Text(" | 赢率: ")// 设置文本内容
                            Text(
                                text = if (numberData.all { it.value.totalAmount == 0 }) {
                                    "0.0%"
                                } else {
                                    "${String.format("%.1f", (profits.count { it.value >= 0 } * 100f) / 49)}%"
                                },
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.primary// 设置文本颜色
                            )
                        }
                        
                        if (maxLoss.isNotEmpty() && (stats?.totalReduction ?: 0) > 0) {
                            Divider(
                                modifier = Modifier
                                    .padding(vertical = 8.dp)
                                    .fillMaxWidth()
                            )
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = if (isEatingMode) 
                                        "建议总减注(限额${eatingLimit}): ${stats?.totalReduction}" 
                                    else 
                                        "建议总减注: ${stats?.totalReduction}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.error
                                )
                                Text(
                                    text = "调整后号码类总额: ${stats?.adjustedTotal}",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "调整后最大盈利: ${stats?.adjustedMaxProfit}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if ((stats?.adjustedMaxProfit ?: 0) >= 0) 
                                        MaterialTheme.colorScheme.primary 
                                    else 
                                        MaterialTheme.colorScheme.error
                                )
                                Text(
                                    text = "调整后赢率: ${String.format("%.1f", stats?.adjustedWinRate ?: 0f)}%",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                }
            } else {
                // 手机模式：使用原有的固定布局
                Column(modifier = Modifier.padding(16.dp)) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.Top
                    ) {
                        Column {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(bottom = 4.dp)//
                            ) {
                                Text(
                                    text = "总数: ",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Text(
                                    text = "${(stats?.totalAmount ?: 0) + specialBetsTotal}",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                
                                // 返水文本按钮 - 使用主题色并优化点击反馈
                                Box(
                                    modifier = Modifier
                                        .clip(MaterialTheme.shapes.medium) // 添加圆角裁剪
                                        .clickable(
                                            interactionSource = remember { MutableInteractionSource() },
                                            indication = rememberRipple(bounded = true), // 使用有界的涟漪效果
                                            onClick = { 
                                                // 点击时打开返水设置对话框
                                                rebateValue = sharedPrefs.getFloat("rebate_value", 0f).toString()
                                                rebateEnabled = sharedPrefs.getBoolean("rebate_enabled", false)
                                                showRebateDialog = true
                                            }
                                        )
                                        .padding(horizontal = 8.dp, vertical = 4.dp) // 增加点击区域
                                ) {
                                    Text(
                                        text = if (rebateEnabled) {
                                            val rebateAmount = calculateRebate((stats?.totalAmount ?: 0) + specialBetsTotal)
                                            "返水(${rebateValue}%)≈$rebateAmount"
                                        } else {
                                            "返水设置"
                                        },
                                        style = MaterialTheme.typography.bodyMedium.copy(
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = MaterialTheme.colorScheme.primary // 使用主题主色
                                    )
                                }
                            }
                            
                            
                            if (showMacau) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(bottom = 2.dp)
                                ) {
                                    Text("澳门: ")
                                    Text(
                                        text = "${(stats?.macauAmount ?: 0) }",//+ macauSpecialTotal}",
                                        style = MaterialTheme.typography.titleMedium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("平特类: ")
                                    Text(
                                        text = "$macauSpecialTotal",
                                        style = MaterialTheme.typography.titleMedium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                }
                            }
                            
                            if (showHongKong) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(bottom = 2.dp)
                                ) {
                                    Text("香港: ")
                                    Text(
                                        text = "${(stats?.hongKongAmount ?: 0) }",//+ hkSpecialTotal}",
                                        style = MaterialTheme.typography.titleMedium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("平特类: ")
                                    Text(
                                        text = "$hkSpecialTotal",
                                        style = MaterialTheme.typography.titleMedium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                }
                            }
                            
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text("最大盈利: ")
                                Text(
                                    text = "${profits.values.maxOrNull() ?: 0}",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = if ((profits.values.maxOrNull() ?: 0) >= 0) 
                                        MaterialTheme.colorScheme.primary 
                                    else 
                                        MaterialTheme.colorScheme.error
                                )
                                Text(" | 赢率: ")
                                Text(
                                    text = if (numberData.all { it.value.totalAmount == 0 }) {
                                        "0.0%"
                                    } else {
                                        "${String.format("%.1f", (profits.count { it.value >= 0 } * 100f) / 49)}%"
                                    },
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                        
                        Column(horizontalAlignment = Alignment.End) {
                            // 删除标识过滤按钮及相关Box
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(bottom = 4.dp)
                            ) {
                                Text(
                                    text = "澳门",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = if (showMacau) 
                                        MaterialTheme.colorScheme.primary 
                                    else 
                                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                                Switch(
                                    checked = showMacau,
                                    onCheckedChange = { showMacau = it },
                                    modifier = Modifier.scale(0.8f)
                                )
                            }
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text(
                                    text = "香港",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = if (showHongKong) 
                                        MaterialTheme.colorScheme.primary 
                                    else 
                                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                                Switch(
                                    checked = showHongKong,
                                    onCheckedChange = { showHongKong = it },
                                    modifier = Modifier.scale(0.8f)
                                )
                            }
                        }
                    }
                    
                    if (maxLoss.isNotEmpty() && (stats?.totalReduction ?: 0) > 0) {
                        Divider(
                            modifier = Modifier
                                .padding(vertical = 8.dp)
                                .fillMaxWidth()
                        )
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = if (isEatingMode) 
                                    "建议总减注(限额${eatingLimit}): ${stats?.totalReduction}" 
                                else 
                                    "建议总减注: ${stats?.totalReduction}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = "调整后号码类总额: ${stats?.adjustedTotal}",
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "调整后最大盈利: ${stats?.adjustedMaxProfit}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = if ((stats?.adjustedMaxProfit ?: 0) >= 0) 
                                    MaterialTheme.colorScheme.primary 
                                else 
                                    MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = "调整后赢率: ${String.format("%.1f", stats?.adjustedWinRate ?: 0f)}%",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            }
        }
        Spacer(modifier = Modifier.height(0.dp)) // 总数卡片和输入框的距离
        // 入和导出区域
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 开奖号码输入框
            OutlinedTextField(
                value = lotteryNumber,
                onValueChange = { newValue ->
                    if (newValue.isEmpty()) {
                        calculationStatus = ""  // 清空时重置状态
                        lotteryNumber = ""
                        selectedSpecialNumbers.clear()
                        return@OutlinedTextField
                    }

                    // 优先处理"飞"字输入
                    if (newValue.startsWith("飞")) {
                        lotteryNumber = newValue
                        // 如果只输入了"飞"字，也允许
                        if (newValue == "飞") {
                            selectedSpecialNumbers.clear()
                            return@OutlinedTextField
                        }
                        
                        val numbers = newValue.substring(1).trim().split(Regex("\\s+"))
                            .mapNotNull { numStr ->
                                when {
                                    numStr.startsWith("0") && numStr.length == 2 -> numStr.substring(1).toIntOrNull()
                                    else -> numStr.toIntOrNull()
                                }
                            }
                            .filter { it in 1..49 }
                            .distinct()

                        selectedSpecialNumbers.clear()
                        if (numbers.isNotEmpty()) {
                            selectedSpecialNumbers.addAll(numbers)
                        }
                        return@OutlinedTextField
                    }

                    // 原有的特殊输入检查逻辑
                    val specialInput = checkSpecialInput(newValue.trim())
                    if (specialInput.isNotEmpty()) {
                        lotteryNumber = newValue
                        selectedSpecialNumbers.clear()
                        selectedSpecialNumbers.addAll(specialInput)
                        calculationStatus = ""
                        return@OutlinedTextField
                    }
                    
                    // 重置特殊选中状态
                    selectedSpecialNumbers.clear()
                    
                    // 分割输入的数字字符串
                    val numberStrings = newValue.split(Regex("[,，\\s]+"))
                        .filter { it.isNotEmpty() }
                    Log.d("InputDebug", "分割后的数字: $numberStrings")
                    
                    // 检查输入数量是否超过7个
                    if (numberStrings.size > 7) {
                        Toast.makeText(
                            context,
                            "最多只能输入7个号码",
                            Toast.LENGTH_SHORT
                        ).apply { setGravity(Gravity.CENTER, 0, 0) }.show()
                        return@OutlinedTextField
                    }
                    
                    // 验证数字格式和范围
                    val isValidFormat = numberStrings.all { numStr ->
                        when {
                            // 允许单个0（用于输入01-09）
                            numStr == "0" -> true
                            // 允许01-09格式
                            numStr.startsWith("0") && numStr.length == 2 -> 
                                numStr.substring(1).toIntOrNull()?.let { it in 1..9 } ?: false
                            // 允许1-49格式
                            else -> numStr.toIntOrNull()?.let { it in 1..49 } ?: false
                        }
                    }
                    
                    // 检查重复（使用原始字符串比较）
                    val hasNoDuplicates = numberStrings.size == numberStrings.distinct().size
                    
                    Log.d("InputDebug", "验证结果 - 格式有效: $isValidFormat, 无重复: $hasNoDuplicates")
                    
                    if (!hasNoDuplicates && numberStrings.isNotEmpty()) {
                        Toast.makeText(
                            context,
                            "输入了重复的号码",
                            Toast.LENGTH_SHORT
                        ).apply { setGravity(Gravity.CENTER, 0, 0) }.show()
                        return@OutlinedTextField
                    }
                    
                    // 验证通过则更新输入值
                    if (isValidFormat && hasNoDuplicates) {
                        Log.d("InputDebug", "更新输入框值: $newValue")
                        lotteryNumber = newValue
                    } else if (!isValidFormat) {
                        Log.d("InputDebug", "输入验证失败，不更新输入框")
                        Toast.makeText(
                            context,
                            "请输入有效号码(1-49或01-09)",
                            Toast.LENGTH_SHORT
                        ).apply { setGravity(Gravity.CENTER, 0, 0) }.show()
                    }
                },
                label = { 
                    Text(
                        text = "输入特码或7个开奖码",
                        modifier = Modifier.fillMaxWidth(), 
                        textAlign = TextAlign.Center 
                    ) 
                },
                keyboardOptions = KeyboardOptions( 
                    keyboardType = KeyboardType.Text, // 修改为Text类型，允许输入汉字
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        if (lotteryNumber.isEmpty()) return@KeyboardActions
                        
                        // 检查是否是特殊输入
                        val specialInput = checkSpecialInput(lotteryNumber.trim())
                        if (specialInput.isNotEmpty()) {
                            // 特殊输入只用于高亮显示，不触发计算
                            selectedSpecialNumbers.clear()
                            selectedSpecialNumbers.addAll(specialInput)
                            // 播放音效提示用户选择成功
                            SoundUtils.playSuccessSound(context)
                            return@KeyboardActions
                        }
                        
                        // 处理普通数字输入
                        val numberStrings = lotteryNumber.split(Regex("[,，\\s]+"))
                            .filter { it.isNotEmpty() }
                        
                        val parsedNumbers = numberStrings.mapNotNull { numStr ->
                            val num = when {
                                numStr.startsWith("0") && numStr.length == 2 -> numStr.substring(1).toIntOrNull()
                                else -> numStr.toIntOrNull()
                            }
                            num
                        }
                        
                        val hasNoDuplicates = parsedNumbers.size == parsedNumbers.distinct().size
                        if (!hasNoDuplicates) {
                            Toast.makeText(
                                context,
                                "不能输入重复的号码",
                                Toast.LENGTH_SHORT
                            ).apply { setGravity(Gravity.CENTER, 0, 0) }.show()
                            return@KeyboardActions
                        }
                        
                        // 只有单个号码或者完整的7个号码才进行计算
                        if (parsedNumbers.all { it in 1..49 } && 
                            (parsedNumbers.size == 1 || parsedNumbers.size == 7)) {
                            SoundUtils.playSuccessSound(context)
                            calculationStatus = "中奖数据计算完成"
                            // 这里可以添加计算逻辑
                        } else if (parsedNumbers.isNotEmpty()) {
                            // 如果输入了数字但不是单个或7个，只显示高亮而不触发计算
                            Toast.makeText(
                                context,
                                "只有单个号码或7个号码才会计算中奖数据",
                                Toast.LENGTH_SHORT
                            ).apply { setGravity(Gravity.CENTER, 0, 0) }.show()
                        }
                    }
                ),
                modifier = Modifier
                    .weight(0.65f)
                    .padding(end = 0.dp), // 输入框距离
                singleLine = true,
                textStyle = LocalTextStyle.current.copy(textAlign = TextAlign.Center),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    unfocusedBorderColor = MaterialTheme.colorScheme.outline
                )
            )

            // 添加开奖结果按钮
            FilledTonalButton(
                onClick = {
                    when {
                        showMacau && showHongKong -> {
                            Toast.makeText(
                                context,
                                "请切换开关选择要获取的开奖结果",
                                Toast.LENGTH_SHORT
                            ).apply { setGravity(Gravity.CENTER, 0, 0) }.show()
                        }
                        !showMacau && !showHongKong -> {
                            Toast.makeText(
                                context,
                                "请切换开关选择要获取的开奖结果",
                                Toast.LENGTH_SHORT
                            ).apply { setGravity(Gravity.CENTER, 0, 0) }.show()
                        }
                        else -> {
                            scope.launch {
                                try {
                                    val apiUrl = if (showMacau) {
                                        "https://macaumarksix.com/api/macaujc2.com"
                                    } else {
                                        "https://macaumarksix.com/api/hkjc.com"
                                    }
                                    
                                    val response = withContext(Dispatchers.IO) {
                                        URL(apiUrl).readText()
                                    }
                                    
                                    val jsonArray = JSONArray(response)
                                    if (jsonArray.length() > 0) {
                                        val jsonObject = jsonArray.getJSONObject(0)
                                        val openCode = jsonObject.getString("openCode")
                                        val expect = jsonObject.getString("expect")
                                        val numbers = openCode.split(",").map { it.trim() }
                                        lotteryNumber = numbers.joinToString(" ")
                                        
                                        // 显示带期号的提示，设置为5秒
                                        Toast.makeText(
                                            context,
                                            "${if (showMacau) "澳门" else "香港"}第${expect}期\n开奖号码：${numbers.joinToString(" ")}",
                                            Toast.LENGTH_LONG // 使用LONG显示时间更长
                                        ).apply { 
                                            setGravity(Gravity.CENTER, 0, 0)
                                            // 5秒后自动取消显示
                                            scope.launch {
                                                withContext(Dispatchers.Main) {
                                                    kotlinx.coroutines.delay(30000)
                                                    cancel()
                                                }
                                            }
                                        }.show()
                                        
                                        SoundUtils.playSuccessSound(context)
                                        calculationStatus = "中奖数据计算完成"
                                        VibrationUtils.vibrate(context)
                                    }
                                } catch (e: Exception) {
                                    Toast.makeText(
                                        context,
                                        "获取开奖结果失败：${e.message}",
                                        Toast.LENGTH_SHORT
                                    ).apply { setGravity(Gravity.CENTER, 0, 0) }.show()
                                }
                            }
                        }
                    }
                },
                modifier = Modifier
                    .padding(horizontal = 1.dp) // 水平内边距设为1dp
                    .padding(top = 6.dp)
                    .height(60.dp)
                    .width(36.dp), // 设置宽度为36dp
                contentPadding = PaddingValues(
                    horizontal = 2.dp, // 内容水平内边距
                    vertical = 4.dp // 内容垂直内边距
                ),
                shape = RoundedCornerShape(4.dp),
                colors = ButtonDefaults.filledTonalButtonColors(
                    containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.55f)
                )
            ) {
                Text(
                    text = "开\n奖",
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontSize = 15.sp // 调整字体大小
                    ),
                    textAlign = TextAlign.Center,
                    lineHeight = 16.sp, // 减小行高
                    modifier = Modifier.padding(1.dp)
                )
            }
            
            // 盈利计算输入框
            OutlinedTextField(
                value = maxLoss,
                onValueChange = { newValue ->
                    if (newValue.isEmpty()) {
                        calculationStatus = ""  // 清空时重置状态
                        isEatingMode = false    // 重置"吃"模式
                        eatingLimit = 0         // 重置限制值
                        maxLoss = ""
                        return@OutlinedTextField
                    }
                    
                    // 检查是否是"吃"模式输入
                    if (newValue.startsWith("吃")) {
                        val limitStr = newValue.substring(1)
                        if (limitStr.isEmpty()) {
                            maxLoss = newValue
                            isEatingMode = true
                            eatingLimit = 0
                        } else if (limitStr.matches(Regex("^\\d*$")) && limitStr.length <= 8) {
                            maxLoss = newValue
                            isEatingMode = true
                            eatingLimit = limitStr.toIntOrNull() ?: 0
                        }
                    } else {
                        // 原有的最大亏损输入逻辑
                        if (newValue.isEmpty() || newValue.matches(Regex("^-?\\d*$")) && newValue.length <= 8) {
                            maxLoss = newValue
                            isEatingMode = false
                            eatingLimit = 0
                        }
                    }
                },
                label = { 
                    Text(
                        text = if (isEatingMode) "注额限制" else "盈利调整",
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center
                    ) 
                },
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,  // 改为Text以支持中文输入
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        if (isEatingMode) {
                            if (eatingLimit > 0) {
                                calculateProfits()
                                SoundUtils.playSuccessSound(context)
                                calculationStatus = "注额限制计算完成"  // 设置状态
                            }
                        } else {
                            val lossValue = maxLoss.toIntOrNull()
                            if (lossValue != null) {
                                calculateProfits()
                                SoundUtils.playSuccessSound(context)
                                calculationStatus = "减注号码计算完成"  // 设置状态
                            }
                        }
                    }
                ),
                modifier = Modifier
                    .weight(0.35f),
                singleLine = true,
                textStyle = LocalTextStyle.current.copy(textAlign = TextAlign.Center),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    unfocusedBorderColor = MaterialTheme.colorScheme.outline
                )
            )
        }
        Spacer(modifier = Modifier.height(0.dp)) // 输入和导出区域和数据列表的距离
        
        // 改为包含悬浮按钮的布局
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        ) {
            // 数据列表
            if (isTabletMode) {
                // 平板模式使用网格布局 - 使用明确的列数而非自适应
                val windowSize = LocalConfiguration.current
                val screenWidth = windowSize.screenWidthDp.dp
                
                // 动态确定列数，根据屏幕宽度适配，使用更合理的宽度临界值
                val gridColumns = when {
                    screenWidth < 650.dp -> GridCells.Fixed(1) // 窄窗口一律使用单列
                    else -> GridCells.Fixed(2) // 宽窗口统一使用两列
                    // 不再使用三列，避免内容过于拥挤
                }
                
                LazyVerticalGrid(
                    columns = gridColumns,
                    modifier = Modifier
                        .fillMaxSize()
                        .animateContentSize(),
                    contentPadding = PaddingValues(vertical = 3.dp),
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalArrangement = Arrangement.spacedBy(3.dp)
                ) {
                    items(numberData.keys.sortedBy { number -> profits[number] ?: 0 }) { number ->
                        // 使用现有的卡片布局
                        val data = numberData[number]!!
                        val profit = profits[number] ?: 0
                        val adjustment = if (maxLoss.isNotEmpty()) stats?.adjustments?.get(number) ?: 0 else 0
                        
                        // 检查是否应该高亮显示
                        val shouldHighlight = lotteryNumber.split(Regex("[,，\\s]+")).let { nums ->
                            when {
                                nums.size == 1 -> {
                                    val inputNum = nums[0]
                                    inputNum == formatNumberDisplay(number) || 
                                    inputNum == number.toString() ||
                                    number in selectedSpecialNumbers // 添加特殊选择检查
                                }
                                nums.size == 7 -> {
                                    val lastNum = nums[6]
                                    lastNum == formatNumberDisplay(number) ||
                                    lastNum == number.toString() ||
                                    number in selectedSpecialNumbers // 添加特殊选择检查
                                }
                                else -> number in selectedSpecialNumbers // 添加特殊选择检查
                            }
                        }
                        
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .animateContentSize()
                                .padding(horizontal = 1.dp, vertical = 1.dp) // 卡片的距离
                                .shadow(
                                    elevation = if (shouldHighlight) 3.dp else 1.dp,
                                    shape = RoundedCornerShape(12.dp),
                                    spotColor = when {
                                        adjustment > 0 -> Color(0xFFE30101).copy(alpha = 0.5f) //有调整时的阴影颜色
                                        profit < 0 -> Color(0xFFE30101).copy(alpha = 0.26f) //负盈利时使用更鲜明的红色
                                        else -> MaterialTheme.colorScheme.primary.copy(alpha = 0.36f) //默认阴影颜色
                                    }
                                ),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = if (shouldHighlight) 
                                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.8f)
                                else 
                                    MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.1f)
                            )
                        ) {
                            // 卡片内容 - 根据屏幕宽度调整内容布局
                            val windowSize = LocalConfiguration.current
                            val itemScreenWidth = windowSize.screenWidthDp.dp
                            
                            // 动态调整元素尺寸
                            val numberBoxWidth = if (itemScreenWidth < 600.dp) 60.dp else 72.dp
                            val profitBoxWidth = if (itemScreenWidth < 600.dp) 120.dp else 150.dp
                            val numberFontSize = if (itemScreenWidth < 600.dp) 28.sp else 32.sp
                            val zodiacFontSize = if (itemScreenWidth < 600.dp) 14.sp else 16.sp
                            val horizontalPadding = if (itemScreenWidth < 600.dp) 6.dp else 8.dp
                            
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = horizontalPadding, vertical = 8.dp), //卡片内容距离和卡片高度
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {

                                Box(
                                    modifier = Modifier
                                        .width(numberBoxWidth)  // 动态调整宽度
                                        .padding(start = 1.dp) //生肖距离
                                        .background(
                                            color = DataParser.getNumberColor(number).background,
                                            shape = RoundedCornerShape(8.dp) //生肖背景
                                        )
                                        .border(
                                            width = 1.dp,
                                            color = DataParser.getNumberColor(number).border,
                                            shape = RoundedCornerShape(8.dp)
                                        )
                                        .padding(horizontal = 6.dp, vertical = 1.dp)
                                        .scale(
                                            animateFloatAsState(
                                                targetValue = if (shouldHighlight) 1.2f else 1f,
                                                animationSpec = spring(
                                                    dampingRatio = Spring.DampingRatioMediumBouncy,
                                                    stiffness = Spring.StiffnessLow
                                                )
                                            ).value
                                        ),
                                    contentAlignment = Alignment.CenterStart
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Start
                                    ) {
                                        Text(
                                            text = String.format("%02d", number),
                                            style = MaterialTheme.typography.titleLarge,
                                            fontSize = numberFontSize,
                                            color = MaterialTheme.colorScheme.onSurface
                                        )
                                        Text(
                                            text = ZodiacUtils.getZodiacForNumber(number) ?: "",
                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                fontSize = zodiacFontSize
                                            ),
                                            color = MaterialTheme.colorScheme.onSurface,
                                            modifier = Modifier.padding(start = 1.dp, top = 8.dp)
                                        )
                                    }
                                }
                                
                                // 注额和计数信息，调整间距
                                Column(
                                    modifier = Modifier
                                        .weight(1f) //注额和计数信息宽度占用剩余空间的全部
                                        .padding(horizontal = 3.dp), //注额和计数信息距离
                                    horizontalAlignment = Alignment.Start
                                ) {
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Text(
                                            text = "${data.totalAmount}",
                                            style = MaterialTheme.typography.bodyLarge,
                                            fontWeight = FontWeight.Bold,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Text(
                                            text = "(${data.count}注)",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                                            modifier = Modifier.padding(start = 5.dp)
                                        )
                                    }
                                    
                                    // 澳门和香港注额 - 优化布局，垂直排列以节省空间
                                    if (showMacau || showHongKong) {
                                        Row(
                                            modifier = Modifier
                                                .padding(top = 2.dp)
                                        ) {
                                            if (showMacau && data.macauAmount > 0) {
                                                Text(
                                                    text = "澳:${data.macauAmount}",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f)
                                                )
                                            }
                                            if (showHongKong && data.hongKongAmount > 0) {
                                                Text(
                                                    text = "港:${data.hongKongAmount}",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                                                    modifier = Modifier.padding(start = if (showMacau && data.macauAmount > 0) 8.dp else 0.dp)
                                                )
                                            }
                                        }
                                    }
                                    
                                    // 建议减注信 - 确保它总是显示在最下面
                                    if (maxLoss.isNotEmpty() && adjustment > 0) {
                                        Text(
                                            text = "建议减注: ${adjustment.toInt()}",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.error,
                                            modifier = Modifier
                                                .padding(top = 2.dp)
                                                .fillMaxWidth(), // 确保文本有足够的宽度
                                            maxLines = 1, // 限制为单
                                            overflow = TextOverflow.Visible // 确保文本完全可见
                                        )
                                    }
                                }
                                
                                // 盈利显示，确保有足够空间
                                Box(
                                    modifier = Modifier//盈利宽度
                                        .width(profitBoxWidth) //动态调整宽度
                                        .padding(start = 1.dp, end = 10.dp),//盈利距离
                                    contentAlignment = Alignment.CenterEnd,
                                ) {
                                    Column(horizontalAlignment = Alignment.End) {
                                        if (showMacau || showHongKong) {
                                            // 根据选中签算盈利(平板用)
                                            val calculatedProfit = when {
                                                showMacau && showHongKong -> profit  // 两个标签都显示时用总盈利
                                                showMacau -> {
                                                    val macauTotal = stats?.macauAmount ?: 0 //澳门总注额
                                                    val macauAmount = data.macauAmount //澳门注额
                                                    val zongShu = macauTotal + macauSpecialTotal
                                                    val rebateAmount = calculateRebate(zongShu) //返水金额
                                                    macauTotal - rebateAmount - (macauAmount * specialNumberOdds).toInt() //澳门盈利
                                                }
                                                showHongKong -> {
                                                    val hkTotal = stats?.hongKongAmount ?: 0 //香港总注额   
                                                    val hkAmount = data.hongKongAmount //香港注额
                                                    val zongShu = hkTotal + hkSpecialTotal
                                                    val rebateAmount = calculateRebate(zongShu) //返水金额
                                                    hkTotal - rebateAmount - (hkAmount * specialNumberOdds).toInt() //香港盈利
                                                }
                                                else -> 0
                                            }
                                            val adjustedProfit = if (maxLoss.isNotEmpty()) { //不知道谁在用
                                                val currentAdjustment = stats?.adjustments?.get(number) ?: 0 //调整建议
                                                when {
                                                    showMacau && showHongKong -> {
                                                        val totalAmount = stats?.totalAmount ?: 0 //总注额  
                                                        val totalReduction = stats?.totalReduction ?: 0 //总减注
                                                        val adjustedTotal = totalAmount - totalReduction //调整后总注额
                                                        val zongshu = adjustedTotal + macauSpecialTotal + hkSpecialTotal
                                                        val rebateAmount = calculateRebate(zongshu) //返水金额
                                                        if (currentAdjustment > 0) {
                                                            (adjustedTotal - rebateAmount - ((data.totalAmount - currentAdjustment) * specialNumberOdds)).toInt()
                                                        } else {
                                                            (adjustedTotal - rebateAmount - (data.totalAmount * specialNumberOdds)).toInt()
                                                        }
                                                    }
                                                    showMacau -> {
                                                        val macauTotal = stats?.macauAmount ?: 0 //澳门总注额
                                                        val totalReduction = stats?.totalReduction ?: 0 //总减注
                                                        val adjustedTotal = macauTotal - totalReduction //调整后总注额
                                                        val zongshu = adjustedTotal + macauSpecialTotal
                                                        val rebateAmount = calculateRebate(zongshu) //返水金额
                                                        if (currentAdjustment > 0) {
                                                            (adjustedTotal - rebateAmount - ((data.macauAmount - currentAdjustment) * specialNumberOdds)).toInt()
                                                        } else {
                                                            (adjustedTotal - rebateAmount - (data.macauAmount * specialNumberOdds)).toInt()
                                                        }
                                                    }
                                                    showHongKong -> {
                                                        val hkTotal = stats?.hongKongAmount ?: 0 //香港总注额
                                                        val totalReduction = stats?.totalReduction ?: 0 //总减注
                                                        val adjustedTotal = hkTotal - totalReduction //调整后总注额
                                                        val zongshu = adjustedTotal + hkSpecialTotal    
                                                        val rebateAmount = calculateRebate(zongshu) //返水金额
                                                        if (currentAdjustment > 0) {
                                                            (adjustedTotal - rebateAmount - ((data.hongKongAmount - currentAdjustment) * specialNumberOdds)).toInt()
                                                        } else {
                                                            (adjustedTotal - rebateAmount - (data.hongKongAmount * specialNumberOdds)).toInt()
                                                        }
                                                    }
                                                    else -> 0
                                                }
                                            } else 0
                                            Text(
                                                text = if (maxLoss.isNotEmpty()) {
                                                    calculatedProfit.toString()
                                                } else {
                                                    "$calculatedProfit"
                                                },
                                                style = MaterialTheme.typography.bodyLarge,
                                                    fontSize = 23.sp,
                                                    fontWeight = FontWeight.Bold,
                                                color = if (calculatedProfit < 0 || adjustedProfit < 0)  // 现在可以使用 adjustedProfit
                                                    Color.Red
                                                else 
                                                    MaterialTheme.colorScheme.primary,
                                                modifier = Modifier.padding(bottom = if (maxLoss.isNotEmpty()) 2.dp else 1.dp) //盈利距离
                                            )
                                            
                                            if (maxLoss.isNotEmpty()) {
                                                Text(
                                                    text = "→$adjustedProfit",
                                                    style = MaterialTheme.typography.bodySmall,
                                                        fontSize = 15.sp,
                                                        fontWeight = FontWeight.Bold,
                                                    color = if (adjustedProfit < 0)
                                                        Color.Red
                                                    else
                                                        MaterialTheme.colorScheme.primary
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                // 手机模式保持原有的 LazyColumn 布局
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .animateContentSize(), 
                    state = rememberLazyListState(),
                    contentPadding = PaddingValues(vertical = 5.dp), 
                    verticalArrangement = Arrangement.spacedBy(5.dp) 
                ) {
                    items(
                        items = numberData.keys.sortedBy { number -> profits[number] ?: 0 },
                        key = { it },
                    ) { number ->
                        val data = numberData[number]!!
                        val profit = profits[number] ?: 0
                        val adjustment = if (maxLoss.isNotEmpty()) stats?.adjustments?.get(number) ?: 0 else 0
                        
                        // 检查是否应该高亮显示
                        val shouldHighlight = lotteryNumber.split(Regex("[,，\\s]+")).let { nums ->
                            when {
                                nums.size == 1 -> {
                                    val inputNum = nums[0]
                                    inputNum == formatNumberDisplay(number) || 
                                    inputNum == number.toString() ||
                                    number in selectedSpecialNumbers // 添加特殊选择检查
                                }
                                nums.size == 7 -> {
                                    val lastNum = nums[6]
                                    lastNum == formatNumberDisplay(number) ||
                                    lastNum == number.toString() ||
                                    number in selectedSpecialNumbers // 添加特殊选择检查
                                }
                                else -> number in selectedSpecialNumbers // 添加特殊选择检查
                            }
                        }
                        
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .animateContentSize() 
                                .padding(horizontal = 2.dp, vertical = 1.dp) //卡片距离
                                .shadow(
                                    elevation = if (shouldHighlight) 3.dp else 1.dp, //阴影高度
                                    shape = RoundedCornerShape(12.dp), //阴影圆角
                                    spotColor = when {
                                        adjustment > 0 -> Color(0xFFE30101).copy(alpha = 1f) //有调整时的阴影颜色
                                        profit < 0 -> Color(0xFFE30101).copy(alpha = 0.6f) //负盈利时使用更鲜明的红色
                                        else -> MaterialTheme.colorScheme.primary.copy(alpha = 0.5f) //默认阴影颜色
                                    }
                                ),
                            shape = RoundedCornerShape(12.dp), //卡片圆角
                            colors = CardDefaults.cardColors(
                                containerColor = if (shouldHighlight) 
                                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.8f) //卡片颜色
                                else 
                                    MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f) //卡片颜色
                            ),
                            onClick = { /* 点击处理 */ }
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 10.dp, vertical = 12.dp) //卡片内容距离
                                    .animateContentSize(
                                        animationSpec = spring(
                                            dampingRatio = Spring.DampingRatioMediumBouncy,
                                            stiffness = Spring.StiffnessLow
                                        )
                                    ),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 号码部分，调整宽度和高度
                                Box(
                                    modifier = Modifier
                                        .width(75.dp)  // 宽度保持不变
                                        // .height(44.dp)  // 添加固定高度 - 可以根据需要调整这个值
                                        .padding(start = 5.dp) //生肖距离
                                        .background(
                                            color = DataParser.getNumberColor(number).background,
                                            shape = RoundedCornerShape(8.dp) //生肖背景
                                        )
                                        .border(
                                            width = 1.dp,
                                            color = DataParser.getNumberColor(number).border,
                                            shape = RoundedCornerShape(8.dp), //生肖边框圆角
                                        )
                                        .padding(horizontal = 6.dp, vertical = 1.dp)
                                        .scale(
                                            animateFloatAsState(
                                                targetValue = if (shouldHighlight) 1.2f else 1f,
                                                animationSpec = spring(
                                                    dampingRatio = Spring.DampingRatioMediumBouncy,
                                                    stiffness = Spring.StiffnessLow
                                                )
                                            ).value
                                        ),
                                    contentAlignment = Alignment.Center  // 调整为居中对齐
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Start
                                    ) {
                                        Text(
                                            text = String.format("%02d", number),
                                            style = MaterialTheme.typography.titleLarge, //号码字体
                                            fontSize = 32.sp, //号码字体
                                            color = MaterialTheme.colorScheme.onSurface  // 使用完全不透明的颜色
                                        )
                                        Text(
                                            text = ZodiacUtils.getZodiacForNumber(number) ?: "",
                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                fontSize = 16.sp //生肖字体
                                            ),
                                            // color = DataParser.getNumberColor(number).text,
                                            color = MaterialTheme.colorScheme.onSurface, //生肖颜色
                                            modifier = Modifier.padding(start = 1.dp, top = 8.dp)
                                        )
                                    }
                                }
                                
                                // 注额和计数信息，调整间距
                                Column(
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(horizontal = 5.dp), //注额距离
                                    horizontalAlignment = Alignment.Start
                                ) {
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Text(
                                            text = "${data.totalAmount}",
                                            style = MaterialTheme.typography.bodyLarge,
                                            fontWeight = FontWeight.Bold,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Text(
                                            text = "(${data.count}注)",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                                            modifier = Modifier.padding(start = 1.dp) //注额距离
                                        )
                                    }
                                    
                                    // 澳门和香港注额
                                    if (showMacau || showHongKong) {
                                        Row(
                                            modifier = Modifier
                                                .padding(top = 2.dp) //注额距离
                                                .fillMaxWidth(), // 使用全宽
                                            horizontalArrangement = Arrangement.Start // 从左开始排列
                                        ) {
                                            if (showMacau && data.macauAmount > 0) {
                                                Text(
                                                    text = "澳:${data.macauAmount}",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                                                    modifier = Modifier.weight(1f, fill = false) // 权重为1但不强制填充
                                                )
                                            }
                                            if (showHongKong && data.hongKongAmount > 0) {
                                                Text(
                                                    text = "港:${data.hongKongAmount}",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                                                    modifier = Modifier.padding(start = if (showMacau && data.macauAmount > 0) 8.dp else 0.dp) // 只有在澳门显示时才添加左边距
                                                )
                                            }
                                        }
                                    }
                                    
                                    // 建议减注信 - 确保它总是显示在最下面
                                    if (maxLoss.isNotEmpty() && adjustment > 0) {
                                        Text(
                                            text = "建议减注: ${adjustment.toInt()}",
                                            style = MaterialTheme.typography.bodySmall,
                                            // fontSize = 15.sp,
                                            color = MaterialTheme.colorScheme.error,
                                            modifier = Modifier
                                                .padding(top = 2.dp)
                                                .fillMaxWidth(), // 确保文本有足够的宽度
                                                
                                            maxLines = 1, // 限制为单
                                            overflow = TextOverflow.Visible // 确保文本完全可见
                                        )
                                    }
                                }
                                
                                // 盈利显示，确保有足够空间
                                Box(
                                    modifier = Modifier
                                        .width(150.dp)
                                        .padding(start = 1.dp, end = 10.dp),
                                    contentAlignment = Alignment.CenterEnd
                                ) {
                                    Column(horizontalAlignment = Alignment.End) {
                                        if (showMacau || showHongKong) {
                                            // 根据选中签算盈利
                                            val calculatedProfit = when {
                                                showMacau && showHongKong -> profit  // 两个标签都显示时用总盈利
                                                showMacau -> {
                                                    val macauTotal = stats?.macauAmount ?: 0 //澳门总注额
                                                    val macauAmount = data.macauAmount //澳门注额
                                                    val zongshu = macauTotal + macauSpecialTotal
                                                    val rebateAmount = calculateRebate(zongshu)
                                                    macauTotal - rebateAmount - (macauAmount * specialNumberOdds).toInt() //澳门盈利
                                                }
                                                showHongKong -> {
                                                    val hkTotal = stats?.hongKongAmount ?: 0 //香港总注额
                                                    val hkAmount = data.hongKongAmount //香港注额
                                                    val zongshu = hkTotal + hkSpecialTotal
                                                    val rebateAmount = calculateRebate(zongshu) //返水金额
                                                    hkTotal - rebateAmount - (hkAmount * specialNumberOdds).toInt() //香港盈利
                                                }
                                                else -> 0
                                            }
                                            val adjustedProfit = if (maxLoss.isNotEmpty()) { //调整盈利     
                                                val currentAdjustment = stats?.adjustments?.get(number) ?: 0 //当前调整值
                                                when {
                                                    showMacau && showHongKong -> {
                                                        val totalAmount = stats?.totalAmount ?: 0 //总注额
                                                        val totalReduction = stats?.totalReduction ?: 0 //总减注
                                                        val adjustedTotal = totalAmount - totalReduction //调整后总注额
                                                        val zongshu = adjustedTotal + macauSpecialTotal + hkSpecialTotal
                                                        val rebateAmount = calculateRebate(zongshu) //返水金额
                                                        if (currentAdjustment > 0) {
                                                            (adjustedTotal - rebateAmount - ((data.totalAmount - currentAdjustment) * specialNumberOdds)).toInt()
                                                        } else {
                                                            (adjustedTotal - rebateAmount - (data.totalAmount * specialNumberOdds)).toInt()
                                                        }
                                                    }
                                                    showMacau -> {
                                                        val macauTotal = stats?.macauAmount ?: 0 //澳门总注额
                                                        val totalReduction = stats?.totalReduction ?: 0 //总减注
                                                        val adjustedTotal = macauTotal - totalReduction //调整后总注额
                                                        val zongshu = adjustedTotal + macauSpecialTotal
                                                        val rebateAmount = calculateRebate(zongshu) //返水金额
                                                        if (currentAdjustment > 0) {
                                                            (adjustedTotal - rebateAmount - ((data.macauAmount - currentAdjustment) * specialNumberOdds)).toInt()
                                                        } else {
                                                            (adjustedTotal - rebateAmount - (data.macauAmount * specialNumberOdds)).toInt()
                                                        }
                                                    }
                                                    showHongKong -> {
                                                        val hkTotal = stats?.hongKongAmount ?: 0 //香港总注额
                                                        val totalReduction = stats?.totalReduction ?: 0 //总减注
                                                        val adjustedTotal = hkTotal - totalReduction //调整后总注额
                                                        val zongshu = adjustedTotal + hkSpecialTotal
                                                        val rebateAmount = calculateRebate(zongshu) //返水金额
                                                        if (currentAdjustment > 0) {
                                                            (adjustedTotal - rebateAmount - ((data.hongKongAmount - currentAdjustment) * specialNumberOdds)).toInt()
                                                        } else {
                                                            (adjustedTotal - rebateAmount - (data.hongKongAmount * specialNumberOdds)).toInt()
                                                        }
                                                    }
                                                    else -> 0
                                                }
                                            } else 0
                                            Text(
                                                text = if (maxLoss.isNotEmpty()) {
                                                    calculatedProfit.toString()
                                                } else {
                                                    "$calculatedProfit"
                                                },
                                                style = MaterialTheme.typography.bodyLarge,
                                                    fontSize = 23.sp,
                                                    fontWeight = FontWeight.Bold,
                                                color = if (calculatedProfit < 0 || adjustedProfit < 0)  // 现在可以使用 adjustedProfit
                                                    Color.Red
                                                else 
                                                    MaterialTheme.colorScheme.primary,
                                                modifier = Modifier.padding(bottom = if (maxLoss.isNotEmpty()) 2.dp else 1.dp) //盈利距离
                                            )
                                            
                                            if (maxLoss.isNotEmpty()) {
                                                Text(
                                                    text = "→$adjustedProfit",
                                                    style = MaterialTheme.typography.bodySmall,
                                                        fontSize = 15.sp, //盈利字体
                                                        fontWeight = FontWeight.Bold,
                                                    color = if (adjustedProfit < 0)
                                                        Color.Red
                                                    else
                                                        MaterialTheme.colorScheme.primary
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp)) //数据列表距离

        // 底部按钮区域
        Box(
            modifier = Modifier //底部按钮区域
                .fillMaxWidth() // 填充整个宽度
                .padding(vertical = 0.dp) // 垂直内边距
        ) {
            // 清空按钮
            Button(
                onClick = { showConfirmDialog = true },
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .height(40.dp), // 按钮高度
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("清空数据")
            }

            // 状态提示文本 - 放在中间
            if (calculationStatus.isNotEmpty()) {
                Text(
                    text = calculationStatus,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(horizontal = 8.dp),
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.primary,
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            // 导出按钮
            Button(
                onClick = {
                    try {
                        when {
                            // 处理确定风险号码和最大亏损同时存在的情况
                            selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                                showNumberSelection = true  // 显示号码选择器
                            }
                            // 处理特殊关键字输入的情况
                            selectedSpecialNumbers.isNotEmpty() -> {
                                showNumberSelection = true
                            }
                            // 处理单个号码或7个号码的情况
                            lotteryNumber.isNotEmpty() -> {
                                val numbers = lotteryNumber.split(Regex("[,，\\s]+"))
                                    .filter { it.isNotEmpty() }
                                    .mapNotNull { numStr ->
                                        when {
                                            numStr.startsWith("0") && numStr.length == 2 -> numStr.substring(1).toIntOrNull()
                                            else -> numStr.toIntOrNull()
                                        }
                                    }
                                
                                if (numbers.size == 1 || numbers.size == 7) {
                                    ExportUtils.shareWinningData(
                                        context,
                                        lotteryNumber,
                                        showMacau,
                                        showHongKong
                                    )
                                } else {
                                    Toast.makeText(
                                        context, 
                                        "请输入1个特码或7个完整号码", 
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }
                            // 处理盈利调整的情况
                            maxLoss.isNotEmpty() && stats?.adjustments?.any { it.value > 0 } == true -> {
                                showNumberSelection = true
                            }
                            // 默认状态：显示所有号码的选择对话框
                            else -> {
                                showNumberSelection = true
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("ManagementScreen", "导出失败", e)
                        Toast.makeText(context, "导出失败：${e.message}", Toast.LENGTH_SHORT).show()
                    }
                },
                enabled = true,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .height(40.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("导出")
            }
        }

        // 号码选择对话框
        if (showNumberSelection) {
            AlertDialog(
                onDismissRequest = { showNumberSelection = false },
                title = { 
                    Text(
                        text = when {
                            selectedSpecialNumbers.isNotEmpty() -> "选择要导出的号码"
                            maxLoss.isNotEmpty() -> "选择需要分享的号码"
                            else -> "选择要导出的号码"
                        }
                    ) 
                },
                text = {
                    Column {
                        // 添加全选按钮
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = when {
                                    selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                                        val totalNumbers = (selectedSpecialNumbers + 
                                            (stats?.adjustments ?: emptyMap())
                                                .filter { it.value > 0 && it.key !in selectedSpecialNumbers }
                                                .keys).size
                                        "需要调整的号码：${totalNumbers}个"
                                    }
                                    selectedSpecialNumbers.isNotEmpty() -> 
                                        "符合条件的号码：${selectedSpecialNumbers.size}个"
                                    maxLoss.isNotEmpty() -> 
                                        "需要调整的号码：${(stats?.adjustments ?: emptyMap()).count { it.value > 0 }}个"
                                    else -> {
                                        // 计算非零注额号码数量
                                        val nonZeroCount = (1..49).count { number ->
                                            when {
                                                showMacau && showHongKong -> (numberData[number]?.totalAmount ?: 0) > 0
                                                showMacau -> (numberData[number]?.macauAmount ?: 0) > 0
                                                showHongKong -> (numberData[number]?.hongKongAmount ?: 0) > 0
                                                else -> false
                                            }
                                        }
                                        "可选号码：${nonZeroCount}个"
                                    }
                                },
                                style = MaterialTheme.typography.bodyMedium
                            )
                            TextButton(
                                onClick = {
                                    selectedNumbers = when {
                                        selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                                            if (selectedNumbers.size == (selectedSpecialNumbers + 
                                                (stats?.adjustments ?: emptyMap())
                                                    .filter { it.value > 0 && it.key !in selectedSpecialNumbers }
                                                    .keys).size) {
                                                emptySet()
                                            } else {
                                                (selectedSpecialNumbers + 
                                                    (stats?.adjustments ?: emptyMap())
                                                        .filter { it.value > 0 && it.key !in selectedSpecialNumbers }
                                                        .keys).toSet()
                                            }
                                        }
                                        selectedSpecialNumbers.isNotEmpty() -> {
                                            if (selectedNumbers.size == selectedSpecialNumbers.size) {
                                                emptySet()
                                            } else {
                                                selectedSpecialNumbers.toSet()
                                            }
                                        }
                                        maxLoss.isNotEmpty() -> {
                                            if (selectedNumbers.size == (stats?.adjustments ?: emptyMap()).count { it.value > 0 }) {
                                                emptySet()
                                            } else {
                                                (stats?.adjustments ?: emptyMap())
                                                    .filter { it.value > 0 }
                                                    .keys
                                                    .toSet()
                                            }
                                        }
                                        else -> {
                                            // 过滤掉注额为0的号码
                                            val nonZeroNumbers = (1..49).filter { number ->
                                                when {
                                                    showMacau && showHongKong -> (numberData[number]?.totalAmount ?: 0) > 0
                                                    showMacau -> (numberData[number]?.macauAmount ?: 0) > 0
                                                    showHongKong -> (numberData[number]?.hongKongAmount ?: 0) > 0
                                                    else -> false
                                                }
                                            }.toSet()
                                            
                                            if (selectedNumbers == nonZeroNumbers) {
                                                emptySet()
                                            } else {
                                                nonZeroNumbers
                                            }
                                        }
                                    }
                                }
                            ) {
                                Text(
                                    when {
                                        selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                                            val totalNumbers = (selectedSpecialNumbers + 
                                                (stats?.adjustments ?: emptyMap())
                                                    .filter { it.value > 0 && it.key !in selectedSpecialNumbers }
                                                    .keys).size
                                            if (selectedNumbers.size == totalNumbers) "取消全选" else "全选"
                                        }
                                        selectedSpecialNumbers.isNotEmpty() -> {
                                            if (selectedNumbers.size == selectedSpecialNumbers.size) "取消全选" else "全选"
                                        }
                                        maxLoss.isNotEmpty() -> {
                                            if (selectedNumbers.size == (stats?.adjustments ?: emptyMap()).count { it.value > 0 }) 
                                                "取消全选" else "全选"
                                        }
                                        else -> {
                                            // 计算非零注额号码的数量
                                            val nonZeroNumbers = (1..49).filter { number ->
                                                when {
                                                    showMacau && showHongKong -> (numberData[number]?.totalAmount ?: 0) > 0
                                                    showMacau -> (numberData[number]?.macauAmount ?: 0) > 0
                                                    showHongKong -> (numberData[number]?.hongKongAmount ?: 0) > 0
                                                    else -> false
                                                }
                                            }.toSet()
                                            
                                            if (selectedNumbers == nonZeroNumbers) "取消全选" else "全选"
                                        }
                                    }
                                )
                            }
                        }
                        
                        // 号码网格布局 - 自适应列数
                        val windowSize = LocalConfiguration.current
                        val dialogScreenWidth = windowSize.screenWidthDp.dp
                        
                        // 根据对话框宽度动态调整列数
                        val selectionGridColumns = when {
                            dialogScreenWidth < 400.dp -> 5 // 小屏幕显示5列
                            dialogScreenWidth < 600.dp -> 6 // 中等屏幕显示6列
                            else -> 7 // 大屏幕显示7列
                        }
                        
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(selectionGridColumns),
                            contentPadding = PaddingValues(horizontal = 1.dp, vertical = 1.dp),
                            horizontalArrangement = Arrangement.spacedBy(3.dp), // 增加水平间距
                            verticalArrangement = Arrangement.spacedBy(5.dp), // 增加垂直间距
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 1.dp) // 增加水平间距
                        ) {
                            items(49) { index ->
                                val number = index + 1
                                val formattedNumber = String.format("%02d", number)
                                val data = numberData[number]
                                
                                // 检查号码是否有效(注额>0)
                                val hasNonZeroAmount = when {
                                    showMacau && showHongKong -> (numberData[number]?.totalAmount ?: 0) > 0
                                    showMacau -> (numberData[number]?.macauAmount ?: 0) > 0
                                    showHongKong -> (numberData[number]?.hongKongAmount ?: 0) > 0
                                    else -> false
                                }
                                
                                // 检查号码是否应该可选
                                val isAvailable = when {
                                    selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                                        number in selectedSpecialNumbers || 
                                        ((stats?.adjustments?.get(number) ?: 0) > 0 && number !in selectedSpecialNumbers)
                                    }
                                    selectedSpecialNumbers.isNotEmpty() -> number in selectedSpecialNumbers
                                    maxLoss.isNotEmpty() -> (stats?.adjustments?.get(number) ?: 0) > 0
                                    else -> hasNonZeroAmount // 只有非零注额的号码可选
                                }
                                
                                val isSelected = number in selectedNumbers
                                
                                val backgroundColor = when {
                                    isSelected -> MaterialTheme.colorScheme.primaryContainer
                                    isAvailable -> MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.7f)
                                    else -> MaterialTheme.colorScheme.surface
                                }
                                
                                val numberColor = when {
                                    isSelected -> MaterialTheme.colorScheme.onPrimaryContainer
                                    number in DataParser.RED_NUMBERS -> Color.Red
                                    number in DataParser.BLUE_NUMBERS -> Color.Blue
                                    number in DataParser.GREEN_NUMBERS -> Color(0xFF006400) // 深绿色替代纯绿色
                                    else -> MaterialTheme.colorScheme.onSurface
                                }
                                
                                Box(
                                    modifier = Modifier
                                        .size(50.dp) // 增加卡片尺寸
                                        .background(
                                            color = backgroundColor,
                                            shape = CircleShape
                                        )
                                        .border(
                                            width = 1.dp, //
                                            color = if (isSelected) 
                                                MaterialTheme.colorScheme.error 
                                            else 
                                                MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                            shape = CircleShape
                                        )
                                        .clip(CircleShape)  // 确保点击效果也是圆形
                                        .clickable(
                                            enabled = isAvailable,
                                            onClick = {
                                                selectedNumbers = if (isSelected) {
                                                    selectedNumbers - number
                                                } else {
                                                    selectedNumbers + number
                                                }
                                            }
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        verticalArrangement = Arrangement.Center,
                                        modifier = Modifier.padding(1.dp) // 增加水平间距
                                    ) {
                                        // 号码
                                        Text(
                                            text = formattedNumber, // 使用带前导零的格式化号码
                                            color = numberColor,
                                            style = MaterialTheme.typography.bodyLarge.copy(
                                                fontSize = 16.sp, // 增加字体大小
                                                fontWeight = FontWeight.Bold // 设置为粗体
                                            ),
                                            modifier = Modifier.padding(bottom = 1.dp) // 增加水平间距
                                        )
                                        
                                        // 显示注额或减注值
                                        val amountText = when {
                                            !isAvailable -> "0"
                                            selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                                                if (number in selectedSpecialNumbers) {
                                                    // 确定风险号码显示全额
                                                    when {
                                                        showMacau && showHongKong -> "${numberData[number]?.totalAmount ?: 0}"
                                                        showMacau -> "${numberData[number]?.macauAmount ?: 0}"
                                                        showHongKong -> "${numberData[number]?.hongKongAmount ?: 0}"
                                                        else -> "0"
                                                    }
                                                } else {
                                                    // 其他号码显示建议减注额
                                                    "${stats?.adjustments?.get(number) ?: 0}"
                                                }
                                            }
                                            selectedSpecialNumbers.isNotEmpty() -> "${data?.totalAmount ?: 0}"
                                            maxLoss.isNotEmpty() -> "${stats?.adjustments?.get(number) ?: 0}"
                                            else -> "${data?.totalAmount ?: 0}"
                                        }
                                        
                                        // 只有当金额不为0时才使用突出颜色
                                        val amountColor = if (amountText != "0" && isAvailable) {
                                            if (number in selectedSpecialNumbers) {
                                                MaterialTheme.colorScheme.error  // 确定风险号码用红色
                                            } else {
                                                MaterialTheme.colorScheme.primary  // 其他号码用主色
                                            }
                                        } else {
                                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                                        }
                                        
                                        Text(
                                            text = amountText,
                                            style = MaterialTheme.typography.labelMedium.copy(
                                                fontSize = 10.sp,
                                                fontWeight = if (amountText != "0") FontWeight.Bold else FontWeight.Normal
                                            ),
                                            color = amountColor,
                                            modifier = Modifier.offset(y = (-1).dp)
                                        )
                                    }
                                }
                            }
                        }

                        // 显示选中号码数量和注额总和
                        if (selectedNumbers.isNotEmpty()) {
                            // 计算已选择号码的注额总和 - 使用正确的变量名
                            val selectedAmountSum = selectedNumbers.sumOf { number ->
                                val amountValue = when {
                                    number in selectedSpecialNumbers -> {
                                        // 使用正确的变量 numberData 而不是 numberDataMap
                                        when {
                                            showMacau && showHongKong -> numberData[number]?.totalAmount ?: 0
                                            showMacau -> numberData[number]?.macauAmount ?: 0
                                            showHongKong -> numberData[number]?.hongKongAmount ?: 0
                                            else -> 0
                                        }
                                    }
                                    maxLoss.isNotEmpty() -> {
                                        stats?.adjustments?.get(number) ?: 0
                                    }
                                    else -> {
                                        // 使用与其他地方相同的逻辑获取金额
                                        when {
                                            showMacau && showHongKong -> numberData[number]?.totalAmount ?: 0
                                            showMacau -> numberData[number]?.macauAmount ?: 0
                                            showHongKong -> numberData[number]?.hongKongAmount ?: 0
                                            else -> 0
                                        }
                                    }
                                }
                                amountValue
                            }
                            
                            Text(
                                text = "已选择 ${selectedNumbers.size} 个号码 总:${selectedAmountSum}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                },
                confirmButton = {
                    TextButton(
                        onClick = {
                            try {
                                when {
                                    selectedSpecialNumbers.isNotEmpty() && maxLoss.isNotEmpty() -> {
                                        // 合并确定风险号码和其他调整建议
                                        val allAdjustments = mutableMapOf<Int, Int>()
                                        
                                        // 添加确定风险号码（全额减注）
                                        selectedSpecialNumbers.forEach { number ->
                                            if (number in selectedNumbers) {  // 只处理选中的号码
                                                val amount = when {
                                                    showMacau && showHongKong -> numberData[number]?.totalAmount ?: 0
                                                    showMacau -> numberData[number]?.macauAmount ?: 0
                                                    showHongKong -> numberData[number]?.hongKongAmount ?: 0
                                                    else -> 0
                                                }
                                                if (amount > 0) {
                                                    allAdjustments[number] = amount
                                                }
                                            }
                                        }
                                        
                                        // 添加其他需要调整的号码
                                        (stats?.adjustments ?: emptyMap()).forEach { (number, adjustment) ->
                                            if (number !in selectedSpecialNumbers && number in selectedNumbers && adjustment > 0) {
                                                allAdjustments[number] = adjustment
                                            }
                                        }
                                        
                                        // 使用现有的导出功能
                                        ExportUtils.shareAdjustmentSuggestions(
                                            context,
                                            allAdjustments,
                                            showMacau,
                                            showHongKong
                                        )
                                    }
                                    selectedSpecialNumbers.isNotEmpty() -> {
                                        // 导出特殊关键字选中的号码数据
                                        val selectedData = selectedNumbers.associateWith { number ->
                                            numberData[number]?.totalAmount ?: 0
                                        }
                                        ExportUtils.shareSpecialNumbersData(
                                            context,
                                            selectedData,
                                            lotteryNumber, // 关键字
                                            showMacau,
                                            showHongKong
                                        )
                                    }
                                    maxLoss.isNotEmpty() -> {
                                        // 导出建议减注数据
                                        val filteredAdjustments = (stats?.adjustments ?: emptyMap())
                                            .filterKeys { it in selectedNumbers }
                                        ExportUtils.shareAdjustmentSuggestions(
                                            context,
                                            filteredAdjustments,
                                            showMacau,
                                            showHongKong
                                        )
                                    }
                                    else -> {
                                        // 导出选中号码的数据
                                        val selectedData = selectedNumbers.associateWith { number ->
                                            numberData[number]?.totalAmount ?: 0
                                        }
                                        ExportUtils.shareSpecialNumbersData(
                                            context,
                                            selectedData,
                                            "", // 空关键字
                                            showMacau,
                                            showHongKong
                                        )
                                    }
                                }
                            } catch (e: Exception) {
                                Toast.makeText(context, "导出失败：${e.message}", Toast.LENGTH_SHORT).show()
                            }
                            showNumberSelection = false
                            selectedNumbers = emptySet()
                        },
                        enabled = selectedNumbers.isNotEmpty()
                    ) {
                        Text("导出")
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = {
                            showNumberSelection = false
                            selectedNumbers = emptySet()
                        }
                    ) {
                        Text("取消")
                    }
                }
            )
        }

        // 添加确认对话框
        if (showConfirmDialog) {
            AlertDialog(
                onDismissRequest = { showConfirmDialog = false },
                title = { Text("确认清空") },
                text = { Text("确定要清空所有数据吗？此操作不可恢复。") },
                confirmButton = {
                    TextButton(
                        onClick = {
                            DatabaseUtils.clearDatabase(context) // 清空数据库
                            resetAllStates() // 重置所有状态
                            DatabaseUtils.resetDataCount(context) // 重置数据计数
                            
                            // // 清空所有数据，包括标识
                            // context.getSharedPreferences("input_state", Context.MODE_PRIVATE)
                            //     .edit()
                            //     .clear() // 完全清空所有数据，包括标识
                            //     .apply()
                            
                            Toast.makeText(context, "数据已清空", Toast.LENGTH_SHORT).show()
                            showConfirmDialog = false
                        },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("确认清空")
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = { showConfirmDialog = false }
                    ) {
                        Text("取消")
                    }
                },
                containerColor = MaterialTheme.colorScheme.surface,
                titleContentColor = MaterialTheme.colorScheme.onSurface,
                textContentColor = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
    
    // 修改返水设置弹窗
    if (showRebateDialog) {
        AlertDialog(
            onDismissRequest = { showRebateDialog = false },
            shape = MaterialTheme.shapes.large, // 使用更大的圆角
            containerColor = MaterialTheme.colorScheme.surface, // 使用主题表面色
            title = { 
                Text(
                    text = "返水设置",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.primary
                ) 
            },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 返水功能开关
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            // 根据开关状态动态显示文本
                            text = if (rebateEnabled) "返水功能已开启" else "返水功能已关闭",
                            style = MaterialTheme.typography.bodyLarge,
                            // 根据开关状态动态改变文本颜色
                            color = if (rebateEnabled) 
                                MaterialTheme.colorScheme.primary 
                            else 
                                MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Switch(
                            checked = rebateEnabled,
                            onCheckedChange = { rebateEnabled = it },
                            modifier = Modifier.scale(0.8f),  // 将开关缩小到原来的80%大小
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = MaterialTheme.colorScheme.primary,
                                // 加深开启状态的背景颜色，使用primary的不透明版本
                                checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                                uncheckedThumbColor = MaterialTheme.colorScheme.outline,
                                // 加深关闭状态的背景颜色
                                uncheckedTrackColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
                            )
                        )
                    }
                    
                    // 返水值输入
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = "返水值 (%)",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        
                        OutlinedTextField(
                            value = rebateValue,
                            onValueChange = { newValue ->
                                // 只允许输入数字和小数点
                                if (newValue.isEmpty() || newValue.matches(Regex("^\\d*\\.?\\d*$"))) {
                                    rebateValue = newValue
                                }
                            },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true,
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Number
                            ),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = MaterialTheme.colorScheme.primary,
                                unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                                cursorColor = MaterialTheme.colorScheme.primary
                            ),
                            trailingIcon = {
                                Text(
                                    text = "%",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        )
                    }
                    
                    // 说明文字
                    Text(
                        text = "返水值表示返还的百分比。例如，返水值设为1.0，表示1%，总数1万元，将返还100元。",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 保存返水设置
                        val rebateValueFloat = rebateValue.toFloatOrNull() ?: 0f
                        sharedPrefs.edit()
                            .putBoolean("rebate_enabled", rebateEnabled)
                            .putFloat("rebate_value", rebateValueFloat)
                            .apply()
                        showRebateDialog = false
                    },
                    shape = MaterialTheme.shapes.medium,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("确认")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showRebateDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 标签选择对话框
    if (showIdentifierSelection) {
        AlertDialog(
            onDismissRequest = { showIdentifierSelection = false },
            title = { Text("选择标签") },
            text = {
                Column {
                    // 添加全选按钮
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "可选标签：${availableIdentifiers.size}个",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        TextButton(
                            onClick = {
                                selectedIdentifiers = if (selectedIdentifiers.size == availableIdentifiers.size) {
                                    emptyList()
                                } else {
                                    availableIdentifiers
                                }
                            }
                        ) {
                            Text(
                                if (selectedIdentifiers.size == availableIdentifiers.size) "取消全选" else "全选"
                            )
                        }
                    }
                    
                    // 标签列表
                    LazyColumn {
                        items(availableIdentifiers) { identifier ->
                            val isSelected = identifier in selectedIdentifiers
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp)
                                    .clickable {
                                        selectedIdentifiers = if (isSelected) {
                                            selectedIdentifiers - identifier
                                        } else {
                                            selectedIdentifiers + identifier
                                        }
                                    },
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Checkbox(
                                    checked = isSelected,
                                    onCheckedChange = { checked ->
                                        selectedIdentifiers = if (checked) {
                                            selectedIdentifiers + identifier
                                        } else {
                                            selectedIdentifiers - identifier
                                        }
                                    }
                                )
                                Text(
                                    text = identifier,
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showIdentifierSelection = false }
                ) {
                    Text("确认")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        // 取消所有标签，而不是选择所有标签
                        selectedIdentifiers = emptyList()
                        showIdentifierSelection = false
                    }
                ) {
                    Text("重置")
                }
            }
        )
    }

    // 添加一个新的悬浮筛选按钮 - 放在整个布局的最外层
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.TopStart
    ) {
        // 获取屏幕尺寸
        val displayMetrics = LocalContext.current.resources.displayMetrics
        val screenWidth = remember { displayMetrics.widthPixels.toFloat() }
        val screenHeight = remember { displayMetrics.heightPixels.toFloat() }
        
        // 筛选按钮
        Box(
            modifier = Modifier
                .offset { IntOffset(buttonPosition.x.roundToInt(), buttonPosition.y.roundToInt()) }
                .size(30.dp)
                .shadow(
                    elevation = 6.dp,
                    shape = CircleShape,
                    spotColor = MaterialTheme.colorScheme.primary.copy(alpha = if(isDragging) 0.5f else 0.25f)
                )
                .background(
                    brush = androidx.compose.ui.graphics.Brush.radialGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.primary.copy(alpha = if(isDragging) 0.8f else 0.65f),
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = if(isDragging) 0.7f else 0.45f)
                        ),
                        radius = 60f 
                    ),
                    shape = CircleShape
                )
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f),
                    shape = CircleShape
                )
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { 
                            isDragging = true
                            // 震动反馈
                            VibrationUtils.vibrate(context)
                        },
                        onDragEnd = { 
                            isDragging = false
                            saveButtonPosition(buttonPosition)
                        },
                        onDragCancel = { isDragging = false }
                    ) { change, dragAmount ->
                        change.consume()
                        // 计算新位置
                        val newX = buttonPosition.x + dragAmount.x
                        val newY = buttonPosition.y + dragAmount.y
                        
                        // 获取当前最新的屏幕尺寸
                        val currentDisplayMetrics = context.resources.displayMetrics
                        val currentScreenWidth = currentDisplayMetrics.widthPixels.toFloat()
                        val currentScreenHeight = currentDisplayMetrics.heightPixels.toFloat()
                        
                        // 限制按钮在屏幕范围内，不能完全移出屏幕
                        buttonPosition = Offset(
                            x = newX.coerceIn(0f, currentScreenWidth - 30f),
                            y = newY.coerceIn(0f, currentScreenHeight - 30f)
                        )
                    }
                }
                .clickable(
                    enabled = !isDragging, // 拖动时禁用点击
                    interactionSource = remember { MutableInteractionSource() },
                    indication = rememberRipple(
                        bounded = true,
                        color = MaterialTheme.colorScheme.onPrimary
                    ),
                    onClick = { 
                        showIdentifierSelection = true 
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            // 根据选择的标签显示不同内容
            if (selectedIdentifiers.isEmpty()) {
                // 无选中标签时显示筛选图标
                Icon(
                    imageVector = Icons.Filled.FilterList,
                    contentDescription = "标签筛选",
                    modifier = Modifier.size(20.dp),
                    tint = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.9f)
                )
            } else {
                // 有选中标签时显示数字指示器
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .background(
                            brush = androidx.compose.ui.graphics.Brush.radialGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.error.copy(alpha = 0.95f),
                                    MaterialTheme.colorScheme.error.copy(alpha = 0.7f)
                                )
                            ),
                            shape = CircleShape
                        )
                        .border(
                            width = 0.5.dp,
                            color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.2f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "${selectedIdentifiers.size}",
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.onError,
                        fontSize = 12.sp
                    )
                }
            }
        }
    }

    // 在组件加载时只加载标签数据，按钮位置已经在初始化时处理
    LaunchedEffect(Unit) {
        // 载入标签数据
        loadAvailableIdentifiers()
    }

    // 获取当前配置
    val configuration = LocalConfiguration.current
    
    // 监听屏幕尺寸变化
    LaunchedEffect(configuration.screenWidthDp, configuration.screenHeightDp) {
        // 获取最新的屏幕尺寸
        val newDisplayMetrics = context.resources.displayMetrics
        val newScreenWidth = newDisplayMetrics.widthPixels.toFloat()
        val newScreenHeight = newDisplayMetrics.heightPixels.toFloat()
        
        // 重新加载保存的位置百分比
        val prefs = context.getSharedPreferences("button_prefs", Context.MODE_PRIVATE)
        val xPercent = prefs.getFloat("button_x_percent", 0.05f)
        val yPercent = prefs.getFloat("button_y_percent", 0.1f)
        
        // 根据新的屏幕尺寸计算新位置
        val newX = (xPercent * newScreenWidth).coerceIn(0f, newScreenWidth - 30f)
        val newY = (yPercent * newScreenHeight).coerceIn(0f, newScreenHeight - 30f)
        
        // 更新按钮位置
        buttonPosition = Offset(newX, newY)
    }
}
