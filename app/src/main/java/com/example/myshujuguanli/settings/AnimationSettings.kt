package com.example.myshujuguanli.settings

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.content.edit

object AnimationSettings {
    private const val PREFS_NAME = "animation_settings"

    // --- 动画类型枚举 ---
    enum class AnimationType {
        FLOWING_LIGHT,  // 流光
        BREATHING_LIGHT // 呼吸灯
    }

    // --- 输入框设置 ---
    private const val KEY_ANIMATION_ENABLED_INPUT = "animation_enabled_input"
    private const val KEY_ANIMATION_TYPE_INPUT = "animation_type_input"
    private const val KEY_ANIMATION_DURATION_INPUT = "animation_duration_input"
    private const val KEY_SNAKE_LENGTH_INPUT = "snake_length_input"
    private const val KEY_ANIMATION_ALPHA_INPUT = "animation_alpha_input"
    private const val KEY_ANIMATION_DIRECTION_INPUT = "animation_direction_input"

    // --- 输出框设置 ---
    private const val KEY_ANIMATION_ENABLED_OUTPUT = "animation_enabled_output"
    private const val KEY_ANIMATION_TYPE_OUTPUT = "animation_type_output"
    private const val KEY_ANIMATION_DURATION_OUTPUT = "animation_duration_output"
    private const val KEY_SNAKE_LENGTH_OUTPUT = "snake_length_output"
    private const val KEY_ANIMATION_ALPHA_OUTPUT = "animation_alpha_output"
    private const val KEY_ANIMATION_DIRECTION_OUTPUT = "animation_direction_output"


    // --- 默认值 ---
    private const val DEFAULT_ENABLED = true
    private val DEFAULT_ANIMATION_TYPE = AnimationType.FLOWING_LIGHT
    private const val DEFAULT_DURATION_MS = 7200
    private const val DEFAULT_SNAKE_LENGTH_PERCENT = 0.3f
    private const val DEFAULT_ANIMATION_ALPHA = 1.0f
    private const val DEFAULT_DIRECTION_INPUT_CLOCKWISE = true
    private const val DEFAULT_DIRECTION_OUTPUT_CLOCKWISE = false


    // --- 输入框状态变量 ---
    var animationEnabledInput by mutableStateOf(DEFAULT_ENABLED)
    var animationTypeInput by mutableStateOf(DEFAULT_ANIMATION_TYPE)
    var durationMillisInput by mutableStateOf(DEFAULT_DURATION_MS)
    var snakeLengthPercentInput by mutableStateOf(DEFAULT_SNAKE_LENGTH_PERCENT)
    var animationAlphaInput by mutableStateOf(DEFAULT_ANIMATION_ALPHA)
    var animationDirectionInput by mutableStateOf(DEFAULT_DIRECTION_INPUT_CLOCKWISE)

    // --- 输出框状态变量 ---
    var animationEnabledOutput by mutableStateOf(DEFAULT_ENABLED)
    var animationTypeOutput by mutableStateOf(DEFAULT_ANIMATION_TYPE)
    var durationMillisOutput by mutableStateOf(DEFAULT_DURATION_MS)
    var snakeLengthPercentOutput by mutableStateOf(DEFAULT_SNAKE_LENGTH_PERCENT)
    var animationAlphaOutput by mutableStateOf(DEFAULT_ANIMATION_ALPHA)
    var animationDirectionOutput by mutableStateOf(DEFAULT_DIRECTION_OUTPUT_CLOCKWISE)


    fun load(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        // 加载输入框设置
        animationEnabledInput = prefs.getBoolean(KEY_ANIMATION_ENABLED_INPUT, DEFAULT_ENABLED)
        animationTypeInput = AnimationType.valueOf(prefs.getString(KEY_ANIMATION_TYPE_INPUT, DEFAULT_ANIMATION_TYPE.name) ?: DEFAULT_ANIMATION_TYPE.name)
        durationMillisInput = prefs.getInt(KEY_ANIMATION_DURATION_INPUT, DEFAULT_DURATION_MS)
        snakeLengthPercentInput = prefs.getFloat(KEY_SNAKE_LENGTH_INPUT, DEFAULT_SNAKE_LENGTH_PERCENT)
        animationAlphaInput = prefs.getFloat(KEY_ANIMATION_ALPHA_INPUT, DEFAULT_ANIMATION_ALPHA)
        animationDirectionInput = prefs.getBoolean(KEY_ANIMATION_DIRECTION_INPUT, DEFAULT_DIRECTION_INPUT_CLOCKWISE)

        // 加载输出框设置
        animationEnabledOutput = prefs.getBoolean(KEY_ANIMATION_ENABLED_OUTPUT, DEFAULT_ENABLED)
        animationTypeOutput = AnimationType.valueOf(prefs.getString(KEY_ANIMATION_TYPE_OUTPUT, DEFAULT_ANIMATION_TYPE.name) ?: DEFAULT_ANIMATION_TYPE.name)
        durationMillisOutput = prefs.getInt(KEY_ANIMATION_DURATION_OUTPUT, DEFAULT_DURATION_MS)
        snakeLengthPercentOutput = prefs.getFloat(KEY_SNAKE_LENGTH_OUTPUT, DEFAULT_SNAKE_LENGTH_PERCENT)
        animationAlphaOutput = prefs.getFloat(KEY_ANIMATION_ALPHA_OUTPUT, DEFAULT_ANIMATION_ALPHA)
        animationDirectionOutput = prefs.getBoolean(KEY_ANIMATION_DIRECTION_OUTPUT, DEFAULT_DIRECTION_OUTPUT_CLOCKWISE)
    }

    fun save(context: Context) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE).edit {
            // 保存输入框设置
            putBoolean(KEY_ANIMATION_ENABLED_INPUT, animationEnabledInput)
            putString(KEY_ANIMATION_TYPE_INPUT, animationTypeInput.name)
            putInt(KEY_ANIMATION_DURATION_INPUT, durationMillisInput)
            putFloat(KEY_SNAKE_LENGTH_INPUT, snakeLengthPercentInput)
            putFloat(KEY_ANIMATION_ALPHA_INPUT, animationAlphaInput)
            putBoolean(KEY_ANIMATION_DIRECTION_INPUT, animationDirectionInput)

            // 保存输出框设置
            putBoolean(KEY_ANIMATION_ENABLED_OUTPUT, animationEnabledOutput)
            putString(KEY_ANIMATION_TYPE_OUTPUT, animationTypeOutput.name)
            putInt(KEY_ANIMATION_DURATION_OUTPUT, durationMillisOutput)
            putFloat(KEY_SNAKE_LENGTH_OUTPUT, snakeLengthPercentOutput)
            putFloat(KEY_ANIMATION_ALPHA_OUTPUT, animationAlphaOutput)
            putBoolean(KEY_ANIMATION_DIRECTION_OUTPUT, animationDirectionOutput)
        }
    }
} 