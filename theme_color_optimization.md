# 主题颜色优化说明

## 问题分析

从您提供的截图可以看出，紫色主题的次颜色（primaryContainer）与主色（primary）的对比度不够，导致视觉层次不清晰，与其他主题的搭配风格不一致。

## 优化前后对比

### 紫色主题
**优化前:**
- primary: `#7B1FA2` (深紫色)
- primaryContainer: `#E1BEE7` (非常浅的紫色)
- 问题: 对比度过低，次颜色过于浅淡

**优化后:**
- primary: `#7B1FA2` (深紫色，保持不变)
- primaryContainer: `#CE93D8` (中等深度的紫色)
- 改进: 提高了对比度，保持了紫色色调的一致性

### 青色主题
**优化前:**
- primary: `#572FA2` (深青紫色)
- primaryContainer: `#8758D9` (比主色更亮的紫色)
- 问题: 违反了 Material Design 原则，容器色不应比主色更亮

**优化后:**
- primary: `#5E35B1` (调整为更标准的深紫色)
- primaryContainer: `#D1C4E9` (浅紫色容器)
- 改进: 符合 Material Design 规范，层次更清晰

## 设计原则

### Material Design 颜色系统
1. **Primary**: 应用的主要品牌色
2. **Primary Container**: 主色的浅色变体，用于容器背景
3. **对比度要求**: Primary Container 应该比 Primary 更浅，但要保持足够的对比度

### 一致性原则
所有主题都遵循相同的颜色深浅关系：
- **蓝色主题**: `#1976D2` → `#BBDEFB` (深蓝 → 浅蓝)
- **绿色主题**: `#388E3C` → `#C8E6C9` (深绿 → 浅绿)
- **紫色主题**: `#7B1FA2` → `#CE93D8` (深紫 → 中紫)
- **青色主题**: `#5E35B1` → `#D1C4E9` (深紫 → 浅紫)
- **灰色主题**: `#546E7A` → `#CFD8DC` (深灰 → 浅灰)
- **橙色主题**: `#F57C00` → `#FFE0B2` (深橙 → 浅橙)

## 视觉效果改进

### 优化后的效果
1. **更好的层次感**: 主色和容器色有明显的深浅对比
2. **一致的视觉风格**: 所有主题都遵循相同的颜色深浅比例
3. **更好的可读性**: 文字在容器背景上有更好的对比度
4. **符合设计规范**: 遵循 Material Design 3 的颜色系统

### 应用场景
- **Primary**: 用于按钮、标题栏等主要UI元素
- **Primary Container**: 用于卡片背景、输入框背景等容器元素

## 测试建议

1. **切换主题测试**: 在不同主题间切换，观察颜色过渡的一致性
2. **可读性测试**: 确保文字在各种背景上都有良好的可读性
3. **视觉层次测试**: 确认主要元素和次要元素有清晰的视觉区分

这次优化确保了所有主题都有一致的视觉风格和良好的用户体验。
