# 随机主题功能测试说明

## 修复内容

### 问题分析
从日志可以看出，之前的问题是：
1. 冷启动时虽然清除了内存缓存，但仍然从 SharedPreferences 中恢复了之前保存的随机主题索引
2. 没有真正生成新的随机主题

### 修复方案

#### 1. 新增强制清除方法
在 `ThemeManager` 中新增了 `clearRandomThemeCacheAndForceNew()` 方法：
- 清除内存缓存 (`cachedRandomThemeIndex = null`)
- 清除 SharedPreferences 中的随机主题索引存储
- 强制在下次加载时生成全新的随机主题

#### 2. 修改冷启动逻辑
在 `MainActivity` 中：
- 冷启动时调用 `clearRandomThemeCacheAndForceNew()` 而不是 `clearRandomThemeCache()`
- 确保冷启动时会生成全新的随机主题

## 预期行为

### 启用随机主题时
- 生成一个新的随机主题索引
- 保存到内存缓存和 SharedPreferences
- 应用该主题

### 热启动时
- 从内存缓存或 SharedPreferences 恢复之前的随机主题索引
- 保持主题不变

### 冷启动时
- 清除内存缓存和 SharedPreferences 存储
- 生成全新的随机主题索引
- 应用新的随机主题

## 测试步骤

1. **启用随机主题**
   - 进入设置，选择随机主题
   - 观察生成的主题

2. **热启动测试**
   - 按 Home 键退到后台
   - 重新打开应用
   - 确认主题保持不变

3. **冷启动测试**
   - 完全关闭应用（从最近任务中移除）
   - 重新启动应用
   - 确认生成了新的随机主题

## 日志关键信息

### 启用随机主题时
```
ThemeManager: 启用随机主题，索引: X, 主题: XXX主题
```

### 冷启动时
```
MainActivity: 冷启动检测到，已强制清除随机主题缓存和存储
ThemeManager: 强制清除随机主题缓存和存储，之前的索引: X，将生成新的随机主题
ThemeManager: 生成新的随机主题索引: Y
ThemeManager: 使用随机主题，索引: Y, 主题: YYY主题
```

### 热启动时
```
ThemeManager: 恢复保存的随机主题索引: X
ThemeManager: 使用随机主题，索引: X, 主题: XXX主题
```
